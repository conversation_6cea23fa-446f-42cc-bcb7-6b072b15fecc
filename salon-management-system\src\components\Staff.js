import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>rid,
  <PERSON>,
  CardContent,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Chip,
  IconButton,
  Avatar,
  List,
  ListItem,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Star as StarIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';

const Staff = () => {
  const [staff, setStaff] = useState([
    {
      id: 1,
      name: '<PERSON>',
      position: 'Senior Stylist',
      email: '<EMAIL>',
      phone: '(*************',
      specialties: ['Hair Cut', 'Hair Color', 'Styling'],
      experience: '8 years',
      rating: 4.9,
      status: 'active',
      schedule: 'Mon-Fri 9AM-6PM',
      hireDate: '2020-03-15',
      salary: '₹45,65,000',
    },
    {
      id: 2,
      name: '<PERSON>',
      position: '<PERSON>',
      email: '<EMAIL>',
      phone: '(*************',
      specialties: ['Men\'s Cuts', '<PERSON> Trim', 'Shaving'],
      experience: '5 years',
      rating: 4.7,
      status: 'active',
      schedule: 'Tue-Sat 10AM-7PM',
      hireDate: '2021-06-20',
      salary: '₹37,35,000',
    },
    {
      id: 3,
      name: 'Mike Johnson',
      position: 'Hair Stylist',
      email: '<EMAIL>',
      phone: '(*************',
      specialties: ['Hair Cut', 'Styling', 'Wedding Hair'],
      experience: '6 years',
      rating: 4.8,
      status: 'active',
      schedule: 'Wed-Sun 9AM-5PM',
      hireDate: '2020-11-10',
      salary: '₹39,84,000',
    },
    {
      id: 4,
      name: 'Sarah Davis',
      position: 'Nail Technician',
      email: '<EMAIL>',
      phone: '(*************',
      specialties: ['Manicure', 'Pedicure', 'Nail Art'],
      experience: '4 years',
      rating: 4.6,
      status: 'active',
      schedule: 'Mon-Fri 10AM-6PM',
      hireDate: '2022-01-15',
      salary: '₹31,54,000',
    },
    {
      id: 5,
      name: 'Lisa Anderson',
      position: 'Esthetician',
      email: '<EMAIL>',
      phone: '(*************',
      specialties: ['Facial', 'Skincare', 'Massage'],
      experience: '7 years',
      rating: 4.9,
      status: 'on-leave',
      schedule: 'Mon-Thu 9AM-5PM',
      hireDate: '2019-08-05',
      salary: '₹34,86,000',
    },
  ]);

  const [open, setOpen] = useState(false);
  const [editingStaff, setEditingStaff] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    position: '',
    email: '',
    phone: '',
    specialties: [],
    experience: '',
    schedule: '',
    salary: '',
    status: 'active',
  });

  const positions = [
    'Senior Stylist',
    'Hair Stylist',
    'Barber',
    'Nail Technician',
    'Esthetician',
    'Massage Therapist',
    'Receptionist',
    'Manager',
  ];

  const specialtyOptions = [
    'Hair Cut',
    'Hair Color',
    'Styling',
    'Men\'s Cuts',
    'Beard Trim',
    'Shaving',
    'Manicure',
    'Pedicure',
    'Nail Art',
    'Facial',
    'Skincare',
    'Massage',
    'Wedding Hair',
    'Special Events',
  ];

  const statusOptions = [
    { value: 'active', label: 'Active', color: 'success' },
    { value: 'on-leave', label: 'On Leave', color: 'warning' },
    { value: 'inactive', label: 'Inactive', color: 'error' },
  ];

  const getStatusColor = (status) => {
    const statusObj = statusOptions.find(s => s.value === status);
    return statusObj ? statusObj.color : 'default';
  };

  const getStatusLabel = (status) => {
    const statusObj = statusOptions.find(s => s.value === status);
    return statusObj ? statusObj.label : status;
  };

  const getInitials = (name) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const handleOpen = (staffMember = null) => {
    if (staffMember) {
      setEditingStaff(staffMember);
      setFormData({
        name: staffMember.name,
        position: staffMember.position,
        email: staffMember.email,
        phone: staffMember.phone,
        specialties: staffMember.specialties,
        experience: staffMember.experience,
        schedule: staffMember.schedule,
        salary: staffMember.salary,
        status: staffMember.status,
      });
    } else {
      setEditingStaff(null);
      setFormData({
        name: '',
        position: '',
        email: '',
        phone: '',
        specialties: [],
        experience: '',
        schedule: '',
        salary: '',
        status: 'active',
      });
    }
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setEditingStaff(null);
  };

  const handleSave = () => {
    const staffData = {
      ...formData,
      rating: editingStaff ? editingStaff.rating : 4.5,
      hireDate: editingStaff ? editingStaff.hireDate : new Date().toISOString().split('T')[0],
    };

    if (editingStaff) {
      setStaff(staff.map(member => 
        member.id === editingStaff.id 
          ? { ...staffData, id: editingStaff.id }
          : member
      ));
    } else {
      const newStaff = {
        ...staffData,
        id: Math.max(...staff.map(s => s.id)) + 1,
      };
      setStaff([...staff, newStaff]);
    }
    handleClose();
  };

  const handleDelete = (id) => {
    setStaff(staff.filter(member => member.id !== id));
  };

  const handleInputChange = (field, value) => {
    setFormData({ ...formData, [field]: value });
  };

  const handleSpecialtyChange = (event) => {
    const value = event.target.value;
    setFormData({ ...formData, specialties: typeof value === 'string' ? value.split(',') : value });
  };

  const staffStats = {
    total: staff.length,
    active: staff.filter(s => s.status === 'active').length,
    avgRating: (staff.reduce((sum, member) => sum + member.rating, 0) / staff.length).toFixed(1),
  };

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Staff Management</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpen()}
        >
          Add Staff Member
        </Button>
      </Box>

      {/* Staff Stats */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Staff
              </Typography>
              <Typography variant="h4">
                {staffStats.total}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Active Staff
              </Typography>
              <Typography variant="h4" color="success.main">
                {staffStats.active}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Average Rating
              </Typography>
              <Typography variant="h4" color="primary.main">
                {staffStats.avgRating} ⭐
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Staff Grid */}
      <Grid container spacing={3}>
        {staff.map((member) => (
          <Grid item xs={12} md={6} lg={4} key={member.id}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardContent sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar
                    sx={{ width: 60, height: 60, mr: 2, bgcolor: 'primary.main' }}
                  >
                    {getInitials(member.name)}
                  </Avatar>
                  <Box>
                    <Typography variant="h6" component="div">
                      {member.name}
                    </Typography>
                    <Typography color="text.secondary" variant="body2">
                      {member.position}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mt: 0.5 }}>
                      <StarIcon sx={{ fontSize: 16, color: '#ffc107' }} />
                      <Typography variant="body2">
                        {member.rating}
                      </Typography>
                    </Box>
                  </Box>
                </Box>

                <Chip
                  label={getStatusLabel(member.status)}
                  color={getStatusColor(member.status)}
                  size="small"
                  sx={{ mb: 2 }}
                />

                <List dense>
                  <ListItem disablePadding>
                    <EmailIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                    <ListItemText 
                      primary={member.email}
                      primaryTypographyProps={{ variant: 'body2' }}
                    />
                  </ListItem>
                  <ListItem disablePadding>
                    <PhoneIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                    <ListItemText 
                      primary={member.phone}
                      primaryTypographyProps={{ variant: 'body2' }}
                    />
                  </ListItem>
                  <ListItem disablePadding>
                    <ScheduleIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                    <ListItemText 
                      primary={member.schedule}
                      primaryTypographyProps={{ variant: 'body2' }}
                    />
                  </ListItem>
                </List>

                <Divider sx={{ my: 1 }} />

                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Specialties:
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
                  {member.specialties.map((specialty, index) => (
                    <Chip
                      key={index}
                      label={specialty}
                      size="small"
                      variant="outlined"
                    />
                  ))}
                </Box>

                <Typography variant="body2" color="text.secondary">
                  Experience: {member.experience}
                </Typography>
                <Typography variant="body2" color="success.main">
                  Salary: {member.salary}
                </Typography>
              </CardContent>
              
              <CardActions>
                <IconButton
                  size="small"
                  onClick={() => handleOpen(member)}
                  color="primary"
                >
                  <EditIcon />
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => handleDelete(member.id)}
                  color="error"
                >
                  <DeleteIcon />
                </IconButton>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Add/Edit Dialog */}
      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingStaff ? 'Edit Staff Member' : 'Add New Staff Member'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Full Name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                select
                label="Position"
                value={formData.position}
                onChange={(e) => handleInputChange('position', e.target.value)}
                required
              >
                {positions.map((position) => (
                  <MenuItem key={position} value={position}>
                    {position}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Experience"
                value={formData.experience}
                onChange={(e) => handleInputChange('experience', e.target.value)}
                placeholder="e.g., 5 years"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Salary"
                value={formData.salary}
                onChange={(e) => handleInputChange('salary', e.target.value)}
                placeholder="e.g., ₹37,35,000"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Schedule"
                value={formData.schedule}
                onChange={(e) => handleInputChange('schedule', e.target.value)}
                placeholder="e.g., Mon-Fri 9AM-6PM"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                select
                label="Specialties"
                multiple
                value={formData.specialties}
                onChange={handleSpecialtyChange}
                SelectProps={{
                  multiple: true,
                  renderValue: (selected) => selected.join(', '),
                }}
              >
                {specialtyOptions.map((specialty) => (
                  <MenuItem key={specialty} value={specialty}>
                    {specialty}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                select
                label="Status"
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value)}
              >
                {statusOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button onClick={handleSave} variant="contained">
            {editingStaff ? 'Update' : 'Add Staff Member'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Staff;
