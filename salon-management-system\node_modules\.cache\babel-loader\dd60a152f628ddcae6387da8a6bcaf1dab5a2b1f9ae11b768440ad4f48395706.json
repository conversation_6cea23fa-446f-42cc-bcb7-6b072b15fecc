{"ast": null, "code": "var _jsxFileName = \"D:\\\\Project\\\\salon-management-system\\\\src\\\\components\\\\Reports.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Grid, Card, CardContent, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TextField, MenuItem, Button, Chip, LinearProgress } from '@mui/material';\nimport { TrendingUp, TrendingDown, AttachMoney, People, Event, Star, Download as DownloadIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Reports = () => {\n  _s();\n  const [dateRange, setDateRange] = useState('thisMonth');\n  const [reportType, setReportType] = useState('overview');\n\n  // Mock data for reports (amounts in INR)\n  const overviewData = {\n    revenue: {\n      current: 1279860,\n      previous: 1149550,\n      change: 11.3\n    },\n    appointments: {\n      current: 186,\n      previous: 172,\n      change: 8.1\n    },\n    customers: {\n      current: 142,\n      previous: 138,\n      change: 2.9\n    },\n    avgRating: {\n      current: 4.7,\n      previous: 4.6,\n      change: 2.2\n    }\n  };\n  const topServices = [{\n    name: 'Hair Cut & Style',\n    bookings: 45,\n    revenue: 317475,\n    percentage: 24.8\n  }, {\n    name: 'Hair Color',\n    bookings: 28,\n    revenue: 348600,\n    percentage: 27.2\n  }, {\n    name: 'Manicure',\n    bookings: 38,\n    revenue: 141930,\n    percentage: 11.1\n  }, {\n    name: 'Facial Treatment',\n    bookings: 22,\n    revenue: 219120,\n    percentage: 17.1\n  }, {\n    name: 'Pedicure',\n    bookings: 31,\n    revenue: 141515,\n    percentage: 11.1\n  }];\n  const staffPerformance = [{\n    name: 'Emma Wilson',\n    appointments: 42,\n    revenue: 383460,\n    rating: 4.9,\n    efficiency: 95\n  }, {\n    name: 'John Smith',\n    appointments: 38,\n    revenue: 236550,\n    rating: 4.7,\n    efficiency: 92\n  }, {\n    name: 'Mike Johnson',\n    appointments: 35,\n    revenue: 325360,\n    rating: 4.8,\n    efficiency: 88\n  }, {\n    name: 'Sarah Davis',\n    appointments: 41,\n    revenue: 204180,\n    rating: 4.6,\n    efficiency: 90\n  }, {\n    name: 'Lisa Anderson',\n    appointments: 30,\n    revenue: 130310,\n    rating: 4.9,\n    efficiency: 85\n  }];\n  const monthlyTrends = [{\n    month: 'Jan',\n    revenue: 1037500,\n    appointments: 145\n  }, {\n    month: 'Feb',\n    revenue: 1095600,\n    appointments: 152\n  }, {\n    month: 'Mar',\n    revenue: 1170300,\n    appointments: 168\n  }, {\n    month: 'Apr',\n    revenue: 1149550,\n    appointments: 172\n  }, {\n    month: 'May',\n    revenue: 1279860,\n    appointments: 186\n  }];\n  const customerInsights = [{\n    metric: 'New Customers',\n    value: 28,\n    change: 12.5\n  }, {\n    metric: 'Returning Customers',\n    value: 114,\n    change: -2.1\n  }, {\n    metric: 'Customer Retention Rate',\n    value: 78,\n    change: 3.2\n  }, {\n    metric: 'Average Visit Value',\n    value: 82.9,\n    change: 5.8\n  }];\n  const dateRangeOptions = [{\n    value: 'today',\n    label: 'Today'\n  }, {\n    value: 'thisWeek',\n    label: 'This Week'\n  }, {\n    value: 'thisMonth',\n    label: 'This Month'\n  }, {\n    value: 'lastMonth',\n    label: 'Last Month'\n  }, {\n    value: 'thisYear',\n    label: 'This Year'\n  }, {\n    value: 'custom',\n    label: 'Custom Range'\n  }];\n  const reportTypeOptions = [{\n    value: 'overview',\n    label: 'Overview'\n  }, {\n    value: 'revenue',\n    label: 'Revenue Analysis'\n  }, {\n    value: 'staff',\n    label: 'Staff Performance'\n  }, {\n    value: 'services',\n    label: 'Service Analysis'\n  }, {\n    value: 'customers',\n    label: 'Customer Insights'\n  }];\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 2\n    }).format(amount);\n  };\n  const formatPercentage = value => {\n    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;\n  };\n  const getChangeIcon = change => {\n    return change > 0 ? /*#__PURE__*/_jsxDEV(TrendingUp, {\n      color: \"success\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 25\n    }, this) : /*#__PURE__*/_jsxDEV(TrendingDown, {\n      color: \"error\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 58\n    }, this);\n  };\n  const getChangeColor = change => {\n    return change > 0 ? 'success.main' : 'error.main';\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      flexGrow: 1,\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        children: \"Reports & Analytics\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 22\n        }, this),\n        children: \"Export Report\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          select: true,\n          label: \"Date Range\",\n          value: dateRange,\n          onChange: e => setDateRange(e.target.value),\n          children: dateRangeOptions.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: option.value,\n            children: option.label\n          }, option.value, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          select: true,\n          label: \"Report Type\",\n          value: reportType,\n          onChange: e => setReportType(e.target.value),\n          children: reportTypeOptions.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: option.value,\n            children: option.label\n          }, option.value, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"Revenue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  children: formatCurrency(overviewData.revenue.current)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mt: 1\n                  },\n                  children: [getChangeIcon(overviewData.revenue.change), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: getChangeColor(overviewData.revenue.change),\n                    sx: {\n                      ml: 0.5\n                    },\n                    children: formatPercentage(overviewData.revenue.change)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(AttachMoney, {\n                color: \"primary\",\n                sx: {\n                  fontSize: 40\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"Appointments\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  children: overviewData.appointments.current\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mt: 1\n                  },\n                  children: [getChangeIcon(overviewData.appointments.change), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: getChangeColor(overviewData.appointments.change),\n                    sx: {\n                      ml: 0.5\n                    },\n                    children: formatPercentage(overviewData.appointments.change)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Event, {\n                color: \"primary\",\n                sx: {\n                  fontSize: 40\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"Customers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  children: overviewData.customers.current\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mt: 1\n                  },\n                  children: [getChangeIcon(overviewData.customers.change), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: getChangeColor(overviewData.customers.change),\n                    sx: {\n                      ml: 0.5\n                    },\n                    children: formatPercentage(overviewData.customers.change)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(People, {\n                color: \"primary\",\n                sx: {\n                  fontSize: 40\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"textSecondary\",\n                  gutterBottom: true,\n                  children: \"Avg Rating\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  children: overviewData.avgRating.current\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mt: 1\n                  },\n                  children: [getChangeIcon(overviewData.avgRating.change), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: getChangeColor(overviewData.avgRating.change),\n                    sx: {\n                      ml: 0.5\n                    },\n                    children: formatPercentage(overviewData.avgRating.change)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Star, {\n                color: \"primary\",\n                sx: {\n                  fontSize: 40\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Top Services\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Service\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: \"Bookings\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: \"Revenue\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: \"Share\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: topServices.map((service, index) => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: service.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: service.bookings\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: formatCurrency(service.revenue)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: `${service.percentage}%`,\n                      size: \"small\",\n                      color: \"primary\",\n                      variant: \"outlined\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 313,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Staff Performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Staff Member\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: \"Appointments\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: \"Revenue\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: \"Rating\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: staffPerformance.map((staff, index) => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: staff.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 349,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n                        variant: \"determinate\",\n                        value: staff.efficiency,\n                        sx: {\n                          mt: 0.5,\n                          height: 4\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 350,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 348,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: staff.appointments\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: formatCurrency(staff.revenue)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'flex-end'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Star, {\n                        sx: {\n                          fontSize: 16,\n                          color: '#ffc107',\n                          mr: 0.5\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 361,\n                        columnNumber: 27\n                      }, this), staff.rating]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 360,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Monthly Trends\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Month\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: \"Revenue\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: \"Appointments\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: \"Avg per Appointment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: monthlyTrends.map((month, index) => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: month.month\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: formatCurrency(month.revenue)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: month.appointments\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: formatCurrency(month.revenue / month.appointments)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Customer Insights\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this), customerInsights.map((insight, index) => /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: insight.metric\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    mr: 1\n                  },\n                  children: insight.metric.includes('Rate') || insight.metric.includes('Value') ? `${insight.value}${insight.metric.includes('Rate') ? '%' : ''}` : insight.value\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: getChangeColor(insight.change),\n                  children: formatPercentage(insight.change)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this), index < customerInsights.length - 1 && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                borderBottom: 1,\n                borderColor: 'divider',\n                mt: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 57\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 129,\n    columnNumber: 5\n  }, this);\n};\n_s(Reports, \"3Tx7qAZwx76rkJogFRKE9oLzb8U=\");\n_c = Reports;\nexport default Reports;\nvar _c;\n$RefreshReg$(_c, \"Reports\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "TextField", "MenuItem", "<PERSON><PERSON>", "Chip", "LinearProgress", "TrendingUp", "TrendingDown", "AttachMoney", "People", "Event", "Star", "Download", "DownloadIcon", "jsxDEV", "_jsxDEV", "Reports", "_s", "date<PERSON><PERSON><PERSON>", "setDateRange", "reportType", "setReportType", "overviewData", "revenue", "current", "previous", "change", "appointments", "customers", "avgRating", "topServices", "name", "bookings", "percentage", "staffPerformance", "rating", "efficiency", "monthlyTrends", "month", "customerInsights", "metric", "value", "dateRangeOptions", "label", "reportTypeOptions", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "formatPercentage", "toFixed", "getChangeIcon", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getChangeColor", "sx", "flexGrow", "p", "children", "display", "justifyContent", "alignItems", "mb", "variant", "startIcon", "container", "spacing", "item", "xs", "sm", "md", "fullWidth", "select", "onChange", "e", "target", "map", "option", "gutterBottom", "mt", "ml", "fontSize", "size", "align", "service", "index", "staff", "height", "mr", "insight", "includes", "length", "borderBottom", "borderColor", "_c", "$RefreshReg$"], "sources": ["D:/Project/salon-management-system/src/components/Reports.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Grid,\n  Card,\n  CardContent,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  TextField,\n  MenuItem,\n  Button,\n  Chip,\n  LinearProgress,\n} from '@mui/material';\nimport {\n  TrendingUp,\n  TrendingDown,\n  AttachMoney,\n  People,\n  Event,\n  Star,\n  Download as DownloadIcon,\n} from '@mui/icons-material';\n\nconst Reports = () => {\n  const [dateRange, setDateRange] = useState('thisMonth');\n  const [reportType, setReportType] = useState('overview');\n\n  // Mock data for reports (amounts in INR)\n  const overviewData = {\n    revenue: {\n      current: 1279860,\n      previous: 1149550,\n      change: 11.3,\n    },\n    appointments: {\n      current: 186,\n      previous: 172,\n      change: 8.1,\n    },\n    customers: {\n      current: 142,\n      previous: 138,\n      change: 2.9,\n    },\n    avgRating: {\n      current: 4.7,\n      previous: 4.6,\n      change: 2.2,\n    },\n  };\n\n  const topServices = [\n    { name: 'Hair Cut & Style', bookings: 45, revenue: 317475, percentage: 24.8 },\n    { name: 'Hair Color', bookings: 28, revenue: 348600, percentage: 27.2 },\n    { name: 'Manicure', bookings: 38, revenue: 141930, percentage: 11.1 },\n    { name: 'Facial Treatment', bookings: 22, revenue: 219120, percentage: 17.1 },\n    { name: 'Pedicure', bookings: 31, revenue: 141515, percentage: 11.1 },\n  ];\n\n  const staffPerformance = [\n    { name: '<PERSON> <PERSON>', appointments: 42, revenue: 383460, rating: 4.9, efficiency: 95 },\n    { name: 'John Smith', appointments: 38, revenue: 236550, rating: 4.7, efficiency: 92 },\n    { name: 'Mike Johnson', appointments: 35, revenue: 325360, rating: 4.8, efficiency: 88 },\n    { name: 'Sarah Davis', appointments: 41, revenue: 204180, rating: 4.6, efficiency: 90 },\n    { name: 'Lisa Anderson', appointments: 30, revenue: 130310, rating: 4.9, efficiency: 85 },\n  ];\n\n  const monthlyTrends = [\n    { month: 'Jan', revenue: 1037500, appointments: 145 },\n    { month: 'Feb', revenue: 1095600, appointments: 152 },\n    { month: 'Mar', revenue: 1170300, appointments: 168 },\n    { month: 'Apr', revenue: 1149550, appointments: 172 },\n    { month: 'May', revenue: 1279860, appointments: 186 },\n  ];\n\n  const customerInsights = [\n    { metric: 'New Customers', value: 28, change: 12.5 },\n    { metric: 'Returning Customers', value: 114, change: -2.1 },\n    { metric: 'Customer Retention Rate', value: 78, change: 3.2 },\n    { metric: 'Average Visit Value', value: 82.9, change: 5.8 },\n  ];\n\n  const dateRangeOptions = [\n    { value: 'today', label: 'Today' },\n    { value: 'thisWeek', label: 'This Week' },\n    { value: 'thisMonth', label: 'This Month' },\n    { value: 'lastMonth', label: 'Last Month' },\n    { value: 'thisYear', label: 'This Year' },\n    { value: 'custom', label: 'Custom Range' },\n  ];\n\n  const reportTypeOptions = [\n    { value: 'overview', label: 'Overview' },\n    { value: 'revenue', label: 'Revenue Analysis' },\n    { value: 'staff', label: 'Staff Performance' },\n    { value: 'services', label: 'Service Analysis' },\n    { value: 'customers', label: 'Customer Insights' },\n  ];\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 2,\n    }).format(amount);\n  };\n\n  const formatPercentage = (value) => {\n    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;\n  };\n\n  const getChangeIcon = (change) => {\n    return change > 0 ? <TrendingUp color=\"success\" /> : <TrendingDown color=\"error\" />;\n  };\n\n  const getChangeColor = (change) => {\n    return change > 0 ? 'success.main' : 'error.main';\n  };\n\n  return (\n    <Box sx={{ flexGrow: 1, p: 3 }}>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h4\">Reports & Analytics</Typography>\n        <Button\n          variant=\"outlined\"\n          startIcon={<DownloadIcon />}\n        >\n          Export Report\n        </Button>\n      </Box>\n\n      {/* Filters */}\n      <Grid container spacing={2} sx={{ mb: 3 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <TextField\n            fullWidth\n            select\n            label=\"Date Range\"\n            value={dateRange}\n            onChange={(e) => setDateRange(e.target.value)}\n          >\n            {dateRangeOptions.map((option) => (\n              <MenuItem key={option.value} value={option.value}>\n                {option.label}\n              </MenuItem>\n            ))}\n          </TextField>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <TextField\n            fullWidth\n            select\n            label=\"Report Type\"\n            value={reportType}\n            onChange={(e) => setReportType(e.target.value)}\n          >\n            {reportTypeOptions.map((option) => (\n              <MenuItem key={option.value} value={option.value}>\n                {option.label}\n              </MenuItem>\n            ))}\n          </TextField>\n        </Grid>\n      </Grid>\n\n      {/* Overview Cards */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Revenue\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {formatCurrency(overviewData.revenue.current)}\n                  </Typography>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>\n                    {getChangeIcon(overviewData.revenue.change)}\n                    <Typography \n                      variant=\"body2\" \n                      color={getChangeColor(overviewData.revenue.change)}\n                      sx={{ ml: 0.5 }}\n                    >\n                      {formatPercentage(overviewData.revenue.change)}\n                    </Typography>\n                  </Box>\n                </Box>\n                <AttachMoney color=\"primary\" sx={{ fontSize: 40 }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Appointments\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {overviewData.appointments.current}\n                  </Typography>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>\n                    {getChangeIcon(overviewData.appointments.change)}\n                    <Typography \n                      variant=\"body2\" \n                      color={getChangeColor(overviewData.appointments.change)}\n                      sx={{ ml: 0.5 }}\n                    >\n                      {formatPercentage(overviewData.appointments.change)}\n                    </Typography>\n                  </Box>\n                </Box>\n                <Event color=\"primary\" sx={{ fontSize: 40 }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Customers\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {overviewData.customers.current}\n                  </Typography>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>\n                    {getChangeIcon(overviewData.customers.change)}\n                    <Typography \n                      variant=\"body2\" \n                      color={getChangeColor(overviewData.customers.change)}\n                      sx={{ ml: 0.5 }}\n                    >\n                      {formatPercentage(overviewData.customers.change)}\n                    </Typography>\n                  </Box>\n                </Box>\n                <People color=\"primary\" sx={{ fontSize: 40 }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Avg Rating\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {overviewData.avgRating.current}\n                  </Typography>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>\n                    {getChangeIcon(overviewData.avgRating.change)}\n                    <Typography \n                      variant=\"body2\" \n                      color={getChangeColor(overviewData.avgRating.change)}\n                      sx={{ ml: 0.5 }}\n                    >\n                      {formatPercentage(overviewData.avgRating.change)}\n                    </Typography>\n                  </Box>\n                </Box>\n                <Star color=\"primary\" sx={{ fontSize: 40 }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      <Grid container spacing={3}>\n        {/* Top Services */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Top Services\n            </Typography>\n            <TableContainer>\n              <Table size=\"small\">\n                <TableHead>\n                  <TableRow>\n                    <TableCell>Service</TableCell>\n                    <TableCell align=\"right\">Bookings</TableCell>\n                    <TableCell align=\"right\">Revenue</TableCell>\n                    <TableCell align=\"right\">Share</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {topServices.map((service, index) => (\n                    <TableRow key={index}>\n                      <TableCell>{service.name}</TableCell>\n                      <TableCell align=\"right\">{service.bookings}</TableCell>\n                      <TableCell align=\"right\">{formatCurrency(service.revenue)}</TableCell>\n                      <TableCell align=\"right\">\n                        <Chip \n                          label={`${service.percentage}%`} \n                          size=\"small\" \n                          color=\"primary\" \n                          variant=\"outlined\"\n                        />\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </TableContainer>\n          </Paper>\n        </Grid>\n\n        {/* Staff Performance */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Staff Performance\n            </Typography>\n            <TableContainer>\n              <Table size=\"small\">\n                <TableHead>\n                  <TableRow>\n                    <TableCell>Staff Member</TableCell>\n                    <TableCell align=\"right\">Appointments</TableCell>\n                    <TableCell align=\"right\">Revenue</TableCell>\n                    <TableCell align=\"right\">Rating</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {staffPerformance.map((staff, index) => (\n                    <TableRow key={index}>\n                      <TableCell>\n                        <Box>\n                          <Typography variant=\"body2\">{staff.name}</Typography>\n                          <LinearProgress \n                            variant=\"determinate\" \n                            value={staff.efficiency} \n                            sx={{ mt: 0.5, height: 4 }}\n                          />\n                        </Box>\n                      </TableCell>\n                      <TableCell align=\"right\">{staff.appointments}</TableCell>\n                      <TableCell align=\"right\">{formatCurrency(staff.revenue)}</TableCell>\n                      <TableCell align=\"right\">\n                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>\n                          <Star sx={{ fontSize: 16, color: '#ffc107', mr: 0.5 }} />\n                          {staff.rating}\n                        </Box>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </TableContainer>\n          </Paper>\n        </Grid>\n\n        {/* Monthly Trends */}\n        <Grid item xs={12} md={8}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Monthly Trends\n            </Typography>\n            <TableContainer>\n              <Table>\n                <TableHead>\n                  <TableRow>\n                    <TableCell>Month</TableCell>\n                    <TableCell align=\"right\">Revenue</TableCell>\n                    <TableCell align=\"right\">Appointments</TableCell>\n                    <TableCell align=\"right\">Avg per Appointment</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {monthlyTrends.map((month, index) => (\n                    <TableRow key={index}>\n                      <TableCell>{month.month}</TableCell>\n                      <TableCell align=\"right\">{formatCurrency(month.revenue)}</TableCell>\n                      <TableCell align=\"right\">{month.appointments}</TableCell>\n                      <TableCell align=\"right\">\n                        {formatCurrency(month.revenue / month.appointments)}\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </TableContainer>\n          </Paper>\n        </Grid>\n\n        {/* Customer Insights */}\n        <Grid item xs={12} md={4}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Customer Insights\n            </Typography>\n            {customerInsights.map((insight, index) => (\n              <Box key={index} sx={{ mb: 2 }}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    {insight.metric}\n                  </Typography>\n                  <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                    <Typography variant=\"h6\" sx={{ mr: 1 }}>\n                      {insight.metric.includes('Rate') || insight.metric.includes('Value') \n                        ? `${insight.value}${insight.metric.includes('Rate') ? '%' : ''}`\n                        : insight.value\n                      }\n                    </Typography>\n                    <Typography \n                      variant=\"body2\" \n                      color={getChangeColor(insight.change)}\n                    >\n                      {formatPercentage(insight.change)}\n                    </Typography>\n                  </Box>\n                </Box>\n                {index < customerInsights.length - 1 && <Box sx={{ borderBottom: 1, borderColor: 'divider', mt: 1 }} />}\n              </Box>\n            ))}\n          </Paper>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default Reports;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,IAAI,EACJC,cAAc,QACT,eAAe;AACtB,SACEC,UAAU,EACVC,YAAY,EACZC,WAAW,EACXC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAACgC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAC,UAAU,CAAC;;EAExD;EACA,MAAMkC,YAAY,GAAG;IACnBC,OAAO,EAAE;MACPC,OAAO,EAAE,OAAO;MAChBC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE;IACV,CAAC;IACDC,YAAY,EAAE;MACZH,OAAO,EAAE,GAAG;MACZC,QAAQ,EAAE,GAAG;MACbC,MAAM,EAAE;IACV,CAAC;IACDE,SAAS,EAAE;MACTJ,OAAO,EAAE,GAAG;MACZC,QAAQ,EAAE,GAAG;MACbC,MAAM,EAAE;IACV,CAAC;IACDG,SAAS,EAAE;MACTL,OAAO,EAAE,GAAG;MACZC,QAAQ,EAAE,GAAG;MACbC,MAAM,EAAE;IACV;EACF,CAAC;EAED,MAAMI,WAAW,GAAG,CAClB;IAAEC,IAAI,EAAE,kBAAkB;IAAEC,QAAQ,EAAE,EAAE;IAAET,OAAO,EAAE,MAAM;IAAEU,UAAU,EAAE;EAAK,CAAC,EAC7E;IAAEF,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,EAAE;IAAET,OAAO,EAAE,MAAM;IAAEU,UAAU,EAAE;EAAK,CAAC,EACvE;IAAEF,IAAI,EAAE,UAAU;IAAEC,QAAQ,EAAE,EAAE;IAAET,OAAO,EAAE,MAAM;IAAEU,UAAU,EAAE;EAAK,CAAC,EACrE;IAAEF,IAAI,EAAE,kBAAkB;IAAEC,QAAQ,EAAE,EAAE;IAAET,OAAO,EAAE,MAAM;IAAEU,UAAU,EAAE;EAAK,CAAC,EAC7E;IAAEF,IAAI,EAAE,UAAU;IAAEC,QAAQ,EAAE,EAAE;IAAET,OAAO,EAAE,MAAM;IAAEU,UAAU,EAAE;EAAK,CAAC,CACtE;EAED,MAAMC,gBAAgB,GAAG,CACvB;IAAEH,IAAI,EAAE,aAAa;IAAEJ,YAAY,EAAE,EAAE;IAAEJ,OAAO,EAAE,MAAM;IAAEY,MAAM,EAAE,GAAG;IAAEC,UAAU,EAAE;EAAG,CAAC,EACvF;IAAEL,IAAI,EAAE,YAAY;IAAEJ,YAAY,EAAE,EAAE;IAAEJ,OAAO,EAAE,MAAM;IAAEY,MAAM,EAAE,GAAG;IAAEC,UAAU,EAAE;EAAG,CAAC,EACtF;IAAEL,IAAI,EAAE,cAAc;IAAEJ,YAAY,EAAE,EAAE;IAAEJ,OAAO,EAAE,MAAM;IAAEY,MAAM,EAAE,GAAG;IAAEC,UAAU,EAAE;EAAG,CAAC,EACxF;IAAEL,IAAI,EAAE,aAAa;IAAEJ,YAAY,EAAE,EAAE;IAAEJ,OAAO,EAAE,MAAM;IAAEY,MAAM,EAAE,GAAG;IAAEC,UAAU,EAAE;EAAG,CAAC,EACvF;IAAEL,IAAI,EAAE,eAAe;IAAEJ,YAAY,EAAE,EAAE;IAAEJ,OAAO,EAAE,MAAM;IAAEY,MAAM,EAAE,GAAG;IAAEC,UAAU,EAAE;EAAG,CAAC,CAC1F;EAED,MAAMC,aAAa,GAAG,CACpB;IAAEC,KAAK,EAAE,KAAK;IAAEf,OAAO,EAAE,OAAO;IAAEI,YAAY,EAAE;EAAI,CAAC,EACrD;IAAEW,KAAK,EAAE,KAAK;IAAEf,OAAO,EAAE,OAAO;IAAEI,YAAY,EAAE;EAAI,CAAC,EACrD;IAAEW,KAAK,EAAE,KAAK;IAAEf,OAAO,EAAE,OAAO;IAAEI,YAAY,EAAE;EAAI,CAAC,EACrD;IAAEW,KAAK,EAAE,KAAK;IAAEf,OAAO,EAAE,OAAO;IAAEI,YAAY,EAAE;EAAI,CAAC,EACrD;IAAEW,KAAK,EAAE,KAAK;IAAEf,OAAO,EAAE,OAAO;IAAEI,YAAY,EAAE;EAAI,CAAC,CACtD;EAED,MAAMY,gBAAgB,GAAG,CACvB;IAAEC,MAAM,EAAE,eAAe;IAAEC,KAAK,EAAE,EAAE;IAAEf,MAAM,EAAE;EAAK,CAAC,EACpD;IAAEc,MAAM,EAAE,qBAAqB;IAAEC,KAAK,EAAE,GAAG;IAAEf,MAAM,EAAE,CAAC;EAAI,CAAC,EAC3D;IAAEc,MAAM,EAAE,yBAAyB;IAAEC,KAAK,EAAE,EAAE;IAAEf,MAAM,EAAE;EAAI,CAAC,EAC7D;IAAEc,MAAM,EAAE,qBAAqB;IAAEC,KAAK,EAAE,IAAI;IAAEf,MAAM,EAAE;EAAI,CAAC,CAC5D;EAED,MAAMgB,gBAAgB,GAAG,CACvB;IAAED,KAAK,EAAE,OAAO;IAAEE,KAAK,EAAE;EAAQ,CAAC,EAClC;IAAEF,KAAK,EAAE,UAAU;IAAEE,KAAK,EAAE;EAAY,CAAC,EACzC;IAAEF,KAAK,EAAE,WAAW;IAAEE,KAAK,EAAE;EAAa,CAAC,EAC3C;IAAEF,KAAK,EAAE,WAAW;IAAEE,KAAK,EAAE;EAAa,CAAC,EAC3C;IAAEF,KAAK,EAAE,UAAU;IAAEE,KAAK,EAAE;EAAY,CAAC,EACzC;IAAEF,KAAK,EAAE,QAAQ;IAAEE,KAAK,EAAE;EAAe,CAAC,CAC3C;EAED,MAAMC,iBAAiB,GAAG,CACxB;IAAEH,KAAK,EAAE,UAAU;IAAEE,KAAK,EAAE;EAAW,CAAC,EACxC;IAAEF,KAAK,EAAE,SAAS;IAAEE,KAAK,EAAE;EAAmB,CAAC,EAC/C;IAAEF,KAAK,EAAE,OAAO;IAAEE,KAAK,EAAE;EAAoB,CAAC,EAC9C;IAAEF,KAAK,EAAE,UAAU;IAAEE,KAAK,EAAE;EAAmB,CAAC,EAChD;IAAEF,KAAK,EAAE,WAAW;IAAEE,KAAK,EAAE;EAAoB,CAAC,CACnD;EAED,MAAME,cAAc,GAAIC,MAAM,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACP,MAAM,CAAC;EACnB,CAAC;EAED,MAAMQ,gBAAgB,GAAIb,KAAK,IAAK;IAClC,OAAO,GAAGA,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGA,KAAK,CAACc,OAAO,CAAC,CAAC,CAAC,GAAG;EACtD,CAAC;EAED,MAAMC,aAAa,GAAI9B,MAAM,IAAK;IAChC,OAAOA,MAAM,GAAG,CAAC,gBAAGX,OAAA,CAACT,UAAU;MAACmD,KAAK,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAG9C,OAAA,CAACR,YAAY;MAACkD,KAAK,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACrF,CAAC;EAED,MAAMC,cAAc,GAAIpC,MAAM,IAAK;IACjC,OAAOA,MAAM,GAAG,CAAC,GAAG,cAAc,GAAG,YAAY;EACnD,CAAC;EAED,oBACEX,OAAA,CAAC1B,GAAG;IAAC0E,EAAE,EAAE;MAAEC,QAAQ,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAC7BnD,OAAA,CAAC1B,GAAG;MAAC0E,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzFnD,OAAA,CAACzB,UAAU;QAACiF,OAAO,EAAC,IAAI;QAAAL,QAAA,EAAC;MAAmB;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACzD9C,OAAA,CAACZ,MAAM;QACLoE,OAAO,EAAC,UAAU;QAClBC,SAAS,eAAEzD,OAAA,CAACF,YAAY;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAK,QAAA,EAC7B;MAED;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN9C,OAAA,CAACxB,IAAI;MAACkF,SAAS;MAACC,OAAO,EAAE,CAAE;MAACX,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACxCnD,OAAA,CAACxB,IAAI;QAACoF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,eAC9BnD,OAAA,CAACd,SAAS;UACR8E,SAAS;UACTC,MAAM;UACNrC,KAAK,EAAC,YAAY;UAClBF,KAAK,EAAEvB,SAAU;UACjB+D,QAAQ,EAAGC,CAAC,IAAK/D,YAAY,CAAC+D,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAAE;UAAAyB,QAAA,EAE7CxB,gBAAgB,CAAC0C,GAAG,CAAEC,MAAM,iBAC3BtE,OAAA,CAACb,QAAQ;YAAoBuC,KAAK,EAAE4C,MAAM,CAAC5C,KAAM;YAAAyB,QAAA,EAC9CmB,MAAM,CAAC1C;UAAK,GADA0C,MAAM,CAAC5C,KAAK;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEjB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eACP9C,OAAA,CAACxB,IAAI;QAACoF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,eAC9BnD,OAAA,CAACd,SAAS;UACR8E,SAAS;UACTC,MAAM;UACNrC,KAAK,EAAC,aAAa;UACnBF,KAAK,EAAErB,UAAW;UAClB6D,QAAQ,EAAGC,CAAC,IAAK7D,aAAa,CAAC6D,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAAE;UAAAyB,QAAA,EAE9CtB,iBAAiB,CAACwC,GAAG,CAAEC,MAAM,iBAC5BtE,OAAA,CAACb,QAAQ;YAAoBuC,KAAK,EAAE4C,MAAM,CAAC5C,KAAM;YAAAyB,QAAA,EAC9CmB,MAAM,CAAC1C;UAAK,GADA0C,MAAM,CAAC5C,KAAK;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEjB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP9C,OAAA,CAACxB,IAAI;MAACkF,SAAS;MAACC,OAAO,EAAE,CAAE;MAACX,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACxCnD,OAAA,CAACxB,IAAI;QAACoF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,eAC9BnD,OAAA,CAACvB,IAAI;UAAA0E,QAAA,eACHnD,OAAA,CAACtB,WAAW;YAAAyE,QAAA,eACVnD,OAAA,CAAC1B,GAAG;cAAC0E,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAF,QAAA,gBAClFnD,OAAA,CAAC1B,GAAG;gBAAA6E,QAAA,gBACFnD,OAAA,CAACzB,UAAU;kBAACmE,KAAK,EAAC,eAAe;kBAAC6B,YAAY;kBAAApB,QAAA,EAAC;gBAE/C;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb9C,OAAA,CAACzB,UAAU;kBAACiF,OAAO,EAAC,IAAI;kBAAAL,QAAA,EACrBrB,cAAc,CAACvB,YAAY,CAACC,OAAO,CAACC,OAAO;gBAAC;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACb9C,OAAA,CAAC1B,GAAG;kBAAC0E,EAAE,EAAE;oBAAEI,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEkB,EAAE,EAAE;kBAAE,CAAE;kBAAArB,QAAA,GACvDV,aAAa,CAAClC,YAAY,CAACC,OAAO,CAACG,MAAM,CAAC,eAC3CX,OAAA,CAACzB,UAAU;oBACTiF,OAAO,EAAC,OAAO;oBACfd,KAAK,EAAEK,cAAc,CAACxC,YAAY,CAACC,OAAO,CAACG,MAAM,CAAE;oBACnDqC,EAAE,EAAE;sBAAEyB,EAAE,EAAE;oBAAI,CAAE;oBAAAtB,QAAA,EAEfZ,gBAAgB,CAAChC,YAAY,CAACC,OAAO,CAACG,MAAM;kBAAC;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN9C,OAAA,CAACP,WAAW;gBAACiD,KAAK,EAAC,SAAS;gBAACM,EAAE,EAAE;kBAAE0B,QAAQ,EAAE;gBAAG;cAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP9C,OAAA,CAACxB,IAAI;QAACoF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,eAC9BnD,OAAA,CAACvB,IAAI;UAAA0E,QAAA,eACHnD,OAAA,CAACtB,WAAW;YAAAyE,QAAA,eACVnD,OAAA,CAAC1B,GAAG;cAAC0E,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAF,QAAA,gBAClFnD,OAAA,CAAC1B,GAAG;gBAAA6E,QAAA,gBACFnD,OAAA,CAACzB,UAAU;kBAACmE,KAAK,EAAC,eAAe;kBAAC6B,YAAY;kBAAApB,QAAA,EAAC;gBAE/C;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb9C,OAAA,CAACzB,UAAU;kBAACiF,OAAO,EAAC,IAAI;kBAAAL,QAAA,EACrB5C,YAAY,CAACK,YAAY,CAACH;gBAAO;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACb9C,OAAA,CAAC1B,GAAG;kBAAC0E,EAAE,EAAE;oBAAEI,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEkB,EAAE,EAAE;kBAAE,CAAE;kBAAArB,QAAA,GACvDV,aAAa,CAAClC,YAAY,CAACK,YAAY,CAACD,MAAM,CAAC,eAChDX,OAAA,CAACzB,UAAU;oBACTiF,OAAO,EAAC,OAAO;oBACfd,KAAK,EAAEK,cAAc,CAACxC,YAAY,CAACK,YAAY,CAACD,MAAM,CAAE;oBACxDqC,EAAE,EAAE;sBAAEyB,EAAE,EAAE;oBAAI,CAAE;oBAAAtB,QAAA,EAEfZ,gBAAgB,CAAChC,YAAY,CAACK,YAAY,CAACD,MAAM;kBAAC;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN9C,OAAA,CAACL,KAAK;gBAAC+C,KAAK,EAAC,SAAS;gBAACM,EAAE,EAAE;kBAAE0B,QAAQ,EAAE;gBAAG;cAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP9C,OAAA,CAACxB,IAAI;QAACoF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,eAC9BnD,OAAA,CAACvB,IAAI;UAAA0E,QAAA,eACHnD,OAAA,CAACtB,WAAW;YAAAyE,QAAA,eACVnD,OAAA,CAAC1B,GAAG;cAAC0E,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAF,QAAA,gBAClFnD,OAAA,CAAC1B,GAAG;gBAAA6E,QAAA,gBACFnD,OAAA,CAACzB,UAAU;kBAACmE,KAAK,EAAC,eAAe;kBAAC6B,YAAY;kBAAApB,QAAA,EAAC;gBAE/C;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb9C,OAAA,CAACzB,UAAU;kBAACiF,OAAO,EAAC,IAAI;kBAAAL,QAAA,EACrB5C,YAAY,CAACM,SAAS,CAACJ;gBAAO;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACb9C,OAAA,CAAC1B,GAAG;kBAAC0E,EAAE,EAAE;oBAAEI,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEkB,EAAE,EAAE;kBAAE,CAAE;kBAAArB,QAAA,GACvDV,aAAa,CAAClC,YAAY,CAACM,SAAS,CAACF,MAAM,CAAC,eAC7CX,OAAA,CAACzB,UAAU;oBACTiF,OAAO,EAAC,OAAO;oBACfd,KAAK,EAAEK,cAAc,CAACxC,YAAY,CAACM,SAAS,CAACF,MAAM,CAAE;oBACrDqC,EAAE,EAAE;sBAAEyB,EAAE,EAAE;oBAAI,CAAE;oBAAAtB,QAAA,EAEfZ,gBAAgB,CAAChC,YAAY,CAACM,SAAS,CAACF,MAAM;kBAAC;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN9C,OAAA,CAACN,MAAM;gBAACgD,KAAK,EAAC,SAAS;gBAACM,EAAE,EAAE;kBAAE0B,QAAQ,EAAE;gBAAG;cAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP9C,OAAA,CAACxB,IAAI;QAACoF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,eAC9BnD,OAAA,CAACvB,IAAI;UAAA0E,QAAA,eACHnD,OAAA,CAACtB,WAAW;YAAAyE,QAAA,eACVnD,OAAA,CAAC1B,GAAG;cAAC0E,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAF,QAAA,gBAClFnD,OAAA,CAAC1B,GAAG;gBAAA6E,QAAA,gBACFnD,OAAA,CAACzB,UAAU;kBAACmE,KAAK,EAAC,eAAe;kBAAC6B,YAAY;kBAAApB,QAAA,EAAC;gBAE/C;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb9C,OAAA,CAACzB,UAAU;kBAACiF,OAAO,EAAC,IAAI;kBAAAL,QAAA,EACrB5C,YAAY,CAACO,SAAS,CAACL;gBAAO;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACb9C,OAAA,CAAC1B,GAAG;kBAAC0E,EAAE,EAAE;oBAAEI,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEkB,EAAE,EAAE;kBAAE,CAAE;kBAAArB,QAAA,GACvDV,aAAa,CAAClC,YAAY,CAACO,SAAS,CAACH,MAAM,CAAC,eAC7CX,OAAA,CAACzB,UAAU;oBACTiF,OAAO,EAAC,OAAO;oBACfd,KAAK,EAAEK,cAAc,CAACxC,YAAY,CAACO,SAAS,CAACH,MAAM,CAAE;oBACrDqC,EAAE,EAAE;sBAAEyB,EAAE,EAAE;oBAAI,CAAE;oBAAAtB,QAAA,EAEfZ,gBAAgB,CAAChC,YAAY,CAACO,SAAS,CAACH,MAAM;kBAAC;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN9C,OAAA,CAACJ,IAAI;gBAAC8C,KAAK,EAAC,SAAS;gBAACM,EAAE,EAAE;kBAAE0B,QAAQ,EAAE;gBAAG;cAAE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEP9C,OAAA,CAACxB,IAAI;MAACkF,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAR,QAAA,gBAEzBnD,OAAA,CAACxB,IAAI;QAACoF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAZ,QAAA,eACvBnD,OAAA,CAACrB,KAAK;UAACqE,EAAE,EAAE;YAAEE,CAAC,EAAE;UAAE,CAAE;UAAAC,QAAA,gBAClBnD,OAAA,CAACzB,UAAU;YAACiF,OAAO,EAAC,IAAI;YAACe,YAAY;YAAApB,QAAA,EAAC;UAEtC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9C,OAAA,CAACjB,cAAc;YAAAoE,QAAA,eACbnD,OAAA,CAACpB,KAAK;cAAC+F,IAAI,EAAC,OAAO;cAAAxB,QAAA,gBACjBnD,OAAA,CAAChB,SAAS;gBAAAmE,QAAA,eACRnD,OAAA,CAACf,QAAQ;kBAAAkE,QAAA,gBACPnD,OAAA,CAAClB,SAAS;oBAAAqE,QAAA,EAAC;kBAAO;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9B9C,OAAA,CAAClB,SAAS;oBAAC8F,KAAK,EAAC,OAAO;oBAAAzB,QAAA,EAAC;kBAAQ;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC7C9C,OAAA,CAAClB,SAAS;oBAAC8F,KAAK,EAAC,OAAO;oBAAAzB,QAAA,EAAC;kBAAO;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC5C9C,OAAA,CAAClB,SAAS;oBAAC8F,KAAK,EAAC,OAAO;oBAAAzB,QAAA,EAAC;kBAAK;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZ9C,OAAA,CAACnB,SAAS;gBAAAsE,QAAA,EACPpC,WAAW,CAACsD,GAAG,CAAC,CAACQ,OAAO,EAAEC,KAAK,kBAC9B9E,OAAA,CAACf,QAAQ;kBAAAkE,QAAA,gBACPnD,OAAA,CAAClB,SAAS;oBAAAqE,QAAA,EAAE0B,OAAO,CAAC7D;kBAAI;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrC9C,OAAA,CAAClB,SAAS;oBAAC8F,KAAK,EAAC,OAAO;oBAAAzB,QAAA,EAAE0B,OAAO,CAAC5D;kBAAQ;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACvD9C,OAAA,CAAClB,SAAS;oBAAC8F,KAAK,EAAC,OAAO;oBAAAzB,QAAA,EAAErB,cAAc,CAAC+C,OAAO,CAACrE,OAAO;kBAAC;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACtE9C,OAAA,CAAClB,SAAS;oBAAC8F,KAAK,EAAC,OAAO;oBAAAzB,QAAA,eACtBnD,OAAA,CAACX,IAAI;sBACHuC,KAAK,EAAE,GAAGiD,OAAO,CAAC3D,UAAU,GAAI;sBAChCyD,IAAI,EAAC,OAAO;sBACZjC,KAAK,EAAC,SAAS;sBACfc,OAAO,EAAC;oBAAU;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC;gBAAA,GAXCgC,KAAK;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAYV,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP9C,OAAA,CAACxB,IAAI;QAACoF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAZ,QAAA,eACvBnD,OAAA,CAACrB,KAAK;UAACqE,EAAE,EAAE;YAAEE,CAAC,EAAE;UAAE,CAAE;UAAAC,QAAA,gBAClBnD,OAAA,CAACzB,UAAU;YAACiF,OAAO,EAAC,IAAI;YAACe,YAAY;YAAApB,QAAA,EAAC;UAEtC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9C,OAAA,CAACjB,cAAc;YAAAoE,QAAA,eACbnD,OAAA,CAACpB,KAAK;cAAC+F,IAAI,EAAC,OAAO;cAAAxB,QAAA,gBACjBnD,OAAA,CAAChB,SAAS;gBAAAmE,QAAA,eACRnD,OAAA,CAACf,QAAQ;kBAAAkE,QAAA,gBACPnD,OAAA,CAAClB,SAAS;oBAAAqE,QAAA,EAAC;kBAAY;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACnC9C,OAAA,CAAClB,SAAS;oBAAC8F,KAAK,EAAC,OAAO;oBAAAzB,QAAA,EAAC;kBAAY;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACjD9C,OAAA,CAAClB,SAAS;oBAAC8F,KAAK,EAAC,OAAO;oBAAAzB,QAAA,EAAC;kBAAO;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC5C9C,OAAA,CAAClB,SAAS;oBAAC8F,KAAK,EAAC,OAAO;oBAAAzB,QAAA,EAAC;kBAAM;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZ9C,OAAA,CAACnB,SAAS;gBAAAsE,QAAA,EACPhC,gBAAgB,CAACkD,GAAG,CAAC,CAACU,KAAK,EAAED,KAAK,kBACjC9E,OAAA,CAACf,QAAQ;kBAAAkE,QAAA,gBACPnD,OAAA,CAAClB,SAAS;oBAAAqE,QAAA,eACRnD,OAAA,CAAC1B,GAAG;sBAAA6E,QAAA,gBACFnD,OAAA,CAACzB,UAAU;wBAACiF,OAAO,EAAC,OAAO;wBAAAL,QAAA,EAAE4B,KAAK,CAAC/D;sBAAI;wBAAA2B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC,eACrD9C,OAAA,CAACV,cAAc;wBACbkE,OAAO,EAAC,aAAa;wBACrB9B,KAAK,EAAEqD,KAAK,CAAC1D,UAAW;wBACxB2B,EAAE,EAAE;0BAAEwB,EAAE,EAAE,GAAG;0BAAEQ,MAAM,EAAE;wBAAE;sBAAE;wBAAArC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACZ9C,OAAA,CAAClB,SAAS;oBAAC8F,KAAK,EAAC,OAAO;oBAAAzB,QAAA,EAAE4B,KAAK,CAACnE;kBAAY;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzD9C,OAAA,CAAClB,SAAS;oBAAC8F,KAAK,EAAC,OAAO;oBAAAzB,QAAA,EAAErB,cAAc,CAACiD,KAAK,CAACvE,OAAO;kBAAC;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACpE9C,OAAA,CAAClB,SAAS;oBAAC8F,KAAK,EAAC,OAAO;oBAAAzB,QAAA,eACtBnD,OAAA,CAAC1B,GAAG;sBAAC0E,EAAE,EAAE;wBAAEI,OAAO,EAAE,MAAM;wBAAEE,UAAU,EAAE,QAAQ;wBAAED,cAAc,EAAE;sBAAW,CAAE;sBAAAF,QAAA,gBAC7EnD,OAAA,CAACJ,IAAI;wBAACoD,EAAE,EAAE;0BAAE0B,QAAQ,EAAE,EAAE;0BAAEhC,KAAK,EAAE,SAAS;0BAAEuC,EAAE,EAAE;wBAAI;sBAAE;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,EACxDiC,KAAK,CAAC3D,MAAM;oBAAA;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA,GAlBCgC,KAAK;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAmBV,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP9C,OAAA,CAACxB,IAAI;QAACoF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAZ,QAAA,eACvBnD,OAAA,CAACrB,KAAK;UAACqE,EAAE,EAAE;YAAEE,CAAC,EAAE;UAAE,CAAE;UAAAC,QAAA,gBAClBnD,OAAA,CAACzB,UAAU;YAACiF,OAAO,EAAC,IAAI;YAACe,YAAY;YAAApB,QAAA,EAAC;UAEtC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9C,OAAA,CAACjB,cAAc;YAAAoE,QAAA,eACbnD,OAAA,CAACpB,KAAK;cAAAuE,QAAA,gBACJnD,OAAA,CAAChB,SAAS;gBAAAmE,QAAA,eACRnD,OAAA,CAACf,QAAQ;kBAAAkE,QAAA,gBACPnD,OAAA,CAAClB,SAAS;oBAAAqE,QAAA,EAAC;kBAAK;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC5B9C,OAAA,CAAClB,SAAS;oBAAC8F,KAAK,EAAC,OAAO;oBAAAzB,QAAA,EAAC;kBAAO;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC5C9C,OAAA,CAAClB,SAAS;oBAAC8F,KAAK,EAAC,OAAO;oBAAAzB,QAAA,EAAC;kBAAY;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACjD9C,OAAA,CAAClB,SAAS;oBAAC8F,KAAK,EAAC,OAAO;oBAAAzB,QAAA,EAAC;kBAAmB;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZ9C,OAAA,CAACnB,SAAS;gBAAAsE,QAAA,EACP7B,aAAa,CAAC+C,GAAG,CAAC,CAAC9C,KAAK,EAAEuD,KAAK,kBAC9B9E,OAAA,CAACf,QAAQ;kBAAAkE,QAAA,gBACPnD,OAAA,CAAClB,SAAS;oBAAAqE,QAAA,EAAE5B,KAAK,CAACA;kBAAK;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACpC9C,OAAA,CAAClB,SAAS;oBAAC8F,KAAK,EAAC,OAAO;oBAAAzB,QAAA,EAAErB,cAAc,CAACP,KAAK,CAACf,OAAO;kBAAC;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACpE9C,OAAA,CAAClB,SAAS;oBAAC8F,KAAK,EAAC,OAAO;oBAAAzB,QAAA,EAAE5B,KAAK,CAACX;kBAAY;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzD9C,OAAA,CAAClB,SAAS;oBAAC8F,KAAK,EAAC,OAAO;oBAAAzB,QAAA,EACrBrB,cAAc,CAACP,KAAK,CAACf,OAAO,GAAGe,KAAK,CAACX,YAAY;kBAAC;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA,GANCgC,KAAK;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOV,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP9C,OAAA,CAACxB,IAAI;QAACoF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAZ,QAAA,eACvBnD,OAAA,CAACrB,KAAK;UAACqE,EAAE,EAAE;YAAEE,CAAC,EAAE;UAAE,CAAE;UAAAC,QAAA,gBAClBnD,OAAA,CAACzB,UAAU;YAACiF,OAAO,EAAC,IAAI;YAACe,YAAY;YAAApB,QAAA,EAAC;UAEtC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZtB,gBAAgB,CAAC6C,GAAG,CAAC,CAACa,OAAO,EAAEJ,KAAK,kBACnC9E,OAAA,CAAC1B,GAAG;YAAa0E,EAAE,EAAE;cAAEO,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBAC7BnD,OAAA,CAAC1B,GAAG;cAAC0E,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAH,QAAA,gBAClFnD,OAAA,CAACzB,UAAU;gBAACiF,OAAO,EAAC,OAAO;gBAACd,KAAK,EAAC,gBAAgB;gBAAAS,QAAA,EAC/C+B,OAAO,CAACzD;cAAM;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACb9C,OAAA,CAAC1B,GAAG;gBAAC0E,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAH,QAAA,gBACjDnD,OAAA,CAACzB,UAAU;kBAACiF,OAAO,EAAC,IAAI;kBAACR,EAAE,EAAE;oBAAEiC,EAAE,EAAE;kBAAE,CAAE;kBAAA9B,QAAA,EACpC+B,OAAO,CAACzD,MAAM,CAAC0D,QAAQ,CAAC,MAAM,CAAC,IAAID,OAAO,CAACzD,MAAM,CAAC0D,QAAQ,CAAC,OAAO,CAAC,GAChE,GAAGD,OAAO,CAACxD,KAAK,GAAGwD,OAAO,CAACzD,MAAM,CAAC0D,QAAQ,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,GAC/DD,OAAO,CAACxD;gBAAK;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEP,CAAC,eACb9C,OAAA,CAACzB,UAAU;kBACTiF,OAAO,EAAC,OAAO;kBACfd,KAAK,EAAEK,cAAc,CAACmC,OAAO,CAACvE,MAAM,CAAE;kBAAAwC,QAAA,EAErCZ,gBAAgB,CAAC2C,OAAO,CAACvE,MAAM;gBAAC;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLgC,KAAK,GAAGtD,gBAAgB,CAAC4D,MAAM,GAAG,CAAC,iBAAIpF,OAAA,CAAC1B,GAAG;cAAC0E,EAAE,EAAE;gBAAEqC,YAAY,EAAE,CAAC;gBAAEC,WAAW,EAAE,SAAS;gBAAEd,EAAE,EAAE;cAAE;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GApB/FgC,KAAK;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqBV,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC5C,EAAA,CA1ZID,OAAO;AAAAsF,EAAA,GAAPtF,OAAO;AA4Zb,eAAeA,OAAO;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}