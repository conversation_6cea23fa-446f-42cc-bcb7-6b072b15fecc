{"ast": null, "code": "var _jsxFileName = \"D:\\\\Project\\\\salon-management-system\\\\src\\\\components\\\\Navbar.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { SparklesIcon, UserIcon, ArrowRightOnRectangleIcon, ArrowLeftOnRectangleIcon, UserCircleIcon, ShieldCheckIcon, IdentificationIcon, Bars3Icon, XMarkIcon, CalendarDaysIcon, UsersIcon, CogIcon, ChartBarIcon, CubeIcon, CreditCardIcon, DocumentTextIcon } from '@heroicons/react/24/outline';\nimport { Menu, Transition } from '@headlessui/react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  var _user$name;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 10);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const handleNavigation = path => {\n    navigate(path);\n    setIsMobileMenuOpen(false);\n  };\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n  const getRoleIcon = role => {\n    switch (role) {\n      case 'admin':\n        return /*#__PURE__*/_jsxDEV(ShieldCheckIcon, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 16\n        }, this);\n      case 'staff':\n        return /*#__PURE__*/_jsxDEV(IdentificationIcon, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(UserIcon, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getRoleBadgeColor = role => {\n    switch (role) {\n      case 'admin':\n        return 'bg-red-100 text-red-800';\n      case 'staff':\n        return 'bg-blue-100 text-blue-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const getMenuItems = () => {\n    const baseItems = [{\n      text: 'Dashboard',\n      path: '/',\n      icon: ChartBarIcon,\n      roles: ['admin', 'staff']\n    }, {\n      text: 'Appointments',\n      path: '/appointments',\n      icon: CalendarDaysIcon,\n      roles: ['admin', 'staff']\n    }, {\n      text: 'Customers',\n      path: '/customers',\n      icon: UsersIcon,\n      roles: ['admin', 'staff']\n    }, {\n      text: 'Services',\n      path: '/services',\n      icon: CogIcon,\n      roles: ['admin', 'staff']\n    }, {\n      text: 'Inventory',\n      path: '/inventory',\n      icon: CubeIcon,\n      roles: ['admin', 'staff']\n    }, {\n      text: 'Billing',\n      path: '/billing',\n      icon: CreditCardIcon,\n      roles: ['admin', 'staff']\n    }, {\n      text: 'Staff',\n      path: '/staff',\n      icon: UsersIcon,\n      roles: ['admin']\n    }, {\n      text: 'Reports',\n      path: '/reports',\n      icon: DocumentTextIcon,\n      roles: ['admin']\n    }, {\n      text: 'Billing Reports',\n      path: '/billing-reports',\n      icon: DocumentTextIcon,\n      roles: ['admin']\n    }];\n    if (!user) return [];\n    return baseItems.filter(item => item.roles.includes(user.role) || user.role === 'admin');\n  };\n  const menuItems = getMenuItems();\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(motion.nav, {\n      initial: {\n        y: -100\n      },\n      animate: {\n        y: 0\n      },\n      className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${scrolled ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200' : 'bg-white/90 backdrop-blur-sm'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center h-16\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            whileHover: {\n              scale: 1.05\n            },\n            className: \"flex items-center space-x-3 cursor-pointer\",\n            onClick: () => handleNavigation('/'),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(SparklesIcon, {\n                className: \"h-6 w-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden sm:block\",\n              children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent\",\n                children: \"Salon Management\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), user && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex items-center space-x-1\",\n            children: menuItems.map(item => {\n              const Icon = item.icon;\n              const isActive = location.pathname === item.path;\n              return /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: () => handleNavigation(item.path),\n                className: `flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${isActive ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-md' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'}`,\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: item.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 23\n                }, this)]\n              }, item.text, true, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: user ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden lg:block\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium ${getRoleBadgeColor(user.role)}`,\n                  children: [getRoleIcon(user.role), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"capitalize\",\n                    children: user.role\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 157,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Menu, {\n                as: \"div\",\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(Menu.Button, {\n                  className: \"flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 transition-colors\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-medium text-sm\",\n                    children: (_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.charAt(0).toUpperCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"hidden sm:block text-left\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: user.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 168,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500 capitalize\",\n                      children: user.role\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 169,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Transition, {\n                  as: React.Fragment,\n                  enter: \"transition ease-out duration-100\",\n                  enterFrom: \"transform opacity-0 scale-95\",\n                  enterTo: \"transform opacity-100 scale-100\",\n                  leave: \"transition ease-in duration-75\",\n                  leaveFrom: \"transform opacity-100 scale-100\",\n                  leaveTo: \"transform opacity-0 scale-95\",\n                  children: /*#__PURE__*/_jsxDEV(Menu.Items, {\n                    className: \"absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-1\",\n                      children: [/*#__PURE__*/_jsxDEV(Menu.Item, {\n                        children: ({\n                          active\n                        }) => /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => navigate('/profile'),\n                          className: `${active ? 'bg-gray-100' : ''} group flex w-full items-center rounded-lg px-3 py-2 text-sm text-gray-900`,\n                          children: [/*#__PURE__*/_jsxDEV(UserCircleIcon, {\n                            className: \"mr-3 h-5 w-5 text-gray-400\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 192,\n                            columnNumber: 33\n                          }, this), \"Profile\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 186,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 184,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Menu.Item, {\n                        children: ({\n                          active\n                        }) => /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: handleLogout,\n                          className: `${active ? 'bg-gray-100' : ''} group flex w-full items-center rounded-lg px-3 py-2 text-sm text-gray-900`,\n                          children: [/*#__PURE__*/_jsxDEV(ArrowRightOnRectangleIcon, {\n                            className: \"mr-3 h-5 w-5 text-gray-400\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 205,\n                            columnNumber: 33\n                          }, this), \"Sign out\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 199,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 197,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 183,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n                className: \"md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors\",\n                children: isMobileMenuOpen ? /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                  className: \"h-6 w-6 text-gray-700\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(Bars3Icon, {\n                  className: \"h-6 w-6 text-gray-700\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => navigate('/login'),\n              className: \"flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg font-medium hover:shadow-lg transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(ArrowLeftOnRectangleIcon, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Sign In\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: isMobileMenuOpen && user && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            height: 0\n          },\n          animate: {\n            opacity: 1,\n            height: 'auto'\n          },\n          exit: {\n            opacity: 0,\n            height: 0\n          },\n          className: \"md:hidden bg-white border-t border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-4 py-3 space-y-1\",\n            children: menuItems.map(item => {\n              const Icon = item.icon;\n              const isActive = location.pathname === item.path;\n              return /*#__PURE__*/_jsxDEV(motion.button, {\n                whileTap: {\n                  scale: 0.95\n                },\n                onClick: () => handleNavigation(item.path),\n                className: `flex items-center space-x-3 w-full px-3 py-2 rounded-lg text-left transition-colors ${isActive ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white' : 'text-gray-700 hover:bg-gray-100'}`,\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: item.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 23\n                }, this)]\n              }, item.text, true, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-20\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Navbar, \"mvgFTv3ZF8I9Ae4j/gt50oETLKI=\", false, function () {\n  return [useNavigate, useLocation, useAuth];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "SparklesIcon", "UserIcon", "ArrowRightOnRectangleIcon", "ArrowLeftOnRectangleIcon", "UserCircleIcon", "ShieldCheckIcon", "IdentificationIcon", "Bars3Icon", "XMarkIcon", "CalendarDaysIcon", "UsersIcon", "CogIcon", "ChartBarIcon", "CubeIcon", "CreditCardIcon", "DocumentTextIcon", "<PERSON><PERSON>", "Transition", "useNavigate", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "_s", "_user$name", "navigate", "location", "user", "logout", "isMobileMenuOpen", "setIsMobileMenuOpen", "scrolled", "setScrolled", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "handleNavigation", "path", "handleLogout", "getRoleIcon", "role", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getRoleBadgeColor", "getMenuItems", "baseItems", "text", "icon", "roles", "filter", "item", "includes", "menuItems", "children", "nav", "initial", "y", "animate", "div", "whileHover", "scale", "onClick", "map", "Icon", "isActive", "pathname", "button", "whileTap", "as", "<PERSON><PERSON>", "name", "char<PERSON>t", "toUpperCase", "enter", "enterFrom", "enterTo", "leave", "leaveFrom", "leaveTo", "Items", "<PERSON><PERSON>", "active", "opacity", "height", "exit", "_c", "$RefreshReg$"], "sources": ["D:/Project/salon-management-system/src/components/Navbar.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  SparklesIcon,\n  UserIcon,\n  ArrowRightOnRectangleIcon,\n  ArrowLeftOnRectangleIcon,\n  UserCircleIcon,\n  ShieldCheckIcon,\n  IdentificationIcon,\n  Bars3Icon,\n  XMarkIcon,\n  CalendarDaysIcon,\n  UsersIcon,\n  CogIcon,\n  ChartBarIcon,\n  CubeIcon,\n  CreditCardIcon,\n  DocumentTextIcon\n} from '@heroicons/react/24/outline';\nimport { Menu, Transition } from '@headlessui/react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst Navbar = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user, logout } = useAuth();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 10);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const handleNavigation = (path) => {\n    navigate(path);\n    setIsMobileMenuOpen(false);\n  };\n\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n\n  const getRoleIcon = (role) => {\n    switch (role) {\n      case 'admin':\n        return <ShieldCheckIcon className=\"h-4 w-4\" />;\n      case 'staff':\n        return <IdentificationIcon className=\"h-4 w-4\" />;\n      default:\n        return <UserIcon className=\"h-4 w-4\" />;\n    }\n  };\n\n  const getRoleBadgeColor = (role) => {\n    switch (role) {\n      case 'admin':\n        return 'bg-red-100 text-red-800';\n      case 'staff':\n        return 'bg-blue-100 text-blue-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getMenuItems = () => {\n    const baseItems = [\n      { text: 'Dashboard', path: '/', icon: ChartBarIcon, roles: ['admin', 'staff'] },\n      { text: 'Appointments', path: '/appointments', icon: CalendarDaysIcon, roles: ['admin', 'staff'] },\n      { text: 'Customers', path: '/customers', icon: UsersIcon, roles: ['admin', 'staff'] },\n      { text: 'Services', path: '/services', icon: CogIcon, roles: ['admin', 'staff'] },\n      { text: 'Inventory', path: '/inventory', icon: CubeIcon, roles: ['admin', 'staff'] },\n      { text: 'Billing', path: '/billing', icon: CreditCardIcon, roles: ['admin', 'staff'] },\n      { text: 'Staff', path: '/staff', icon: UsersIcon, roles: ['admin'] },\n      { text: 'Reports', path: '/reports', icon: DocumentTextIcon, roles: ['admin'] },\n      { text: 'Billing Reports', path: '/billing-reports', icon: DocumentTextIcon, roles: ['admin'] },\n    ];\n\n    if (!user) return [];\n\n    return baseItems.filter(item =>\n      item.roles.includes(user.role) || user.role === 'admin'\n    );\n  };\n\n  const menuItems = getMenuItems();\n\n  return (\n    <>\n      <motion.nav\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n          scrolled\n            ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200'\n            : 'bg-white/90 backdrop-blur-sm'\n        }`}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            {/* Logo */}\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"flex items-center space-x-3 cursor-pointer\"\n              onClick={() => handleNavigation('/')}\n            >\n              <div className=\"p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg\">\n                <SparklesIcon className=\"h-6 w-6 text-white\" />\n              </div>\n              <div className=\"hidden sm:block\">\n                <h1 className=\"text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent\">\n                  Salon Management\n                </h1>\n              </div>\n            </motion.div>\n\n            {/* Desktop Navigation */}\n            {user && (\n              <div className=\"hidden md:flex items-center space-x-1\">\n                {menuItems.map((item) => {\n                  const Icon = item.icon;\n                  const isActive = location.pathname === item.path;\n                  return (\n                    <motion.button\n                      key={item.text}\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      onClick={() => handleNavigation(item.path)}\n                      className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${\n                        isActive\n                          ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-md'\n                          : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'\n                      }`}\n                    >\n                      <Icon className=\"h-4 w-4\" />\n                      <span>{item.text}</span>\n                    </motion.button>\n                  );\n                })}\n              </div>\n            )}\n\n            {/* Right Section */}\n            <div className=\"flex items-center space-x-4\">\n              {user ? (\n                <>\n                  {/* Role Badge - Desktop */}\n                  <div className=\"hidden lg:block\">\n                    <span className={`inline-flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium ${getRoleBadgeColor(user.role)}`}>\n                      {getRoleIcon(user.role)}\n                      <span className=\"capitalize\">{user.role}</span>\n                    </span>\n                  </div>\n\n                  {/* User Menu */}\n                  <Menu as=\"div\" className=\"relative\">\n                    <Menu.Button className=\"flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 transition-colors\">\n                      <div className=\"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-medium text-sm\">\n                        {user.name?.charAt(0).toUpperCase()}\n                      </div>\n                      <div className=\"hidden sm:block text-left\">\n                        <p className=\"text-sm font-medium text-gray-900\">{user.name}</p>\n                        <p className=\"text-xs text-gray-500 capitalize\">{user.role}</p>\n                      </div>\n                    </Menu.Button>\n\n                    <Transition\n                      as={React.Fragment}\n                      enter=\"transition ease-out duration-100\"\n                      enterFrom=\"transform opacity-0 scale-95\"\n                      enterTo=\"transform opacity-100 scale-100\"\n                      leave=\"transition ease-in duration-75\"\n                      leaveFrom=\"transform opacity-100 scale-100\"\n                      leaveTo=\"transform opacity-0 scale-95\"\n                    >\n                      <Menu.Items className=\"absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\">\n                        <div className=\"p-1\">\n                          <Menu.Item>\n                            {({ active }) => (\n                              <button\n                                onClick={() => navigate('/profile')}\n                                className={`${\n                                  active ? 'bg-gray-100' : ''\n                                } group flex w-full items-center rounded-lg px-3 py-2 text-sm text-gray-900`}\n                              >\n                                <UserCircleIcon className=\"mr-3 h-5 w-5 text-gray-400\" />\n                                Profile\n                              </button>\n                            )}\n                          </Menu.Item>\n                          <Menu.Item>\n                            {({ active }) => (\n                              <button\n                                onClick={handleLogout}\n                                className={`${\n                                  active ? 'bg-gray-100' : ''\n                                } group flex w-full items-center rounded-lg px-3 py-2 text-sm text-gray-900`}\n                              >\n                                <ArrowRightOnRectangleIcon className=\"mr-3 h-5 w-5 text-gray-400\" />\n                                Sign out\n                              </button>\n                            )}\n                          </Menu.Item>\n                        </div>\n                      </Menu.Items>\n                    </Transition>\n                  </Menu>\n\n                  {/* Mobile Menu Button */}\n                  <button\n                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n                    className=\"md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n                  >\n                    {isMobileMenuOpen ? (\n                      <XMarkIcon className=\"h-6 w-6 text-gray-700\" />\n                    ) : (\n                      <Bars3Icon className=\"h-6 w-6 text-gray-700\" />\n                    )}\n                  </button>\n                </>\n              ) : (\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => navigate('/login')}\n                  className=\"flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg font-medium hover:shadow-lg transition-all duration-200\"\n                >\n                  <ArrowLeftOnRectangleIcon className=\"h-4 w-4\" />\n                  <span>Sign In</span>\n                </motion.button>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        <AnimatePresence>\n          {isMobileMenuOpen && user && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              className=\"md:hidden bg-white border-t border-gray-200\"\n            >\n              <div className=\"px-4 py-3 space-y-1\">\n                {menuItems.map((item) => {\n                  const Icon = item.icon;\n                  const isActive = location.pathname === item.path;\n                  return (\n                    <motion.button\n                      key={item.text}\n                      whileTap={{ scale: 0.95 }}\n                      onClick={() => handleNavigation(item.path)}\n                      className={`flex items-center space-x-3 w-full px-3 py-2 rounded-lg text-left transition-colors ${\n                        isActive\n                          ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'\n                          : 'text-gray-700 hover:bg-gray-100'\n                      }`}\n                    >\n                      <Icon className=\"h-5 w-5\" />\n                      <span className=\"font-medium\">{item.text}</span>\n                    </motion.button>\n                  );\n                })}\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </motion.nav>\n\n      {/* Spacer to prevent content from hiding behind fixed navbar */}\n      <div className=\"h-20\"></div>\n    </>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,YAAY,EACZC,QAAQ,EACRC,yBAAyB,EACzBC,wBAAwB,EACxBC,cAAc,EACdC,eAAe,EACfC,kBAAkB,EAClBC,SAAS,EACTC,SAAS,EACTC,gBAAgB,EAChBC,SAAS,EACTC,OAAO,EACPC,YAAY,EACZC,QAAQ,EACRC,cAAc,EACdC,gBAAgB,QACX,6BAA6B;AACpC,SAASC,IAAI,EAAEC,UAAU,QAAQ,mBAAmB;AACpD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,UAAA;EACnB,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEW,IAAI;IAAEC;EAAO,CAAC,GAAGX,OAAO,CAAC,CAAC;EAClC,MAAM,CAACY,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAE/CC,SAAS,CAAC,MAAM;IACd,MAAMuC,YAAY,GAAGA,CAAA,KAAM;MACzBD,WAAW,CAACE,MAAM,CAACC,OAAO,GAAG,EAAE,CAAC;IAClC,CAAC;IACDD,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAC/C,OAAO,MAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,gBAAgB,GAAIC,IAAI,IAAK;IACjCd,QAAQ,CAACc,IAAI,CAAC;IACdT,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAMU,YAAY,GAAGA,CAAA,KAAM;IACzBZ,MAAM,CAAC,CAAC;IACRH,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMgB,WAAW,GAAIC,IAAI,IAAK;IAC5B,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,oBAAOvB,OAAA,CAACjB,eAAe;UAACyC,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAChD,KAAK,OAAO;QACV,oBAAO5B,OAAA,CAAChB,kBAAkB;UAACwC,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnD;QACE,oBAAO5B,OAAA,CAACrB,QAAQ;UAAC6C,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC3C;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAIN,IAAI,IAAK;IAClC,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,OAAO,yBAAyB;MAClC,KAAK,OAAO;QACV,OAAO,2BAA2B;MACpC;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;EAED,MAAMO,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAChB;MAAEC,IAAI,EAAE,WAAW;MAAEZ,IAAI,EAAE,GAAG;MAAEa,IAAI,EAAE3C,YAAY;MAAE4C,KAAK,EAAE,CAAC,OAAO,EAAE,OAAO;IAAE,CAAC,EAC/E;MAAEF,IAAI,EAAE,cAAc;MAAEZ,IAAI,EAAE,eAAe;MAAEa,IAAI,EAAE9C,gBAAgB;MAAE+C,KAAK,EAAE,CAAC,OAAO,EAAE,OAAO;IAAE,CAAC,EAClG;MAAEF,IAAI,EAAE,WAAW;MAAEZ,IAAI,EAAE,YAAY;MAAEa,IAAI,EAAE7C,SAAS;MAAE8C,KAAK,EAAE,CAAC,OAAO,EAAE,OAAO;IAAE,CAAC,EACrF;MAAEF,IAAI,EAAE,UAAU;MAAEZ,IAAI,EAAE,WAAW;MAAEa,IAAI,EAAE5C,OAAO;MAAE6C,KAAK,EAAE,CAAC,OAAO,EAAE,OAAO;IAAE,CAAC,EACjF;MAAEF,IAAI,EAAE,WAAW;MAAEZ,IAAI,EAAE,YAAY;MAAEa,IAAI,EAAE1C,QAAQ;MAAE2C,KAAK,EAAE,CAAC,OAAO,EAAE,OAAO;IAAE,CAAC,EACpF;MAAEF,IAAI,EAAE,SAAS;MAAEZ,IAAI,EAAE,UAAU;MAAEa,IAAI,EAAEzC,cAAc;MAAE0C,KAAK,EAAE,CAAC,OAAO,EAAE,OAAO;IAAE,CAAC,EACtF;MAAEF,IAAI,EAAE,OAAO;MAAEZ,IAAI,EAAE,QAAQ;MAAEa,IAAI,EAAE7C,SAAS;MAAE8C,KAAK,EAAE,CAAC,OAAO;IAAE,CAAC,EACpE;MAAEF,IAAI,EAAE,SAAS;MAAEZ,IAAI,EAAE,UAAU;MAAEa,IAAI,EAAExC,gBAAgB;MAAEyC,KAAK,EAAE,CAAC,OAAO;IAAE,CAAC,EAC/E;MAAEF,IAAI,EAAE,iBAAiB;MAAEZ,IAAI,EAAE,kBAAkB;MAAEa,IAAI,EAAExC,gBAAgB;MAAEyC,KAAK,EAAE,CAAC,OAAO;IAAE,CAAC,CAChG;IAED,IAAI,CAAC1B,IAAI,EAAE,OAAO,EAAE;IAEpB,OAAOuB,SAAS,CAACI,MAAM,CAACC,IAAI,IAC1BA,IAAI,CAACF,KAAK,CAACG,QAAQ,CAAC7B,IAAI,CAACe,IAAI,CAAC,IAAIf,IAAI,CAACe,IAAI,KAAK,OAClD,CAAC;EACH,CAAC;EAED,MAAMe,SAAS,GAAGR,YAAY,CAAC,CAAC;EAEhC,oBACE9B,OAAA,CAAAE,SAAA;IAAAqC,QAAA,gBACEvC,OAAA,CAACxB,MAAM,CAACgE,GAAG;MACTC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC;MAAI,CAAE;MACrBC,OAAO,EAAE;QAAED,CAAC,EAAE;MAAE,CAAE;MAClBlB,SAAS,EAAE,+DACTZ,QAAQ,GACJ,iEAAiE,GACjE,8BAA8B,EACjC;MAAA2B,QAAA,gBAEHvC,OAAA;QAAKwB,SAAS,EAAC,wCAAwC;QAAAe,QAAA,eACrDvC,OAAA;UAAKwB,SAAS,EAAC,wCAAwC;UAAAe,QAAA,gBAErDvC,OAAA,CAACxB,MAAM,CAACoE,GAAG;YACTC,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BtB,SAAS,EAAC,4CAA4C;YACtDuB,OAAO,EAAEA,CAAA,KAAM5B,gBAAgB,CAAC,GAAG,CAAE;YAAAoB,QAAA,gBAErCvC,OAAA;cAAKwB,SAAS,EAAC,6DAA6D;cAAAe,QAAA,eAC1EvC,OAAA,CAACtB,YAAY;gBAAC8C,SAAS,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACN5B,OAAA;cAAKwB,SAAS,EAAC,iBAAiB;cAAAe,QAAA,eAC9BvC,OAAA;gBAAIwB,SAAS,EAAC,8FAA8F;gBAAAe,QAAA,EAAC;cAE7G;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EAGZpB,IAAI,iBACHR,OAAA;YAAKwB,SAAS,EAAC,uCAAuC;YAAAe,QAAA,EACnDD,SAAS,CAACU,GAAG,CAAEZ,IAAI,IAAK;cACvB,MAAMa,IAAI,GAAGb,IAAI,CAACH,IAAI;cACtB,MAAMiB,QAAQ,GAAG3C,QAAQ,CAAC4C,QAAQ,KAAKf,IAAI,CAAChB,IAAI;cAChD,oBACEpB,OAAA,CAACxB,MAAM,CAAC4E,MAAM;gBAEZP,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BO,QAAQ,EAAE;kBAAEP,KAAK,EAAE;gBAAK,CAAE;gBAC1BC,OAAO,EAAEA,CAAA,KAAM5B,gBAAgB,CAACiB,IAAI,CAAChB,IAAI,CAAE;gBAC3CI,SAAS,EAAE,oGACT0B,QAAQ,GACJ,mEAAmE,GACnE,qDAAqD,EACxD;gBAAAX,QAAA,gBAEHvC,OAAA,CAACiD,IAAI;kBAACzB,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5B5B,OAAA;kBAAAuC,QAAA,EAAOH,IAAI,CAACJ;gBAAI;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAXnBQ,IAAI,CAACJ,IAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYD,CAAC;YAEpB,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAGD5B,OAAA;YAAKwB,SAAS,EAAC,6BAA6B;YAAAe,QAAA,EACzC/B,IAAI,gBACHR,OAAA,CAAAE,SAAA;cAAAqC,QAAA,gBAEEvC,OAAA;gBAAKwB,SAAS,EAAC,iBAAiB;gBAAAe,QAAA,eAC9BvC,OAAA;kBAAMwB,SAAS,EAAE,iFAAiFK,iBAAiB,CAACrB,IAAI,CAACe,IAAI,CAAC,EAAG;kBAAAgB,QAAA,GAC9HjB,WAAW,CAACd,IAAI,CAACe,IAAI,CAAC,eACvBvB,OAAA;oBAAMwB,SAAS,EAAC,YAAY;oBAAAe,QAAA,EAAE/B,IAAI,CAACe;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAGN5B,OAAA,CAACN,IAAI;gBAAC4D,EAAE,EAAC,KAAK;gBAAC9B,SAAS,EAAC,UAAU;gBAAAe,QAAA,gBACjCvC,OAAA,CAACN,IAAI,CAAC6D,MAAM;kBAAC/B,SAAS,EAAC,gFAAgF;kBAAAe,QAAA,gBACrGvC,OAAA;oBAAKwB,SAAS,EAAC,mIAAmI;oBAAAe,QAAA,GAAAlC,UAAA,GAC/IG,IAAI,CAACgD,IAAI,cAAAnD,UAAA,uBAATA,UAAA,CAAWoD,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;kBAAC;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,eACN5B,OAAA;oBAAKwB,SAAS,EAAC,2BAA2B;oBAAAe,QAAA,gBACxCvC,OAAA;sBAAGwB,SAAS,EAAC,mCAAmC;sBAAAe,QAAA,EAAE/B,IAAI,CAACgD;oBAAI;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAChE5B,OAAA;sBAAGwB,SAAS,EAAC,kCAAkC;sBAAAe,QAAA,EAAE/B,IAAI,CAACe;oBAAI;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eAEd5B,OAAA,CAACL,UAAU;kBACT2D,EAAE,EAAEjF,KAAK,CAAC4B,QAAS;kBACnB0D,KAAK,EAAC,kCAAkC;kBACxCC,SAAS,EAAC,8BAA8B;kBACxCC,OAAO,EAAC,iCAAiC;kBACzCC,KAAK,EAAC,gCAAgC;kBACtCC,SAAS,EAAC,iCAAiC;kBAC3CC,OAAO,EAAC,8BAA8B;kBAAAzB,QAAA,eAEtCvC,OAAA,CAACN,IAAI,CAACuE,KAAK;oBAACzC,SAAS,EAAC,8GAA8G;oBAAAe,QAAA,eAClIvC,OAAA;sBAAKwB,SAAS,EAAC,KAAK;sBAAAe,QAAA,gBAClBvC,OAAA,CAACN,IAAI,CAACwE,IAAI;wBAAA3B,QAAA,EACPA,CAAC;0BAAE4B;wBAAO,CAAC,kBACVnE,OAAA;0BACE+C,OAAO,EAAEA,CAAA,KAAMzC,QAAQ,CAAC,UAAU,CAAE;0BACpCkB,SAAS,EAAE,GACT2C,MAAM,GAAG,aAAa,GAAG,EAAE,4EACgD;0BAAA5B,QAAA,gBAE7EvC,OAAA,CAAClB,cAAc;4BAAC0C,SAAS,EAAC;0BAA4B;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,WAE3D;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBACT;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACQ,CAAC,eACZ5B,OAAA,CAACN,IAAI,CAACwE,IAAI;wBAAA3B,QAAA,EACPA,CAAC;0BAAE4B;wBAAO,CAAC,kBACVnE,OAAA;0BACE+C,OAAO,EAAE1B,YAAa;0BACtBG,SAAS,EAAE,GACT2C,MAAM,GAAG,aAAa,GAAG,EAAE,4EACgD;0BAAA5B,QAAA,gBAE7EvC,OAAA,CAACpB,yBAAyB;4BAAC4C,SAAS,EAAC;0BAA4B;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,YAEtE;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBACT;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAGP5B,OAAA;gBACE+C,OAAO,EAAEA,CAAA,KAAMpC,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;gBACtDc,SAAS,EAAC,8DAA8D;gBAAAe,QAAA,EAEvE7B,gBAAgB,gBACfV,OAAA,CAACd,SAAS;kBAACsC,SAAS,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE/C5B,OAAA,CAACf,SAAS;kBAACuC,SAAS,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAC/C;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA,eACT,CAAC,gBAEH5B,OAAA,CAACxB,MAAM,CAAC4E,MAAM;cACZP,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BO,QAAQ,EAAE;gBAAEP,KAAK,EAAE;cAAK,CAAE;cAC1BC,OAAO,EAAEA,CAAA,KAAMzC,QAAQ,CAAC,QAAQ,CAAE;cAClCkB,SAAS,EAAC,kKAAkK;cAAAe,QAAA,gBAE5KvC,OAAA,CAACnB,wBAAwB;gBAAC2C,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChD5B,OAAA;gBAAAuC,QAAA,EAAM;cAAO;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAChB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5B,OAAA,CAACvB,eAAe;QAAA8D,QAAA,EACb7B,gBAAgB,IAAIF,IAAI,iBACvBR,OAAA,CAACxB,MAAM,CAACoE,GAAG;UACTH,OAAO,EAAE;YAAE2B,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAE;UACnC1B,OAAO,EAAE;YAAEyB,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAO,CAAE;UACxCC,IAAI,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAE;UAChC7C,SAAS,EAAC,6CAA6C;UAAAe,QAAA,eAEvDvC,OAAA;YAAKwB,SAAS,EAAC,qBAAqB;YAAAe,QAAA,EACjCD,SAAS,CAACU,GAAG,CAAEZ,IAAI,IAAK;cACvB,MAAMa,IAAI,GAAGb,IAAI,CAACH,IAAI;cACtB,MAAMiB,QAAQ,GAAG3C,QAAQ,CAAC4C,QAAQ,KAAKf,IAAI,CAAChB,IAAI;cAChD,oBACEpB,OAAA,CAACxB,MAAM,CAAC4E,MAAM;gBAEZC,QAAQ,EAAE;kBAAEP,KAAK,EAAE;gBAAK,CAAE;gBAC1BC,OAAO,EAAEA,CAAA,KAAM5B,gBAAgB,CAACiB,IAAI,CAAChB,IAAI,CAAE;gBAC3CI,SAAS,EAAE,uFACT0B,QAAQ,GACJ,yDAAyD,GACzD,iCAAiC,EACpC;gBAAAX,QAAA,gBAEHvC,OAAA,CAACiD,IAAI;kBAACzB,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5B5B,OAAA;kBAAMwB,SAAS,EAAC,aAAa;kBAAAe,QAAA,EAAEH,IAAI,CAACJ;gBAAI;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAV3CQ,IAAI,CAACJ,IAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWD,CAAC;YAEpB,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGb5B,OAAA;MAAKwB,SAAS,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA,eAC5B,CAAC;AAEP,CAAC;AAACxB,EAAA,CAhQID,MAAM;EAAA,QACOP,WAAW,EACXC,WAAW,EACHC,OAAO;AAAA;AAAAyE,EAAA,GAH5BpE,MAAM;AAkQZ,eAAeA,MAAM;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}