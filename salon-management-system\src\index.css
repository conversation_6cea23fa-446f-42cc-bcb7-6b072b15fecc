@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* Base Styles */
body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #fafafa;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Modern Button Styles */
.btn-modern {
  @apply px-6 py-3 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-md hover:shadow-lg;
}

.btn-secondary {
  @apply bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500 shadow-md hover:shadow-lg;
}

.btn-accent {
  @apply bg-accent-600 text-white hover:bg-accent-700 focus:ring-accent-500 shadow-md hover:shadow-lg;
}

.btn-outline {
  @apply border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white focus:ring-primary-500;
}

/* Modern Card Styles */
.card-modern {
  @apply bg-white rounded-xl shadow-soft hover:shadow-medium transition-all duration-300 border border-gray-100;
}

.card-glass {
  @apply bg-white/80 backdrop-blur-sm rounded-xl shadow-soft hover:shadow-medium transition-all duration-300 border border-white/20;
}

/* Modern Input Styles */
.input-modern {
  @apply w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200 bg-white;
}

/* Loading Animation */
.loading-spinner {
  @apply animate-spin rounded-full border-4 border-gray-200 border-t-primary-600;
}

/* Gradient Backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
}

.gradient-accent {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

/* React Big Calendar Styles */
.rbc-calendar {
  font-family: inherit;
}

.rbc-header {
  padding: 8px 6px;
  font-weight: 500;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.rbc-event {
  border-radius: 4px;
  padding: 2px 5px;
  font-size: 12px;
  line-height: 1.2;
}

.rbc-time-view .rbc-time-gutter {
  background-color: #f9f9f9;
}

.rbc-time-slot {
  border-top: 1px solid #f0f0f0;
}

.rbc-today {
  background-color: rgba(142, 36, 170, 0.1);
}

.rbc-current-time-indicator {
  background-color: #8e24aa;
  height: 2px;
}
