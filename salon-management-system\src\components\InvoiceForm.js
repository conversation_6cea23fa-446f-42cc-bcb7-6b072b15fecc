import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  DocumentTextIcon,
  UserIcon,
  CurrencyDollarIcon,
  PlusIcon,
  TrashIcon,
  CalendarDaysIcon,
  ClipboardDocumentListIcon,
  ReceiptPercentIcon,
  BanknotesIcon,
  CheckCircleIcon,
  PhoneIcon,
  EnvelopeIcon
} from '@heroicons/react/24/outline';
import { Button, Card, Input, Modal } from './ui';
import { useBilling } from '../contexts/BillingContext';

const InvoiceForm = ({ open, onClose, invoice = null, mode = 'add' }) => {
  const { createInvoice, updateInvoice, validateDiscount } = useBilling();
  
  const [formData, setFormData] = useState({
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    services: [
      {
        name: '',
        price: 0,
        stylist: '',
        duration: 60
      }
    ],
    discountType: null,
    discountValue: 0,
    taxRate: 8.5,
    notes: ''
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Sample services for autocomplete
  const availableServices = [
    { name: 'Hair Cut & Style', price: 85, duration: 60 },
    { name: 'Hair Color', price: 120, duration: 90 },
    { name: 'Hair Highlights', price: 150, duration: 120 },
    { name: 'Beard Trim', price: 35, duration: 30 },
    { name: 'Facial Treatment', price: 95, duration: 75 },
    { name: 'Manicure', price: 45, duration: 45 },
    { name: 'Pedicure', price: 55, duration: 60 },
    { name: 'Hair Wash & Blow Dry', price: 40, duration: 30 },
    { name: 'Deep Conditioning', price: 65, duration: 45 },
    { name: 'Eyebrow Shaping', price: 25, duration: 20 }
  ];

  // Sample stylists
  const availableStylists = [
    'Emma Wilson',
    'John Smith',
    'Sarah Davis',
    'Mike Johnson',
    'Lisa Brown'
  ];

  useEffect(() => {
    if (invoice && mode === 'edit') {
      setFormData({
        customerName: invoice.customerName || '',
        customerEmail: invoice.customerEmail || '',
        customerPhone: invoice.customerPhone || '',
        services: invoice.services || [{ name: '', price: 0, stylist: '', duration: 60 }],
        discountType: invoice.discountType || null,
        discountValue: invoice.discountValue || 0,
        taxRate: invoice.taxRate || 8.5,
        notes: invoice.notes || ''
      });
    } else {
      // Reset form for add mode
      setFormData({
        customerName: '',
        customerEmail: '',
        customerPhone: '',
        services: [{ name: '', price: 0, stylist: '', duration: 60 }],
        discountType: null,
        discountValue: 0,
        taxRate: 8.5,
        notes: ''
      });
    }
    setErrors({});
  }, [invoice, mode, open]);

  const handleChange = (field) => (event) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleServiceChange = (index, field, value) => {
    const updatedServices = [...formData.services];
    updatedServices[index] = {
      ...updatedServices[index],
      [field]: value
    };
    
    // Auto-fill price and duration when service is selected
    if (field === 'name') {
      const selectedService = availableServices.find(s => s.name === value);
      if (selectedService) {
        updatedServices[index].price = selectedService.price;
        updatedServices[index].duration = selectedService.duration;
      }
    }
    
    setFormData(prev => ({
      ...prev,
      services: updatedServices
    }));
  };

  const addService = () => {
    setFormData(prev => ({
      ...prev,
      services: [...prev.services, { name: '', price: 0, stylist: '', duration: 60 }]
    }));
  };

  const removeService = (index) => {
    if (formData.services.length > 1) {
      setFormData(prev => ({
        ...prev,
        services: prev.services.filter((_, i) => i !== index)
      }));
    }
  };

  const calculateTotals = () => {
    const subtotal = formData.services.reduce((sum, service) =>
      sum + service.price, 0
    );
    
    let discountAmount = 0;
    if (formData.discountType === 'percentage') {
      discountAmount = (subtotal * formData.discountValue) / 100;
    } else if (formData.discountType === 'fixed') {
      discountAmount = formData.discountValue;
    }
    
    const afterDiscount = subtotal - discountAmount;
    const taxAmount = (afterDiscount * formData.taxRate) / 100;
    const total = afterDiscount + taxAmount;
    
    return {
      subtotal: parseFloat(subtotal.toFixed(2)),
      discountAmount: parseFloat(discountAmount.toFixed(2)),
      taxAmount: parseFloat(taxAmount.toFixed(2)),
      total: parseFloat(total.toFixed(2))
    };
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.customerName.trim()) {
      newErrors.customerName = 'Customer name is required';
    }

    if (!formData.customerEmail.trim()) {
      newErrors.customerEmail = 'Customer email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.customerEmail)) {
      newErrors.customerEmail = 'Please enter a valid email address';
    }

    if (!formData.customerPhone.trim()) {
      newErrors.customerPhone = 'Customer phone is required';
    }

    // Validate services
    formData.services.forEach((service, index) => {
      if (!service.name.trim()) {
        newErrors[`service_${index}_name`] = 'Service name is required';
      }
      if (service.price <= 0) {
        newErrors[`service_${index}_price`] = 'Service price must be greater than 0';
      }
      if (!service.stylist.trim()) {
        newErrors[`service_${index}_stylist`] = 'Stylist is required';
      }
    });

    if (formData.taxRate < 0) {
      newErrors.taxRate = 'Tax rate cannot be negative';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const totals = calculateTotals();
      const invoiceData = {
        ...formData,
        ...totals,
        customerId: Math.floor(Math.random() * 1000), // In real app, this would be from customer selection
        services: formData.services.map(service => ({
          ...service,
          price: Number(service.price),
          duration: Number(service.duration)
        })),
        discountValue: Number(formData.discountValue),
        taxRate: Number(formData.taxRate)
      };

      if (mode === 'edit' && invoice) {
        updateInvoice(invoice.id, invoiceData);
      } else {
        createInvoice(invoiceData);
      }

      onClose();
    } catch (error) {
      console.error('Error saving invoice:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const totals = calculateTotals();

  return (
    <Modal
      isOpen={open}
      onClose={handleClose}
      title={
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg">
            <DocumentTextIcon className="h-6 w-6 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {mode === 'edit' ? 'Edit Invoice' : 'Create New Invoice'}
            </h2>
            <p className="text-sm text-gray-500">
              {mode === 'edit' ? 'Update invoice information' : 'Create a new invoice for your customer'}
            </p>
          </div>
        </div>
      }
      size="xl"
    >
      <div className="space-y-8">
        {/* Customer Information Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <div className="p-6">
              <div className="flex items-center space-x-2 mb-6">
                <UserIcon className="h-5 w-5 text-blue-600" />
                <h3 className="text-lg font-semibold text-gray-900">Customer Information</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <Input
                    label="Customer Name"
                    value={formData.customerName}
                    onChange={handleChange('customerName')}
                    error={errors.customerName}
                    required
                    placeholder="Enter customer name"
                    icon={<UserIcon className="h-5 w-5" />}
                  />
                </div>

                <div>
                  <Input
                    label="Email Address"
                    type="email"
                    value={formData.customerEmail}
                    onChange={handleChange('customerEmail')}
                    error={errors.customerEmail}
                    required
                    placeholder="<EMAIL>"
                    icon={<EnvelopeIcon className="h-5 w-5" />}
                  />
                </div>

                <div>
                  <Input
                    label="Phone Number"
                    value={formData.customerPhone}
                    onChange={handleChange('customerPhone')}
                    error={errors.customerPhone}
                    required
                    placeholder="(*************"
                    icon={<PhoneIcon className="h-5 w-5" />}
                  />
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Services Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-2">
                  <ClipboardDocumentListIcon className="h-5 w-5 text-green-600" />
                  <h3 className="text-lg font-semibold text-gray-900">Services</h3>
                </div>
                <Button
                  variant="outline"
                  onClick={addService}
                  className="flex items-center space-x-2"
                >
                  <PlusIcon className="h-4 w-4" />
                  <span>Add Service</span>
                </Button>
              </div>

              {/* Services Table */}
              <div className="mt-6">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Service
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Stylist
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Price ($)
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Duration (min)
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {formData.services.map((service, index) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <input
                              type="text"
                              value={service.name}
                              onChange={(e) => handleServiceChange(index, 'name', e.target.value)}
                              placeholder="Service name"
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <input
                              type="text"
                              value={service.stylist}
                              onChange={(e) => handleServiceChange(index, 'stylist', e.target.value)}
                              placeholder="Stylist name"
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="relative">
                              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span className="text-gray-500 sm:text-sm">$</span>
                              </div>
                              <input
                                type="number"
                                value={service.price}
                                onChange={(e) => handleServiceChange(index, 'price', Number(e.target.value))}
                                placeholder="0.00"
                                min="0"
                                step="0.01"
                                className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <input
                              type="number"
                              value={service.duration}
                              onChange={(e) => handleServiceChange(index, 'duration', Number(e.target.value))}
                              placeholder="Duration"
                              min="1"
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <button
                              onClick={() => removeService(index)}
                              disabled={formData.services.length === 1}
                              className="text-red-600 hover:text-red-900 p-1 disabled:opacity-50"
                            >
                              <TrashIcon className="h-5 w-5" />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Discount Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <div className="p-6">
              <div className="flex items-center space-x-2 mb-6">
                <ReceiptPercentIcon className="h-5 w-5 text-purple-600" />
                <h3 className="text-lg font-semibold text-gray-900">Discount & Payment</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <Input
                    label="Discount Code"
                    value={formData.discountCode}
                    onChange={handleChange('discountCode')}
                    placeholder="Enter discount code"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tax Rate (%)
                  </label>
                  <input
                    type="number"
                    value={formData.taxRate}
                    onChange={handleChange('taxRate')}
                    min="0"
                    max="100"
                    step="0.1"
                    placeholder="8.5"
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Payment Method
                  </label>
                  <select
                    value={formData.paymentMethod}
                    onChange={handleChange('paymentMethod')}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 bg-white"
                  >
                    <option value="cash">Cash</option>
                    <option value="card">Credit/Debit Card</option>
                    <option value="bank_transfer">Bank Transfer</option>
                    <option value="check">Check</option>
                  </select>
                </div>
              </div>

              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Notes
                </label>
                <textarea
                  value={formData.notes}
                  onChange={handleChange('notes')}
                  rows={3}
                  placeholder="Additional notes for this invoice..."
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 resize-none"
                />
              </div>
            </div>
          </Card>
        </motion.div>
      </div>

      <Modal.Footer>
        <div className="flex justify-between items-center w-full">
          <Button variant="outline" onClick={handleClose} disabled={isSubmitting}>
            Cancel
          </Button>

          <div className="text-right">
            <div className="space-y-1 mb-4">
              <p className="text-sm text-gray-600">
                Subtotal: {formatCurrency(totals.subtotal)}
              </p>
              {totals.discountAmount > 0 && (
                <p className="text-sm text-green-600">
                  Discount: -{formatCurrency(totals.discountAmount)}
                </p>
              )}
              <p className="text-sm text-gray-600">
                Tax ({formData.taxRate}%): {formatCurrency(totals.taxAmount)}
              </p>
              <p className="text-lg font-bold text-gray-900">
                Total: {formatCurrency(totals.total)}
              </p>
            </div>

            <Button
              variant="primary"
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:shadow-lg"
            >
              {isSubmitting ? 'Saving...' : (mode === 'edit' ? 'Update Invoice' : 'Create Invoice')}
            </Button>
          </div>
        </div>
      </Modal.Footer>
    </Modal>
  );
};

export default InvoiceForm;
