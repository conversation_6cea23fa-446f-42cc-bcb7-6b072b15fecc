// Currency Utility Functions for Indian Rupees (INR)
// Conversion rate: 1 USD = 83 INR (approximate)

const USD_TO_INR_RATE = 83;

/**
 * Convert USD amount to INR
 * @param {number} usdAmount - Amount in USD
 * @returns {number} Amount in INR
 */
export const convertUSDToINR = (usdAmount) => {
  return Math.round(usdAmount * USD_TO_INR_RATE);
};

/**
 * Format currency value in Indian Rupees
 * @param {number} amount - The amount to format
 * @param {boolean} showSymbol - Whether to show the ₹ symbol (default: true)
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (amount, showSymbol = true) => {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return showSymbol ? '₹0' : '0';
  }

  const formatter = new Intl.NumberFormat('en-IN', {
    style: showSymbol ? 'currency' : 'decimal',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });

  if (showSymbol) {
    return formatter.format(amount);
  } else {
    return formatter.format(amount).replace(/[₹\s]/g, '');
  }
};

/**
 * Format currency with compact notation for large amounts
 * @param {number} amount - The amount to format
 * @returns {string} Formatted currency string with compact notation
 */
export const formatCompactCurrency = (amount) => {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return '₹0';
  }

  if (amount >= 10000000) { // 1 Crore
    return `₹${(amount / 10000000).toFixed(1)}Cr`;
  } else if (amount >= 100000) { // 1 Lakh
    return `₹${(amount / 100000).toFixed(1)}L`;
  } else if (amount >= 1000) { // 1 Thousand
    return `₹${(amount / 1000).toFixed(1)}K`;
  }

  return formatCurrency(amount);
};

/**
 * Parse currency string to number
 * @param {string} currencyString - Currency string to parse
 * @returns {number} Parsed amount
 */
export const parseCurrency = (currencyString) => {
  if (!currencyString) return 0;
  
  // Remove currency symbols and spaces
  const cleanString = currencyString.toString().replace(/[₹,\s]/g, '');
  const amount = parseFloat(cleanString);
  
  return isNaN(amount) ? 0 : amount;
};

/**
 * Convert and format USD amount to INR
 * @param {number} usdAmount - Amount in USD
 * @param {boolean} showSymbol - Whether to show the ₹ symbol (default: true)
 * @returns {string} Formatted INR amount
 */
export const convertAndFormatUSDToINR = (usdAmount, showSymbol = true) => {
  const inrAmount = convertUSDToINR(usdAmount);
  return formatCurrency(inrAmount, showSymbol);
};

/**
 * Get currency symbol
 * @returns {string} Indian Rupee symbol
 */
export const getCurrencySymbol = () => '₹';

/**
 * Get currency code
 * @returns {string} Indian Rupee code
 */
export const getCurrencyCode = () => 'INR';

/**
 * Format amount for input fields (without currency symbol)
 * @param {number} amount - The amount to format
 * @returns {string} Formatted amount string
 */
export const formatAmountForInput = (amount) => {
  return formatCurrency(amount, false);
};

/**
 * Validate currency amount
 * @param {string|number} amount - Amount to validate
 * @returns {boolean} Whether the amount is valid
 */
export const isValidCurrencyAmount = (amount) => {
  const parsed = parseCurrency(amount);
  return !isNaN(parsed) && parsed >= 0;
};

/**
 * Round amount to 2 decimal places
 * @param {number} amount - Amount to round
 * @returns {number} Rounded amount
 */
export const roundAmount = (amount) => {
  return Math.round((amount + Number.EPSILON) * 100) / 100;
};

/**
 * Calculate percentage of amount
 * @param {number} amount - Base amount
 * @param {number} percentage - Percentage to calculate
 * @returns {number} Calculated percentage amount
 */
export const calculatePercentage = (amount, percentage) => {
  return roundAmount((amount * percentage) / 100);
};

/**
 * Add tax to amount
 * @param {number} amount - Base amount
 * @param {number} taxRate - Tax rate percentage
 * @returns {object} Object with subtotal, tax amount, and total
 */
export const addTax = (amount, taxRate = 18) => {
  const subtotal = roundAmount(amount);
  const taxAmount = calculatePercentage(subtotal, taxRate);
  const total = roundAmount(subtotal + taxAmount);
  
  return {
    subtotal,
    taxAmount,
    total
  };
};

/**
 * Apply discount to amount
 * @param {number} amount - Base amount
 * @param {number} discount - Discount amount or percentage
 * @param {string} type - 'fixed' or 'percentage'
 * @returns {object} Object with original amount, discount amount, and final amount
 */
export const applyDiscount = (amount, discount, type = 'fixed') => {
  const originalAmount = roundAmount(amount);
  let discountAmount = 0;
  
  if (type === 'percentage') {
    discountAmount = calculatePercentage(originalAmount, discount);
  } else {
    discountAmount = roundAmount(discount);
  }
  
  const finalAmount = roundAmount(originalAmount - discountAmount);
  
  return {
    originalAmount,
    discountAmount,
    finalAmount
  };
};

// Export default object with all functions
export default {
  convertUSDToINR,
  formatCurrency,
  formatCompactCurrency,
  parseCurrency,
  convertAndFormatUSDToINR,
  getCurrencySymbol,
  getCurrencyCode,
  formatAmountForInput,
  isValidCurrencyAmount,
  roundAmount,
  calculatePercentage,
  addTax,
  applyDiscount,
  USD_TO_INR_RATE
};
