{"ast": null, "code": "import jsPDF from 'jspdf';\nexport const generateInvoicePDF = invoice => {\n  const pdf = new jsPDF();\n\n  // Set font\n  pdf.setFont('helvetica');\n\n  // Company Header\n  pdf.setFontSize(20);\n  pdf.setTextColor(40, 40, 40);\n  pdf.text('Salon Management System', 20, 30);\n  pdf.setFontSize(10);\n  pdf.setTextColor(100, 100, 100);\n  pdf.text('123 Beauty Street', 20, 40);\n  pdf.text('City, State 12345', 20, 45);\n  pdf.text('Phone: (*************', 20, 50);\n  pdf.text('Email: <EMAIL>', 20, 55);\n\n  // Invoice Title and Number\n  pdf.setFontSize(24);\n  pdf.setTextColor(40, 40, 40);\n  pdf.text('INVOICE', 150, 30);\n  pdf.setFontSize(14);\n  pdf.setTextColor(60, 60, 60);\n  pdf.text(invoice.id, 150, 40);\n\n  // Invoice Details\n  pdf.setFontSize(10);\n  pdf.text(`Date: ${invoice.date}`, 150, 50);\n  pdf.text(`Due Date: ${invoice.dueDate}`, 150, 55);\n  pdf.text(`Status: ${invoice.status.toUpperCase()}`, 150, 60);\n\n  // Customer Information\n  pdf.setFontSize(12);\n  pdf.setTextColor(40, 40, 40);\n  pdf.text('Bill To:', 20, 80);\n  pdf.setFontSize(10);\n  pdf.setTextColor(60, 60, 60);\n  pdf.text(invoice.customerName, 20, 90);\n  pdf.text(invoice.customerEmail, 20, 95);\n  pdf.text(invoice.customerPhone, 20, 100);\n\n  // Services Table Header\n  const tableStartY = 120;\n  pdf.setFontSize(10);\n  pdf.setTextColor(40, 40, 40);\n\n  // Table headers\n  pdf.text('Service', 20, tableStartY);\n  pdf.text('Stylist', 80, tableStartY);\n  pdf.text('Price', 130, tableStartY);\n  pdf.text('Duration', 160, tableStartY);\n\n  // Draw header line\n  pdf.setDrawColor(200, 200, 200);\n  pdf.line(20, tableStartY + 2, 180, tableStartY + 2);\n\n  // Services Table Content\n  let currentY = tableStartY + 10;\n  pdf.setFontSize(9);\n  pdf.setTextColor(60, 60, 60);\n  invoice.services.forEach((service, index) => {\n    pdf.text(service.name, 20, currentY);\n    pdf.text(service.stylist, 80, currentY);\n    pdf.text(`₹${service.price.toLocaleString('en-IN')}`, 130, currentY);\n    pdf.text(`${service.duration} min`, 160, currentY);\n    currentY += 8;\n\n    // Add page break if needed\n    if (currentY > 250) {\n      pdf.addPage();\n      currentY = 30;\n    }\n  });\n\n  // Draw line before totals\n  pdf.setDrawColor(200, 200, 200);\n  pdf.line(130, currentY + 2, 180, currentY + 2);\n\n  // Totals Section\n  currentY += 15;\n  pdf.setFontSize(10);\n  pdf.setTextColor(60, 60, 60);\n\n  // Subtotal\n  pdf.text('Subtotal:', 130, currentY);\n  pdf.text(`₹${invoice.subtotal.toLocaleString('en-IN')}`, 175, currentY);\n  currentY += 8;\n\n  // Discount (if applicable)\n  if (invoice.discountAmount > 0) {\n    pdf.setTextColor(0, 150, 0);\n    pdf.text(`Discount (${invoice.discountType === 'percentage' ? `${invoice.discountValue}%` : 'Fixed'}):`, 130, currentY);\n    pdf.text(`-₹${invoice.discountAmount.toLocaleString('en-IN')}`, 175, currentY);\n    currentY += 8;\n    pdf.setTextColor(60, 60, 60);\n  }\n\n  // Tax (GST)\n  pdf.text(`GST (${invoice.taxRate}%):`, 130, currentY);\n  pdf.text(`₹${invoice.taxAmount.toLocaleString('en-IN')}`, 175, currentY);\n  currentY += 8;\n\n  // Total\n  pdf.setDrawColor(40, 40, 40);\n  pdf.line(130, currentY, 180, currentY);\n  currentY += 8;\n  pdf.setFontSize(12);\n  pdf.setTextColor(40, 40, 40);\n  pdf.text('Total:', 130, currentY);\n  pdf.text(`₹${invoice.total.toLocaleString('en-IN')}`, 175, currentY);\n\n  // Payment Information (if paid)\n  if (invoice.status === 'paid' && invoice.paymentMethod) {\n    currentY += 20;\n    pdf.setFontSize(10);\n    pdf.setTextColor(0, 150, 0);\n    pdf.text('Payment Information:', 20, currentY);\n    currentY += 8;\n    pdf.setTextColor(60, 60, 60);\n    pdf.text(`Method: ${invoice.paymentMethod.toUpperCase()}`, 20, currentY);\n    if (invoice.paymentDate) {\n      currentY += 6;\n      pdf.text(`Date: ${invoice.paymentDate}`, 20, currentY);\n    }\n    if (invoice.transactionId) {\n      currentY += 6;\n      pdf.text(`Transaction ID: ${invoice.transactionId}`, 20, currentY);\n    }\n  }\n\n  // Notes (if any)\n  if (invoice.notes) {\n    currentY += 20;\n    pdf.setFontSize(10);\n    pdf.setTextColor(40, 40, 40);\n    pdf.text('Notes:', 20, currentY);\n    currentY += 8;\n    pdf.setTextColor(60, 60, 60);\n\n    // Split notes into multiple lines if too long\n    const splitNotes = pdf.splitTextToSize(invoice.notes, 170);\n    splitNotes.forEach(line => {\n      pdf.text(line, 20, currentY);\n      currentY += 6;\n    });\n  }\n\n  // Footer\n  const pageHeight = pdf.internal.pageSize.height;\n  pdf.setFontSize(8);\n  pdf.setTextColor(150, 150, 150);\n  pdf.text('Thank you for your business!', 20, pageHeight - 20);\n  pdf.text(`Generated on ${new Date().toLocaleDateString()}`, 20, pageHeight - 15);\n\n  // Save the PDF\n  pdf.save(`invoice-${invoice.id}.pdf`);\n};\nexport const generateInvoicePreview = invoice => {\n  const pdf = new jsPDF();\n\n  // Use the same generation logic as above\n  // This function can be used to generate a preview without downloading\n\n  // Return the PDF as a blob for preview\n  return pdf.output('blob');\n};\nexport const exportInvoicesPDF = invoices => {\n  const pdf = new jsPDF();\n\n  // Title page\n  pdf.setFontSize(24);\n  pdf.setTextColor(40, 40, 40);\n  pdf.text('Invoice Export Report', 20, 30);\n  pdf.setFontSize(12);\n  pdf.setTextColor(60, 60, 60);\n  pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 45);\n  pdf.text(`Total Invoices: ${invoices.length}`, 20, 55);\n\n  // Calculate totals\n  const totalAmount = invoices.reduce((sum, inv) => sum + inv.total, 0);\n  const paidAmount = invoices.filter(inv => inv.status === 'paid').reduce((sum, inv) => sum + inv.total, 0);\n  const pendingAmount = invoices.filter(inv => inv.status === 'pending').reduce((sum, inv) => sum + inv.total, 0);\n  pdf.text(`Total Amount: ₹${totalAmount.toLocaleString('en-IN')}`, 20, 65);\n  pdf.text(`Paid Amount: ₹${paidAmount.toLocaleString('en-IN')}`, 20, 75);\n  pdf.text(`Pending Amount: ₹${pendingAmount.toLocaleString('en-IN')}`, 20, 85);\n\n  // Summary table header\n  let currentY = 110;\n  pdf.setFontSize(14);\n  pdf.setTextColor(40, 40, 40);\n  pdf.text('Invoice Summary', 20, currentY);\n  currentY += 15;\n  pdf.setFontSize(10);\n\n  // Table headers\n  pdf.text('Invoice ID', 20, currentY);\n  pdf.text('Customer', 60, currentY);\n  pdf.text('Date', 110, currentY);\n  pdf.text('Status', 140, currentY);\n  pdf.text('Amount', 170, currentY);\n\n  // Draw header line\n  pdf.setDrawColor(200, 200, 200);\n  pdf.line(20, currentY + 2, 190, currentY + 2);\n  currentY += 10;\n\n  // Invoice rows\n  pdf.setFontSize(9);\n  pdf.setTextColor(60, 60, 60);\n  invoices.forEach((invoice, index) => {\n    if (currentY > 270) {\n      pdf.addPage();\n      currentY = 30;\n\n      // Repeat headers on new page\n      pdf.setFontSize(10);\n      pdf.setTextColor(40, 40, 40);\n      pdf.text('Invoice ID', 20, currentY);\n      pdf.text('Customer', 60, currentY);\n      pdf.text('Date', 110, currentY);\n      pdf.text('Status', 140, currentY);\n      pdf.text('Amount', 170, currentY);\n      pdf.setDrawColor(200, 200, 200);\n      pdf.line(20, currentY + 2, 190, currentY + 2);\n      currentY += 10;\n      pdf.setFontSize(9);\n      pdf.setTextColor(60, 60, 60);\n    }\n    pdf.text(invoice.id, 20, currentY);\n    pdf.text(invoice.customerName.substring(0, 20), 60, currentY);\n    pdf.text(invoice.date, 110, currentY);\n\n    // Color code status\n    if (invoice.status === 'paid') {\n      pdf.setTextColor(0, 150, 0);\n    } else if (invoice.status === 'pending') {\n      pdf.setTextColor(255, 140, 0);\n    } else if (invoice.status === 'overdue') {\n      pdf.setTextColor(220, 20, 60);\n    }\n    pdf.text(invoice.status.toUpperCase(), 140, currentY);\n    pdf.setTextColor(60, 60, 60);\n    pdf.text(`₹${invoice.total.toLocaleString('en-IN')}`, 170, currentY);\n    currentY += 8;\n  });\n\n  // Footer\n  const pageHeight = pdf.internal.pageSize.height;\n  pdf.setFontSize(8);\n  pdf.setTextColor(150, 150, 150);\n  pdf.text('Salon Management System - Invoice Export', 20, pageHeight - 20);\n  pdf.text(`Generated on ${new Date().toLocaleDateString()}`, 20, pageHeight - 15);\n\n  // Save the PDF\n  pdf.save(`invoices-export-${new Date().toISOString().split('T')[0]}.pdf`);\n};\nexport const printInvoice = invoice => {\n  const pdf = new jsPDF();\n\n  // Generate PDF with same logic as generateInvoicePDF\n  // But open in new window for printing instead of downloading\n\n  const pdfUrl = pdf.output('bloburl');\n  const printWindow = window.open(pdfUrl);\n  if (printWindow) {\n    printWindow.onload = () => {\n      printWindow.print();\n    };\n  }\n};\n\n// Inventory PDF Export Functions\nexport const exportInventoryPDF = (products, options = {}) => {\n  const pdf = new jsPDF();\n\n  // Set font\n  pdf.setFont('helvetica');\n\n  // Company Header\n  pdf.setFontSize(20);\n  pdf.setTextColor(40, 40, 40);\n  pdf.text('Salon Management System', 20, 30);\n  pdf.setFontSize(10);\n  pdf.setTextColor(100, 100, 100);\n  pdf.text('123 Beauty Street', 20, 40);\n  pdf.text('City, State 12345', 20, 45);\n  pdf.text('Phone: (*************', 20, 50);\n  pdf.text('Email: <EMAIL>', 20, 55);\n\n  // Report Title\n  pdf.setFontSize(24);\n  pdf.setTextColor(40, 40, 40);\n  pdf.text('INVENTORY REPORT', 20, 80);\n  pdf.setFontSize(12);\n  pdf.setTextColor(60, 60, 60);\n  pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 95);\n  pdf.text(`Total Products: ${products.length}`, 20, 105);\n\n  // Calculate totals\n  const totalValue = products.reduce((sum, product) => sum + product.currentStock * product.unitPrice, 0);\n  const lowStockCount = products.filter(p => p.currentStock <= p.minStockLevel).length;\n  const outOfStockCount = products.filter(p => p.currentStock === 0).length;\n  pdf.text(`Total Inventory Value: ₹${totalValue.toLocaleString('en-IN')}`, 20, 115);\n  pdf.text(`Low Stock Items: ${lowStockCount}`, 20, 125);\n  pdf.text(`Out of Stock Items: ${outOfStockCount}`, 20, 135);\n\n  // Table headers\n  let yPosition = 160;\n  pdf.setFontSize(10);\n  pdf.setTextColor(40, 40, 40);\n  pdf.setFont('helvetica', 'bold');\n  const headers = ['Product Name', 'SKU', 'Category', 'Stock', 'Min/Max', 'Unit Price', 'Total Value', 'Status'];\n  const columnWidths = [35, 25, 25, 15, 20, 20, 25, 15];\n  let xPosition = 20;\n  headers.forEach((header, index) => {\n    pdf.text(header, xPosition, yPosition);\n    xPosition += columnWidths[index];\n  });\n\n  // Draw header line\n  pdf.line(20, yPosition + 2, 200, yPosition + 2);\n\n  // Table data\n  pdf.setFont('helvetica', 'normal');\n  pdf.setFontSize(8);\n  yPosition += 10;\n  products.forEach((product, index) => {\n    if (yPosition > 270) {\n      pdf.addPage();\n      yPosition = 30;\n\n      // Repeat headers on new page\n      pdf.setFont('helvetica', 'bold');\n      pdf.setFontSize(10);\n      xPosition = 20;\n      headers.forEach((header, index) => {\n        pdf.text(header, xPosition, yPosition);\n        xPosition += columnWidths[index];\n      });\n      pdf.line(20, yPosition + 2, 200, yPosition + 2);\n      pdf.setFont('helvetica', 'normal');\n      pdf.setFontSize(8);\n      yPosition += 10;\n    }\n    xPosition = 20;\n    const rowData = [product.name.substring(0, 20) + (product.name.length > 20 ? '...' : ''), product.sku, product.category.substring(0, 15) + (product.category.length > 15 ? '...' : ''), product.currentStock.toString(), `${product.minStockLevel}/${product.maxStockLevel}`, `₹${product.unitPrice.toLocaleString('en-IN')}`, `₹${(product.currentStock * product.unitPrice).toLocaleString('en-IN')}`, product.currentStock === 0 ? 'Out' : product.currentStock <= product.minStockLevel ? 'Low' : 'OK'];\n\n    // Set color based on stock status\n    if (product.currentStock === 0) {\n      pdf.setTextColor(220, 53, 69); // Red for out of stock\n    } else if (product.currentStock <= product.minStockLevel) {\n      pdf.setTextColor(255, 193, 7); // Yellow for low stock\n    } else {\n      pdf.setTextColor(40, 40, 40); // Normal color\n    }\n    rowData.forEach((data, index) => {\n      pdf.text(data, xPosition, yPosition);\n      xPosition += columnWidths[index];\n    });\n    yPosition += 8;\n  });\n\n  // Footer\n  const pageHeight = pdf.internal.pageSize.height;\n  pdf.setFontSize(8);\n  pdf.setTextColor(150, 150, 150);\n  pdf.text('Salon Management System - Inventory Report', 20, pageHeight - 20);\n  pdf.text(`Generated on ${new Date().toLocaleDateString()}`, 20, pageHeight - 15);\n\n  // Save the PDF\n  pdf.save(`inventory-report-${new Date().toISOString().split('T')[0]}.pdf`);\n};\nexport const exportLowStockPDF = products => {\n  const lowStockProducts = products.filter(p => p.currentStock <= p.minStockLevel);\n  const pdf = new jsPDF();\n  pdf.setFont('helvetica');\n\n  // Header\n  pdf.setFontSize(20);\n  pdf.setTextColor(40, 40, 40);\n  pdf.text('Low Stock Alert Report', 20, 30);\n  pdf.setFontSize(12);\n  pdf.setTextColor(60, 60, 60);\n  pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 45);\n  pdf.text(`Low Stock Items: ${lowStockProducts.length}`, 20, 55);\n  let yPosition = 80;\n  lowStockProducts.forEach((product, index) => {\n    if (yPosition > 250) {\n      pdf.addPage();\n      yPosition = 30;\n    }\n    pdf.setFontSize(14);\n    pdf.setTextColor(220, 53, 69);\n    pdf.text(`${index + 1}. ${product.name}`, 20, yPosition);\n    pdf.setFontSize(10);\n    pdf.setTextColor(60, 60, 60);\n    pdf.text(`SKU: ${product.sku}`, 30, yPosition + 10);\n    pdf.text(`Current Stock: ${product.currentStock}`, 30, yPosition + 20);\n    pdf.text(`Minimum Level: ${product.minStockLevel}`, 30, yPosition + 30);\n    pdf.text(`Supplier: ${product.supplier}`, 30, yPosition + 40);\n    yPosition += 60;\n  });\n  pdf.save(`low-stock-report-${new Date().toISOString().split('T')[0]}.pdf`);\n};", "map": {"version": 3, "names": ["jsPDF", "generateInvoicePDF", "invoice", "pdf", "setFont", "setFontSize", "setTextColor", "text", "id", "date", "dueDate", "status", "toUpperCase", "customerName", "customerEmail", "customerPhone", "tableStartY", "setDrawColor", "line", "currentY", "services", "for<PERSON>ach", "service", "index", "name", "stylist", "price", "toLocaleString", "duration", "addPage", "subtotal", "discountAmount", "discountType", "discountValue", "taxRate", "taxAmount", "total", "paymentMethod", "paymentDate", "transactionId", "notes", "splitNotes", "splitTextToSize", "pageHeight", "internal", "pageSize", "height", "Date", "toLocaleDateString", "save", "generateInvoicePreview", "output", "exportInvoicesPDF", "invoices", "length", "totalAmount", "reduce", "sum", "inv", "paidAmount", "filter", "pendingAmount", "substring", "toISOString", "split", "printInvoice", "pdfUrl", "printWindow", "window", "open", "onload", "print", "exportInventoryPDF", "products", "options", "totalValue", "product", "currentStock", "unitPrice", "lowStockCount", "p", "minStockLevel", "outOfStockCount", "yPosition", "headers", "columnWidths", "xPosition", "header", "rowData", "sku", "category", "toString", "maxStockLevel", "data", "exportLowStockPDF", "lowStockProducts", "supplier"], "sources": ["D:/Project/salon-management-system/src/utils/pdfGenerator.js"], "sourcesContent": ["import jsPDF from 'jspdf';\n\nexport const generateInvoicePDF = (invoice) => {\n  const pdf = new jsPDF();\n  \n  // Set font\n  pdf.setFont('helvetica');\n  \n  // Company Header\n  pdf.setFontSize(20);\n  pdf.setTextColor(40, 40, 40);\n  pdf.text('Salon Management System', 20, 30);\n  \n  pdf.setFontSize(10);\n  pdf.setTextColor(100, 100, 100);\n  pdf.text('123 Beauty Street', 20, 40);\n  pdf.text('City, State 12345', 20, 45);\n  pdf.text('Phone: (*************', 20, 50);\n  pdf.text('Email: <EMAIL>', 20, 55);\n  \n  // Invoice Title and Number\n  pdf.setFontSize(24);\n  pdf.setTextColor(40, 40, 40);\n  pdf.text('INVOICE', 150, 30);\n  \n  pdf.setFontSize(14);\n  pdf.setTextColor(60, 60, 60);\n  pdf.text(invoice.id, 150, 40);\n  \n  // Invoice Details\n  pdf.setFontSize(10);\n  pdf.text(`Date: ${invoice.date}`, 150, 50);\n  pdf.text(`Due Date: ${invoice.dueDate}`, 150, 55);\n  pdf.text(`Status: ${invoice.status.toUpperCase()}`, 150, 60);\n  \n  // Customer Information\n  pdf.setFontSize(12);\n  pdf.setTextColor(40, 40, 40);\n  pdf.text('Bill To:', 20, 80);\n  \n  pdf.setFontSize(10);\n  pdf.setTextColor(60, 60, 60);\n  pdf.text(invoice.customerName, 20, 90);\n  pdf.text(invoice.customerEmail, 20, 95);\n  pdf.text(invoice.customerPhone, 20, 100);\n  \n  // Services Table Header\n  const tableStartY = 120;\n  pdf.setFontSize(10);\n  pdf.setTextColor(40, 40, 40);\n  \n  // Table headers\n  pdf.text('Service', 20, tableStartY);\n  pdf.text('Stylist', 80, tableStartY);\n  pdf.text('Price', 130, tableStartY);\n  pdf.text('Duration', 160, tableStartY);\n  \n  // Draw header line\n  pdf.setDrawColor(200, 200, 200);\n  pdf.line(20, tableStartY + 2, 180, tableStartY + 2);\n  \n  // Services Table Content\n  let currentY = tableStartY + 10;\n  pdf.setFontSize(9);\n  pdf.setTextColor(60, 60, 60);\n  \n  invoice.services.forEach((service, index) => {\n    pdf.text(service.name, 20, currentY);\n    pdf.text(service.stylist, 80, currentY);\n    pdf.text(`₹${service.price.toLocaleString('en-IN')}`, 130, currentY);\n    pdf.text(`${service.duration} min`, 160, currentY);\n    \n    currentY += 8;\n    \n    // Add page break if needed\n    if (currentY > 250) {\n      pdf.addPage();\n      currentY = 30;\n    }\n  });\n  \n  // Draw line before totals\n  pdf.setDrawColor(200, 200, 200);\n  pdf.line(130, currentY + 2, 180, currentY + 2);\n  \n  // Totals Section\n  currentY += 15;\n  pdf.setFontSize(10);\n  pdf.setTextColor(60, 60, 60);\n  \n  // Subtotal\n  pdf.text('Subtotal:', 130, currentY);\n  pdf.text(`₹${invoice.subtotal.toLocaleString('en-IN')}`, 175, currentY);\n  currentY += 8;\n  \n  // Discount (if applicable)\n  if (invoice.discountAmount > 0) {\n    pdf.setTextColor(0, 150, 0);\n    pdf.text(`Discount (${invoice.discountType === 'percentage' ? `${invoice.discountValue}%` : 'Fixed'}):`, 130, currentY);\n    pdf.text(`-₹${invoice.discountAmount.toLocaleString('en-IN')}`, 175, currentY);\n    currentY += 8;\n    pdf.setTextColor(60, 60, 60);\n  }\n  \n  // Tax (GST)\n  pdf.text(`GST (${invoice.taxRate}%):`, 130, currentY);\n  pdf.text(`₹${invoice.taxAmount.toLocaleString('en-IN')}`, 175, currentY);\n  currentY += 8;\n  \n  // Total\n  pdf.setDrawColor(40, 40, 40);\n  pdf.line(130, currentY, 180, currentY);\n  currentY += 8;\n  \n  pdf.setFontSize(12);\n  pdf.setTextColor(40, 40, 40);\n  pdf.text('Total:', 130, currentY);\n  pdf.text(`₹${invoice.total.toLocaleString('en-IN')}`, 175, currentY);\n  \n  // Payment Information (if paid)\n  if (invoice.status === 'paid' && invoice.paymentMethod) {\n    currentY += 20;\n    pdf.setFontSize(10);\n    pdf.setTextColor(0, 150, 0);\n    pdf.text('Payment Information:', 20, currentY);\n    currentY += 8;\n    pdf.setTextColor(60, 60, 60);\n    pdf.text(`Method: ${invoice.paymentMethod.toUpperCase()}`, 20, currentY);\n    if (invoice.paymentDate) {\n      currentY += 6;\n      pdf.text(`Date: ${invoice.paymentDate}`, 20, currentY);\n    }\n    if (invoice.transactionId) {\n      currentY += 6;\n      pdf.text(`Transaction ID: ${invoice.transactionId}`, 20, currentY);\n    }\n  }\n  \n  // Notes (if any)\n  if (invoice.notes) {\n    currentY += 20;\n    pdf.setFontSize(10);\n    pdf.setTextColor(40, 40, 40);\n    pdf.text('Notes:', 20, currentY);\n    currentY += 8;\n    pdf.setTextColor(60, 60, 60);\n    \n    // Split notes into multiple lines if too long\n    const splitNotes = pdf.splitTextToSize(invoice.notes, 170);\n    splitNotes.forEach((line) => {\n      pdf.text(line, 20, currentY);\n      currentY += 6;\n    });\n  }\n  \n  // Footer\n  const pageHeight = pdf.internal.pageSize.height;\n  pdf.setFontSize(8);\n  pdf.setTextColor(150, 150, 150);\n  pdf.text('Thank you for your business!', 20, pageHeight - 20);\n  pdf.text(`Generated on ${new Date().toLocaleDateString()}`, 20, pageHeight - 15);\n  \n  // Save the PDF\n  pdf.save(`invoice-${invoice.id}.pdf`);\n};\n\nexport const generateInvoicePreview = (invoice) => {\n  const pdf = new jsPDF();\n  \n  // Use the same generation logic as above\n  // This function can be used to generate a preview without downloading\n  \n  // Return the PDF as a blob for preview\n  return pdf.output('blob');\n};\n\nexport const exportInvoicesPDF = (invoices) => {\n  const pdf = new jsPDF();\n\n  // Title page\n  pdf.setFontSize(24);\n  pdf.setTextColor(40, 40, 40);\n  pdf.text('Invoice Export Report', 20, 30);\n\n  pdf.setFontSize(12);\n  pdf.setTextColor(60, 60, 60);\n  pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 45);\n  pdf.text(`Total Invoices: ${invoices.length}`, 20, 55);\n\n  // Calculate totals\n  const totalAmount = invoices.reduce((sum, inv) => sum + inv.total, 0);\n  const paidAmount = invoices.filter(inv => inv.status === 'paid').reduce((sum, inv) => sum + inv.total, 0);\n  const pendingAmount = invoices.filter(inv => inv.status === 'pending').reduce((sum, inv) => sum + inv.total, 0);\n\n  pdf.text(`Total Amount: ₹${totalAmount.toLocaleString('en-IN')}`, 20, 65);\n  pdf.text(`Paid Amount: ₹${paidAmount.toLocaleString('en-IN')}`, 20, 75);\n  pdf.text(`Pending Amount: ₹${pendingAmount.toLocaleString('en-IN')}`, 20, 85);\n\n  // Summary table header\n  let currentY = 110;\n  pdf.setFontSize(14);\n  pdf.setTextColor(40, 40, 40);\n  pdf.text('Invoice Summary', 20, currentY);\n\n  currentY += 15;\n  pdf.setFontSize(10);\n\n  // Table headers\n  pdf.text('Invoice ID', 20, currentY);\n  pdf.text('Customer', 60, currentY);\n  pdf.text('Date', 110, currentY);\n  pdf.text('Status', 140, currentY);\n  pdf.text('Amount', 170, currentY);\n\n  // Draw header line\n  pdf.setDrawColor(200, 200, 200);\n  pdf.line(20, currentY + 2, 190, currentY + 2);\n\n  currentY += 10;\n\n  // Invoice rows\n  pdf.setFontSize(9);\n  pdf.setTextColor(60, 60, 60);\n\n  invoices.forEach((invoice, index) => {\n    if (currentY > 270) {\n      pdf.addPage();\n      currentY = 30;\n\n      // Repeat headers on new page\n      pdf.setFontSize(10);\n      pdf.setTextColor(40, 40, 40);\n      pdf.text('Invoice ID', 20, currentY);\n      pdf.text('Customer', 60, currentY);\n      pdf.text('Date', 110, currentY);\n      pdf.text('Status', 140, currentY);\n      pdf.text('Amount', 170, currentY);\n\n      pdf.setDrawColor(200, 200, 200);\n      pdf.line(20, currentY + 2, 190, currentY + 2);\n      currentY += 10;\n      pdf.setFontSize(9);\n      pdf.setTextColor(60, 60, 60);\n    }\n\n    pdf.text(invoice.id, 20, currentY);\n    pdf.text(invoice.customerName.substring(0, 20), 60, currentY);\n    pdf.text(invoice.date, 110, currentY);\n\n    // Color code status\n    if (invoice.status === 'paid') {\n      pdf.setTextColor(0, 150, 0);\n    } else if (invoice.status === 'pending') {\n      pdf.setTextColor(255, 140, 0);\n    } else if (invoice.status === 'overdue') {\n      pdf.setTextColor(220, 20, 60);\n    }\n\n    pdf.text(invoice.status.toUpperCase(), 140, currentY);\n    pdf.setTextColor(60, 60, 60);\n    pdf.text(`₹${invoice.total.toLocaleString('en-IN')}`, 170, currentY);\n\n    currentY += 8;\n  });\n\n  // Footer\n  const pageHeight = pdf.internal.pageSize.height;\n  pdf.setFontSize(8);\n  pdf.setTextColor(150, 150, 150);\n  pdf.text('Salon Management System - Invoice Export', 20, pageHeight - 20);\n  pdf.text(`Generated on ${new Date().toLocaleDateString()}`, 20, pageHeight - 15);\n\n  // Save the PDF\n  pdf.save(`invoices-export-${new Date().toISOString().split('T')[0]}.pdf`);\n};\n\nexport const printInvoice = (invoice) => {\n  const pdf = new jsPDF();\n\n  // Generate PDF with same logic as generateInvoicePDF\n  // But open in new window for printing instead of downloading\n\n  const pdfUrl = pdf.output('bloburl');\n  const printWindow = window.open(pdfUrl);\n\n  if (printWindow) {\n    printWindow.onload = () => {\n      printWindow.print();\n    };\n  }\n};\n\n// Inventory PDF Export Functions\nexport const exportInventoryPDF = (products, options = {}) => {\n  const pdf = new jsPDF();\n\n  // Set font\n  pdf.setFont('helvetica');\n\n  // Company Header\n  pdf.setFontSize(20);\n  pdf.setTextColor(40, 40, 40);\n  pdf.text('Salon Management System', 20, 30);\n\n  pdf.setFontSize(10);\n  pdf.setTextColor(100, 100, 100);\n  pdf.text('123 Beauty Street', 20, 40);\n  pdf.text('City, State 12345', 20, 45);\n  pdf.text('Phone: (*************', 20, 50);\n  pdf.text('Email: <EMAIL>', 20, 55);\n\n  // Report Title\n  pdf.setFontSize(24);\n  pdf.setTextColor(40, 40, 40);\n  pdf.text('INVENTORY REPORT', 20, 80);\n\n  pdf.setFontSize(12);\n  pdf.setTextColor(60, 60, 60);\n  pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 95);\n  pdf.text(`Total Products: ${products.length}`, 20, 105);\n\n  // Calculate totals\n  const totalValue = products.reduce((sum, product) => sum + (product.currentStock * product.unitPrice), 0);\n  const lowStockCount = products.filter(p => p.currentStock <= p.minStockLevel).length;\n  const outOfStockCount = products.filter(p => p.currentStock === 0).length;\n\n  pdf.text(`Total Inventory Value: ₹${totalValue.toLocaleString('en-IN')}`, 20, 115);\n  pdf.text(`Low Stock Items: ${lowStockCount}`, 20, 125);\n  pdf.text(`Out of Stock Items: ${outOfStockCount}`, 20, 135);\n\n  // Table headers\n  let yPosition = 160;\n  pdf.setFontSize(10);\n  pdf.setTextColor(40, 40, 40);\n  pdf.setFont('helvetica', 'bold');\n\n  const headers = ['Product Name', 'SKU', 'Category', 'Stock', 'Min/Max', 'Unit Price', 'Total Value', 'Status'];\n  const columnWidths = [35, 25, 25, 15, 20, 20, 25, 15];\n  let xPosition = 20;\n\n  headers.forEach((header, index) => {\n    pdf.text(header, xPosition, yPosition);\n    xPosition += columnWidths[index];\n  });\n\n  // Draw header line\n  pdf.line(20, yPosition + 2, 200, yPosition + 2);\n\n  // Table data\n  pdf.setFont('helvetica', 'normal');\n  pdf.setFontSize(8);\n  yPosition += 10;\n\n  products.forEach((product, index) => {\n    if (yPosition > 270) {\n      pdf.addPage();\n      yPosition = 30;\n\n      // Repeat headers on new page\n      pdf.setFont('helvetica', 'bold');\n      pdf.setFontSize(10);\n      xPosition = 20;\n      headers.forEach((header, index) => {\n        pdf.text(header, xPosition, yPosition);\n        xPosition += columnWidths[index];\n      });\n      pdf.line(20, yPosition + 2, 200, yPosition + 2);\n      pdf.setFont('helvetica', 'normal');\n      pdf.setFontSize(8);\n      yPosition += 10;\n    }\n\n    xPosition = 20;\n    const rowData = [\n      product.name.substring(0, 20) + (product.name.length > 20 ? '...' : ''),\n      product.sku,\n      product.category.substring(0, 15) + (product.category.length > 15 ? '...' : ''),\n      product.currentStock.toString(),\n      `${product.minStockLevel}/${product.maxStockLevel}`,\n      `₹${product.unitPrice.toLocaleString('en-IN')}`,\n      `₹${(product.currentStock * product.unitPrice).toLocaleString('en-IN')}`,\n      product.currentStock === 0 ? 'Out' : product.currentStock <= product.minStockLevel ? 'Low' : 'OK'\n    ];\n\n    // Set color based on stock status\n    if (product.currentStock === 0) {\n      pdf.setTextColor(220, 53, 69); // Red for out of stock\n    } else if (product.currentStock <= product.minStockLevel) {\n      pdf.setTextColor(255, 193, 7); // Yellow for low stock\n    } else {\n      pdf.setTextColor(40, 40, 40); // Normal color\n    }\n\n    rowData.forEach((data, index) => {\n      pdf.text(data, xPosition, yPosition);\n      xPosition += columnWidths[index];\n    });\n\n    yPosition += 8;\n  });\n\n  // Footer\n  const pageHeight = pdf.internal.pageSize.height;\n  pdf.setFontSize(8);\n  pdf.setTextColor(150, 150, 150);\n  pdf.text('Salon Management System - Inventory Report', 20, pageHeight - 20);\n  pdf.text(`Generated on ${new Date().toLocaleDateString()}`, 20, pageHeight - 15);\n\n  // Save the PDF\n  pdf.save(`inventory-report-${new Date().toISOString().split('T')[0]}.pdf`);\n};\n\nexport const exportLowStockPDF = (products) => {\n  const lowStockProducts = products.filter(p => p.currentStock <= p.minStockLevel);\n\n  const pdf = new jsPDF();\n  pdf.setFont('helvetica');\n\n  // Header\n  pdf.setFontSize(20);\n  pdf.setTextColor(40, 40, 40);\n  pdf.text('Low Stock Alert Report', 20, 30);\n\n  pdf.setFontSize(12);\n  pdf.setTextColor(60, 60, 60);\n  pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 45);\n  pdf.text(`Low Stock Items: ${lowStockProducts.length}`, 20, 55);\n\n  let yPosition = 80;\n\n  lowStockProducts.forEach((product, index) => {\n    if (yPosition > 250) {\n      pdf.addPage();\n      yPosition = 30;\n    }\n\n    pdf.setFontSize(14);\n    pdf.setTextColor(220, 53, 69);\n    pdf.text(`${index + 1}. ${product.name}`, 20, yPosition);\n\n    pdf.setFontSize(10);\n    pdf.setTextColor(60, 60, 60);\n    pdf.text(`SKU: ${product.sku}`, 30, yPosition + 10);\n    pdf.text(`Current Stock: ${product.currentStock}`, 30, yPosition + 20);\n    pdf.text(`Minimum Level: ${product.minStockLevel}`, 30, yPosition + 30);\n    pdf.text(`Supplier: ${product.supplier}`, 30, yPosition + 40);\n\n    yPosition += 60;\n  });\n\n  pdf.save(`low-stock-report-${new Date().toISOString().split('T')[0]}.pdf`);\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,OAAO,MAAMC,kBAAkB,GAAIC,OAAO,IAAK;EAC7C,MAAMC,GAAG,GAAG,IAAIH,KAAK,CAAC,CAAC;;EAEvB;EACAG,GAAG,CAACC,OAAO,CAAC,WAAW,CAAC;;EAExB;EACAD,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;EACnBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5BH,GAAG,CAACI,IAAI,CAAC,yBAAyB,EAAE,EAAE,EAAE,EAAE,CAAC;EAE3CJ,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;EACnBF,GAAG,CAACG,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC/BH,GAAG,CAACI,IAAI,CAAC,mBAAmB,EAAE,EAAE,EAAE,EAAE,CAAC;EACrCJ,GAAG,CAACI,IAAI,CAAC,mBAAmB,EAAE,EAAE,EAAE,EAAE,CAAC;EACrCJ,GAAG,CAACI,IAAI,CAAC,uBAAuB,EAAE,EAAE,EAAE,EAAE,CAAC;EACzCJ,GAAG,CAACI,IAAI,CAAC,iCAAiC,EAAE,EAAE,EAAE,EAAE,CAAC;;EAEnD;EACAJ,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;EACnBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5BH,GAAG,CAACI,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,EAAE,CAAC;EAE5BJ,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;EACnBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5BH,GAAG,CAACI,IAAI,CAACL,OAAO,CAACM,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;;EAE7B;EACAL,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;EACnBF,GAAG,CAACI,IAAI,CAAC,SAASL,OAAO,CAACO,IAAI,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;EAC1CN,GAAG,CAACI,IAAI,CAAC,aAAaL,OAAO,CAACQ,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;EACjDP,GAAG,CAACI,IAAI,CAAC,WAAWL,OAAO,CAACS,MAAM,CAACC,WAAW,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;;EAE5D;EACAT,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;EACnBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5BH,GAAG,CAACI,IAAI,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE,CAAC;EAE5BJ,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;EACnBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5BH,GAAG,CAACI,IAAI,CAACL,OAAO,CAACW,YAAY,EAAE,EAAE,EAAE,EAAE,CAAC;EACtCV,GAAG,CAACI,IAAI,CAACL,OAAO,CAACY,aAAa,EAAE,EAAE,EAAE,EAAE,CAAC;EACvCX,GAAG,CAACI,IAAI,CAACL,OAAO,CAACa,aAAa,EAAE,EAAE,EAAE,GAAG,CAAC;;EAExC;EACA,MAAMC,WAAW,GAAG,GAAG;EACvBb,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;EACnBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;EAE5B;EACAH,GAAG,CAACI,IAAI,CAAC,SAAS,EAAE,EAAE,EAAES,WAAW,CAAC;EACpCb,GAAG,CAACI,IAAI,CAAC,SAAS,EAAE,EAAE,EAAES,WAAW,CAAC;EACpCb,GAAG,CAACI,IAAI,CAAC,OAAO,EAAE,GAAG,EAAES,WAAW,CAAC;EACnCb,GAAG,CAACI,IAAI,CAAC,UAAU,EAAE,GAAG,EAAES,WAAW,CAAC;;EAEtC;EACAb,GAAG,CAACc,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC/Bd,GAAG,CAACe,IAAI,CAAC,EAAE,EAAEF,WAAW,GAAG,CAAC,EAAE,GAAG,EAAEA,WAAW,GAAG,CAAC,CAAC;;EAEnD;EACA,IAAIG,QAAQ,GAAGH,WAAW,GAAG,EAAE;EAC/Bb,GAAG,CAACE,WAAW,CAAC,CAAC,CAAC;EAClBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAE5BJ,OAAO,CAACkB,QAAQ,CAACC,OAAO,CAAC,CAACC,OAAO,EAAEC,KAAK,KAAK;IAC3CpB,GAAG,CAACI,IAAI,CAACe,OAAO,CAACE,IAAI,EAAE,EAAE,EAAEL,QAAQ,CAAC;IACpChB,GAAG,CAACI,IAAI,CAACe,OAAO,CAACG,OAAO,EAAE,EAAE,EAAEN,QAAQ,CAAC;IACvChB,GAAG,CAACI,IAAI,CAAC,IAAIe,OAAO,CAACI,KAAK,CAACC,cAAc,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG,EAAER,QAAQ,CAAC;IACpEhB,GAAG,CAACI,IAAI,CAAC,GAAGe,OAAO,CAACM,QAAQ,MAAM,EAAE,GAAG,EAAET,QAAQ,CAAC;IAElDA,QAAQ,IAAI,CAAC;;IAEb;IACA,IAAIA,QAAQ,GAAG,GAAG,EAAE;MAClBhB,GAAG,CAAC0B,OAAO,CAAC,CAAC;MACbV,QAAQ,GAAG,EAAE;IACf;EACF,CAAC,CAAC;;EAEF;EACAhB,GAAG,CAACc,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC/Bd,GAAG,CAACe,IAAI,CAAC,GAAG,EAAEC,QAAQ,GAAG,CAAC,EAAE,GAAG,EAAEA,QAAQ,GAAG,CAAC,CAAC;;EAE9C;EACAA,QAAQ,IAAI,EAAE;EACdhB,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;EACnBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;EAE5B;EACAH,GAAG,CAACI,IAAI,CAAC,WAAW,EAAE,GAAG,EAAEY,QAAQ,CAAC;EACpChB,GAAG,CAACI,IAAI,CAAC,IAAIL,OAAO,CAAC4B,QAAQ,CAACH,cAAc,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG,EAAER,QAAQ,CAAC;EACvEA,QAAQ,IAAI,CAAC;;EAEb;EACA,IAAIjB,OAAO,CAAC6B,cAAc,GAAG,CAAC,EAAE;IAC9B5B,GAAG,CAACG,YAAY,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IAC3BH,GAAG,CAACI,IAAI,CAAC,aAAaL,OAAO,CAAC8B,YAAY,KAAK,YAAY,GAAG,GAAG9B,OAAO,CAAC+B,aAAa,GAAG,GAAG,OAAO,IAAI,EAAE,GAAG,EAAEd,QAAQ,CAAC;IACvHhB,GAAG,CAACI,IAAI,CAAC,KAAKL,OAAO,CAAC6B,cAAc,CAACJ,cAAc,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG,EAAER,QAAQ,CAAC;IAC9EA,QAAQ,IAAI,CAAC;IACbhB,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC9B;;EAEA;EACAH,GAAG,CAACI,IAAI,CAAC,QAAQL,OAAO,CAACgC,OAAO,KAAK,EAAE,GAAG,EAAEf,QAAQ,CAAC;EACrDhB,GAAG,CAACI,IAAI,CAAC,IAAIL,OAAO,CAACiC,SAAS,CAACR,cAAc,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG,EAAER,QAAQ,CAAC;EACxEA,QAAQ,IAAI,CAAC;;EAEb;EACAhB,GAAG,CAACc,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5Bd,GAAG,CAACe,IAAI,CAAC,GAAG,EAAEC,QAAQ,EAAE,GAAG,EAAEA,QAAQ,CAAC;EACtCA,QAAQ,IAAI,CAAC;EAEbhB,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;EACnBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5BH,GAAG,CAACI,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAEY,QAAQ,CAAC;EACjChB,GAAG,CAACI,IAAI,CAAC,IAAIL,OAAO,CAACkC,KAAK,CAACT,cAAc,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG,EAAER,QAAQ,CAAC;;EAEpE;EACA,IAAIjB,OAAO,CAACS,MAAM,KAAK,MAAM,IAAIT,OAAO,CAACmC,aAAa,EAAE;IACtDlB,QAAQ,IAAI,EAAE;IACdhB,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;IACnBF,GAAG,CAACG,YAAY,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IAC3BH,GAAG,CAACI,IAAI,CAAC,sBAAsB,EAAE,EAAE,EAAEY,QAAQ,CAAC;IAC9CA,QAAQ,IAAI,CAAC;IACbhB,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC5BH,GAAG,CAACI,IAAI,CAAC,WAAWL,OAAO,CAACmC,aAAa,CAACzB,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,EAAEO,QAAQ,CAAC;IACxE,IAAIjB,OAAO,CAACoC,WAAW,EAAE;MACvBnB,QAAQ,IAAI,CAAC;MACbhB,GAAG,CAACI,IAAI,CAAC,SAASL,OAAO,CAACoC,WAAW,EAAE,EAAE,EAAE,EAAEnB,QAAQ,CAAC;IACxD;IACA,IAAIjB,OAAO,CAACqC,aAAa,EAAE;MACzBpB,QAAQ,IAAI,CAAC;MACbhB,GAAG,CAACI,IAAI,CAAC,mBAAmBL,OAAO,CAACqC,aAAa,EAAE,EAAE,EAAE,EAAEpB,QAAQ,CAAC;IACpE;EACF;;EAEA;EACA,IAAIjB,OAAO,CAACsC,KAAK,EAAE;IACjBrB,QAAQ,IAAI,EAAE;IACdhB,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;IACnBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC5BH,GAAG,CAACI,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAEY,QAAQ,CAAC;IAChCA,QAAQ,IAAI,CAAC;IACbhB,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;IAE5B;IACA,MAAMmC,UAAU,GAAGtC,GAAG,CAACuC,eAAe,CAACxC,OAAO,CAACsC,KAAK,EAAE,GAAG,CAAC;IAC1DC,UAAU,CAACpB,OAAO,CAAEH,IAAI,IAAK;MAC3Bf,GAAG,CAACI,IAAI,CAACW,IAAI,EAAE,EAAE,EAAEC,QAAQ,CAAC;MAC5BA,QAAQ,IAAI,CAAC;IACf,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMwB,UAAU,GAAGxC,GAAG,CAACyC,QAAQ,CAACC,QAAQ,CAACC,MAAM;EAC/C3C,GAAG,CAACE,WAAW,CAAC,CAAC,CAAC;EAClBF,GAAG,CAACG,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC/BH,GAAG,CAACI,IAAI,CAAC,8BAA8B,EAAE,EAAE,EAAEoC,UAAU,GAAG,EAAE,CAAC;EAC7DxC,GAAG,CAACI,IAAI,CAAC,gBAAgB,IAAIwC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAEL,UAAU,GAAG,EAAE,CAAC;;EAEhF;EACAxC,GAAG,CAAC8C,IAAI,CAAC,WAAW/C,OAAO,CAACM,EAAE,MAAM,CAAC;AACvC,CAAC;AAED,OAAO,MAAM0C,sBAAsB,GAAIhD,OAAO,IAAK;EACjD,MAAMC,GAAG,GAAG,IAAIH,KAAK,CAAC,CAAC;;EAEvB;EACA;;EAEA;EACA,OAAOG,GAAG,CAACgD,MAAM,CAAC,MAAM,CAAC;AAC3B,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAIC,QAAQ,IAAK;EAC7C,MAAMlD,GAAG,GAAG,IAAIH,KAAK,CAAC,CAAC;;EAEvB;EACAG,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;EACnBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5BH,GAAG,CAACI,IAAI,CAAC,uBAAuB,EAAE,EAAE,EAAE,EAAE,CAAC;EAEzCJ,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;EACnBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5BH,GAAG,CAACI,IAAI,CAAC,iBAAiB,IAAIwC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACpE7C,GAAG,CAACI,IAAI,CAAC,mBAAmB8C,QAAQ,CAACC,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;EAEtD;EACA,MAAMC,WAAW,GAAGF,QAAQ,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACtB,KAAK,EAAE,CAAC,CAAC;EACrE,MAAMuB,UAAU,GAAGN,QAAQ,CAACO,MAAM,CAACF,GAAG,IAAIA,GAAG,CAAC/C,MAAM,KAAK,MAAM,CAAC,CAAC6C,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACtB,KAAK,EAAE,CAAC,CAAC;EACzG,MAAMyB,aAAa,GAAGR,QAAQ,CAACO,MAAM,CAACF,GAAG,IAAIA,GAAG,CAAC/C,MAAM,KAAK,SAAS,CAAC,CAAC6C,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,CAACtB,KAAK,EAAE,CAAC,CAAC;EAE/GjC,GAAG,CAACI,IAAI,CAAC,kBAAkBgD,WAAW,CAAC5B,cAAc,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACzExB,GAAG,CAACI,IAAI,CAAC,iBAAiBoD,UAAU,CAAChC,cAAc,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACvExB,GAAG,CAACI,IAAI,CAAC,oBAAoBsD,aAAa,CAAClC,cAAc,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;EAE7E;EACA,IAAIR,QAAQ,GAAG,GAAG;EAClBhB,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;EACnBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5BH,GAAG,CAACI,IAAI,CAAC,iBAAiB,EAAE,EAAE,EAAEY,QAAQ,CAAC;EAEzCA,QAAQ,IAAI,EAAE;EACdhB,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;;EAEnB;EACAF,GAAG,CAACI,IAAI,CAAC,YAAY,EAAE,EAAE,EAAEY,QAAQ,CAAC;EACpChB,GAAG,CAACI,IAAI,CAAC,UAAU,EAAE,EAAE,EAAEY,QAAQ,CAAC;EAClChB,GAAG,CAACI,IAAI,CAAC,MAAM,EAAE,GAAG,EAAEY,QAAQ,CAAC;EAC/BhB,GAAG,CAACI,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAEY,QAAQ,CAAC;EACjChB,GAAG,CAACI,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAEY,QAAQ,CAAC;;EAEjC;EACAhB,GAAG,CAACc,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC/Bd,GAAG,CAACe,IAAI,CAAC,EAAE,EAAEC,QAAQ,GAAG,CAAC,EAAE,GAAG,EAAEA,QAAQ,GAAG,CAAC,CAAC;EAE7CA,QAAQ,IAAI,EAAE;;EAEd;EACAhB,GAAG,CAACE,WAAW,CAAC,CAAC,CAAC;EAClBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAE5B+C,QAAQ,CAAChC,OAAO,CAAC,CAACnB,OAAO,EAAEqB,KAAK,KAAK;IACnC,IAAIJ,QAAQ,GAAG,GAAG,EAAE;MAClBhB,GAAG,CAAC0B,OAAO,CAAC,CAAC;MACbV,QAAQ,GAAG,EAAE;;MAEb;MACAhB,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;MACnBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAC5BH,GAAG,CAACI,IAAI,CAAC,YAAY,EAAE,EAAE,EAAEY,QAAQ,CAAC;MACpChB,GAAG,CAACI,IAAI,CAAC,UAAU,EAAE,EAAE,EAAEY,QAAQ,CAAC;MAClChB,GAAG,CAACI,IAAI,CAAC,MAAM,EAAE,GAAG,EAAEY,QAAQ,CAAC;MAC/BhB,GAAG,CAACI,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAEY,QAAQ,CAAC;MACjChB,GAAG,CAACI,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAEY,QAAQ,CAAC;MAEjChB,GAAG,CAACc,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/Bd,GAAG,CAACe,IAAI,CAAC,EAAE,EAAEC,QAAQ,GAAG,CAAC,EAAE,GAAG,EAAEA,QAAQ,GAAG,CAAC,CAAC;MAC7CA,QAAQ,IAAI,EAAE;MACdhB,GAAG,CAACE,WAAW,CAAC,CAAC,CAAC;MAClBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC9B;IAEAH,GAAG,CAACI,IAAI,CAACL,OAAO,CAACM,EAAE,EAAE,EAAE,EAAEW,QAAQ,CAAC;IAClChB,GAAG,CAACI,IAAI,CAACL,OAAO,CAACW,YAAY,CAACiD,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE3C,QAAQ,CAAC;IAC7DhB,GAAG,CAACI,IAAI,CAACL,OAAO,CAACO,IAAI,EAAE,GAAG,EAAEU,QAAQ,CAAC;;IAErC;IACA,IAAIjB,OAAO,CAACS,MAAM,KAAK,MAAM,EAAE;MAC7BR,GAAG,CAACG,YAAY,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IAC7B,CAAC,MAAM,IAAIJ,OAAO,CAACS,MAAM,KAAK,SAAS,EAAE;MACvCR,GAAG,CAACG,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC/B,CAAC,MAAM,IAAIJ,OAAO,CAACS,MAAM,KAAK,SAAS,EAAE;MACvCR,GAAG,CAACG,YAAY,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;IAC/B;IAEAH,GAAG,CAACI,IAAI,CAACL,OAAO,CAACS,MAAM,CAACC,WAAW,CAAC,CAAC,EAAE,GAAG,EAAEO,QAAQ,CAAC;IACrDhB,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC5BH,GAAG,CAACI,IAAI,CAAC,IAAIL,OAAO,CAACkC,KAAK,CAACT,cAAc,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG,EAAER,QAAQ,CAAC;IAEpEA,QAAQ,IAAI,CAAC;EACf,CAAC,CAAC;;EAEF;EACA,MAAMwB,UAAU,GAAGxC,GAAG,CAACyC,QAAQ,CAACC,QAAQ,CAACC,MAAM;EAC/C3C,GAAG,CAACE,WAAW,CAAC,CAAC,CAAC;EAClBF,GAAG,CAACG,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC/BH,GAAG,CAACI,IAAI,CAAC,0CAA0C,EAAE,EAAE,EAAEoC,UAAU,GAAG,EAAE,CAAC;EACzExC,GAAG,CAACI,IAAI,CAAC,gBAAgB,IAAIwC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAEL,UAAU,GAAG,EAAE,CAAC;;EAEhF;EACAxC,GAAG,CAAC8C,IAAI,CAAC,mBAAmB,IAAIF,IAAI,CAAC,CAAC,CAACgB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AAC3E,CAAC;AAED,OAAO,MAAMC,YAAY,GAAI/D,OAAO,IAAK;EACvC,MAAMC,GAAG,GAAG,IAAIH,KAAK,CAAC,CAAC;;EAEvB;EACA;;EAEA,MAAMkE,MAAM,GAAG/D,GAAG,CAACgD,MAAM,CAAC,SAAS,CAAC;EACpC,MAAMgB,WAAW,GAAGC,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC;EAEvC,IAAIC,WAAW,EAAE;IACfA,WAAW,CAACG,MAAM,GAAG,MAAM;MACzBH,WAAW,CAACI,KAAK,CAAC,CAAC;IACrB,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,kBAAkB,GAAGA,CAACC,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;EAC5D,MAAMvE,GAAG,GAAG,IAAIH,KAAK,CAAC,CAAC;;EAEvB;EACAG,GAAG,CAACC,OAAO,CAAC,WAAW,CAAC;;EAExB;EACAD,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;EACnBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5BH,GAAG,CAACI,IAAI,CAAC,yBAAyB,EAAE,EAAE,EAAE,EAAE,CAAC;EAE3CJ,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;EACnBF,GAAG,CAACG,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC/BH,GAAG,CAACI,IAAI,CAAC,mBAAmB,EAAE,EAAE,EAAE,EAAE,CAAC;EACrCJ,GAAG,CAACI,IAAI,CAAC,mBAAmB,EAAE,EAAE,EAAE,EAAE,CAAC;EACrCJ,GAAG,CAACI,IAAI,CAAC,uBAAuB,EAAE,EAAE,EAAE,EAAE,CAAC;EACzCJ,GAAG,CAACI,IAAI,CAAC,iCAAiC,EAAE,EAAE,EAAE,EAAE,CAAC;;EAEnD;EACAJ,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;EACnBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5BH,GAAG,CAACI,IAAI,CAAC,kBAAkB,EAAE,EAAE,EAAE,EAAE,CAAC;EAEpCJ,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;EACnBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5BH,GAAG,CAACI,IAAI,CAAC,iBAAiB,IAAIwC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACpE7C,GAAG,CAACI,IAAI,CAAC,mBAAmBkE,QAAQ,CAACnB,MAAM,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;;EAEvD;EACA,MAAMqB,UAAU,GAAGF,QAAQ,CAACjB,MAAM,CAAC,CAACC,GAAG,EAAEmB,OAAO,KAAKnB,GAAG,GAAImB,OAAO,CAACC,YAAY,GAAGD,OAAO,CAACE,SAAU,EAAE,CAAC,CAAC;EACzG,MAAMC,aAAa,GAAGN,QAAQ,CAACb,MAAM,CAACoB,CAAC,IAAIA,CAAC,CAACH,YAAY,IAAIG,CAAC,CAACC,aAAa,CAAC,CAAC3B,MAAM;EACpF,MAAM4B,eAAe,GAAGT,QAAQ,CAACb,MAAM,CAACoB,CAAC,IAAIA,CAAC,CAACH,YAAY,KAAK,CAAC,CAAC,CAACvB,MAAM;EAEzEnD,GAAG,CAACI,IAAI,CAAC,2BAA2BoE,UAAU,CAAChD,cAAc,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EAClFxB,GAAG,CAACI,IAAI,CAAC,oBAAoBwE,aAAa,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EACtD5E,GAAG,CAACI,IAAI,CAAC,uBAAuB2E,eAAe,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;;EAE3D;EACA,IAAIC,SAAS,GAAG,GAAG;EACnBhF,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;EACnBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5BH,GAAG,CAACC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;EAEhC,MAAMgF,OAAO,GAAG,CAAC,cAAc,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,aAAa,EAAE,QAAQ,CAAC;EAC9G,MAAMC,YAAY,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACrD,IAAIC,SAAS,GAAG,EAAE;EAElBF,OAAO,CAAC/D,OAAO,CAAC,CAACkE,MAAM,EAAEhE,KAAK,KAAK;IACjCpB,GAAG,CAACI,IAAI,CAACgF,MAAM,EAAED,SAAS,EAAEH,SAAS,CAAC;IACtCG,SAAS,IAAID,YAAY,CAAC9D,KAAK,CAAC;EAClC,CAAC,CAAC;;EAEF;EACApB,GAAG,CAACe,IAAI,CAAC,EAAE,EAAEiE,SAAS,GAAG,CAAC,EAAE,GAAG,EAAEA,SAAS,GAAG,CAAC,CAAC;;EAE/C;EACAhF,GAAG,CAACC,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;EAClCD,GAAG,CAACE,WAAW,CAAC,CAAC,CAAC;EAClB8E,SAAS,IAAI,EAAE;EAEfV,QAAQ,CAACpD,OAAO,CAAC,CAACuD,OAAO,EAAErD,KAAK,KAAK;IACnC,IAAI4D,SAAS,GAAG,GAAG,EAAE;MACnBhF,GAAG,CAAC0B,OAAO,CAAC,CAAC;MACbsD,SAAS,GAAG,EAAE;;MAEd;MACAhF,GAAG,CAACC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCD,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;MACnBiF,SAAS,GAAG,EAAE;MACdF,OAAO,CAAC/D,OAAO,CAAC,CAACkE,MAAM,EAAEhE,KAAK,KAAK;QACjCpB,GAAG,CAACI,IAAI,CAACgF,MAAM,EAAED,SAAS,EAAEH,SAAS,CAAC;QACtCG,SAAS,IAAID,YAAY,CAAC9D,KAAK,CAAC;MAClC,CAAC,CAAC;MACFpB,GAAG,CAACe,IAAI,CAAC,EAAE,EAAEiE,SAAS,GAAG,CAAC,EAAE,GAAG,EAAEA,SAAS,GAAG,CAAC,CAAC;MAC/ChF,GAAG,CAACC,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCD,GAAG,CAACE,WAAW,CAAC,CAAC,CAAC;MAClB8E,SAAS,IAAI,EAAE;IACjB;IAEAG,SAAS,GAAG,EAAE;IACd,MAAME,OAAO,GAAG,CACdZ,OAAO,CAACpD,IAAI,CAACsC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAIc,OAAO,CAACpD,IAAI,CAAC8B,MAAM,GAAG,EAAE,GAAG,KAAK,GAAG,EAAE,CAAC,EACvEsB,OAAO,CAACa,GAAG,EACXb,OAAO,CAACc,QAAQ,CAAC5B,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAIc,OAAO,CAACc,QAAQ,CAACpC,MAAM,GAAG,EAAE,GAAG,KAAK,GAAG,EAAE,CAAC,EAC/EsB,OAAO,CAACC,YAAY,CAACc,QAAQ,CAAC,CAAC,EAC/B,GAAGf,OAAO,CAACK,aAAa,IAAIL,OAAO,CAACgB,aAAa,EAAE,EACnD,IAAIhB,OAAO,CAACE,SAAS,CAACnD,cAAc,CAAC,OAAO,CAAC,EAAE,EAC/C,IAAI,CAACiD,OAAO,CAACC,YAAY,GAAGD,OAAO,CAACE,SAAS,EAAEnD,cAAc,CAAC,OAAO,CAAC,EAAE,EACxEiD,OAAO,CAACC,YAAY,KAAK,CAAC,GAAG,KAAK,GAAGD,OAAO,CAACC,YAAY,IAAID,OAAO,CAACK,aAAa,GAAG,KAAK,GAAG,IAAI,CAClG;;IAED;IACA,IAAIL,OAAO,CAACC,YAAY,KAAK,CAAC,EAAE;MAC9B1E,GAAG,CAACG,YAAY,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IACjC,CAAC,MAAM,IAAIsE,OAAO,CAACC,YAAY,IAAID,OAAO,CAACK,aAAa,EAAE;MACxD9E,GAAG,CAACG,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC,MAAM;MACLH,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IAChC;IAEAkF,OAAO,CAACnE,OAAO,CAAC,CAACwE,IAAI,EAAEtE,KAAK,KAAK;MAC/BpB,GAAG,CAACI,IAAI,CAACsF,IAAI,EAAEP,SAAS,EAAEH,SAAS,CAAC;MACpCG,SAAS,IAAID,YAAY,CAAC9D,KAAK,CAAC;IAClC,CAAC,CAAC;IAEF4D,SAAS,IAAI,CAAC;EAChB,CAAC,CAAC;;EAEF;EACA,MAAMxC,UAAU,GAAGxC,GAAG,CAACyC,QAAQ,CAACC,QAAQ,CAACC,MAAM;EAC/C3C,GAAG,CAACE,WAAW,CAAC,CAAC,CAAC;EAClBF,GAAG,CAACG,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC/BH,GAAG,CAACI,IAAI,CAAC,4CAA4C,EAAE,EAAE,EAAEoC,UAAU,GAAG,EAAE,CAAC;EAC3ExC,GAAG,CAACI,IAAI,CAAC,gBAAgB,IAAIwC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAEL,UAAU,GAAG,EAAE,CAAC;;EAEhF;EACAxC,GAAG,CAAC8C,IAAI,CAAC,oBAAoB,IAAIF,IAAI,CAAC,CAAC,CAACgB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AAC5E,CAAC;AAED,OAAO,MAAM8B,iBAAiB,GAAIrB,QAAQ,IAAK;EAC7C,MAAMsB,gBAAgB,GAAGtB,QAAQ,CAACb,MAAM,CAACoB,CAAC,IAAIA,CAAC,CAACH,YAAY,IAAIG,CAAC,CAACC,aAAa,CAAC;EAEhF,MAAM9E,GAAG,GAAG,IAAIH,KAAK,CAAC,CAAC;EACvBG,GAAG,CAACC,OAAO,CAAC,WAAW,CAAC;;EAExB;EACAD,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;EACnBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5BH,GAAG,CAACI,IAAI,CAAC,wBAAwB,EAAE,EAAE,EAAE,EAAE,CAAC;EAE1CJ,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;EACnBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5BH,GAAG,CAACI,IAAI,CAAC,iBAAiB,IAAIwC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACpE7C,GAAG,CAACI,IAAI,CAAC,oBAAoBwF,gBAAgB,CAACzC,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAE/D,IAAI6B,SAAS,GAAG,EAAE;EAElBY,gBAAgB,CAAC1E,OAAO,CAAC,CAACuD,OAAO,EAAErD,KAAK,KAAK;IAC3C,IAAI4D,SAAS,GAAG,GAAG,EAAE;MACnBhF,GAAG,CAAC0B,OAAO,CAAC,CAAC;MACbsD,SAAS,GAAG,EAAE;IAChB;IAEAhF,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;IACnBF,GAAG,CAACG,YAAY,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;IAC7BH,GAAG,CAACI,IAAI,CAAC,GAAGgB,KAAK,GAAG,CAAC,KAAKqD,OAAO,CAACpD,IAAI,EAAE,EAAE,EAAE,EAAE2D,SAAS,CAAC;IAExDhF,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;IACnBF,GAAG,CAACG,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC5BH,GAAG,CAACI,IAAI,CAAC,QAAQqE,OAAO,CAACa,GAAG,EAAE,EAAE,EAAE,EAAEN,SAAS,GAAG,EAAE,CAAC;IACnDhF,GAAG,CAACI,IAAI,CAAC,kBAAkBqE,OAAO,CAACC,YAAY,EAAE,EAAE,EAAE,EAAEM,SAAS,GAAG,EAAE,CAAC;IACtEhF,GAAG,CAACI,IAAI,CAAC,kBAAkBqE,OAAO,CAACK,aAAa,EAAE,EAAE,EAAE,EAAEE,SAAS,GAAG,EAAE,CAAC;IACvEhF,GAAG,CAACI,IAAI,CAAC,aAAaqE,OAAO,CAACoB,QAAQ,EAAE,EAAE,EAAE,EAAEb,SAAS,GAAG,EAAE,CAAC;IAE7DA,SAAS,IAAI,EAAE;EACjB,CAAC,CAAC;EAEFhF,GAAG,CAAC8C,IAAI,CAAC,oBAAoB,IAAIF,IAAI,CAAC,CAAC,CAACgB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AAC5E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}