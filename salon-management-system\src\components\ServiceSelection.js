import React, { useState } from 'react';
import {
  Box,
  <PERSON>po<PERSON>,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  Divider,
  Alert,
} from '@mui/material';
import {
  Search as SearchIcon,
  Schedule as ScheduleIcon,
  AttachMoney as MoneyIcon,
  ContentCut as HairIcon,
  Palette as ColorIcon,
  Face as FaceIcon,
  Spa as SpaIcon,
  Star as StarIcon,
} from '@mui/icons-material';
import { useBooking } from '../contexts/BookingContext';

const ServiceSelection = ({ onServiceSelect, onNext }) => {
  const { services, selectedService } = useBooking();
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');

  // Get unique categories
  const categories = ['all', ...new Set(services.map(service => service.category))];

  // Filter services based on search and category
  const filteredServices = services.filter(service => {
    const matchesSearch = service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || service.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });

  // Get service icon
  const getServiceIcon = (category) => {
    switch (category.toLowerCase()) {
      case 'hair':
        return <HairIcon />;
      case 'grooming':
        return <FaceIcon />;
      case 'nails':
        return <SpaIcon />;
      case 'complete':
        return <StarIcon />;
      default:
        return <SpaIcon />;
    }
  };

  // Get category color
  const getCategoryColor = (category) => {
    switch (category.toLowerCase()) {
      case 'hair':
        return 'primary';
      case 'grooming':
        return 'secondary';
      case 'nails':
        return 'success';
      case 'complete':
        return 'warning';
      default:
        return 'default';
    }
  };

  const handleServiceSelect = (service) => {
    onServiceSelect(service);
  };

  const handleNext = () => {
    if (selectedService && onNext) {
      onNext();
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Select a Service
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Choose from our range of professional salon services
      </Typography>

      {/* Search and Filter */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} md={8}>
          <TextField
            fullWidth
            placeholder="Search services..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <FormControl fullWidth>
            <InputLabel>Category</InputLabel>
            <Select
              value={categoryFilter}
              label="Category"
              onChange={(e) => setCategoryFilter(e.target.value)}
            >
              {categories.map(category => (
                <MenuItem key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      {/* Services Grid */}
      <Grid container spacing={3}>
        {filteredServices.map((service) => (
          <Grid item xs={12} sm={6} md={4} key={service.id}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                cursor: 'pointer',
                border: selectedService?.id === service.id ? 2 : 1,
                borderColor: selectedService?.id === service.id ? 'primary.main' : 'divider',
                '&:hover': {
                  boxShadow: 3,
                  transform: 'translateY(-2px)',
                },
                transition: 'all 0.2s ease-in-out',
              }}
              onClick={() => handleServiceSelect(service)}
            >
              <CardContent sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar
                    sx={{
                      bgcolor: `${getCategoryColor(service.category)}.main`,
                      mr: 2,
                    }}
                  >
                    {getServiceIcon(service.category)}
                  </Avatar>
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="h6" component="div">
                      {service.name}
                    </Typography>
                    <Chip
                      label={service.category}
                      size="small"
                      color={getCategoryColor(service.category)}
                      variant="outlined"
                    />
                  </Box>
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {service.description}
                </Typography>

                <Divider sx={{ my: 2 }} />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <ScheduleIcon sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                    <Typography variant="body2" color="text.secondary">
                      {service.duration} min
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <MoneyIcon sx={{ fontSize: 16, mr: 0.5, color: 'success.main' }} />
                    <Typography variant="h6" color="success.main">
                      ₹{service.price}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>

              {selectedService?.id === service.id && (
                <CardActions sx={{ justifyContent: 'center', bgcolor: 'primary.light' }}>
                  <Typography variant="body2" color="primary.contrastText">
                    Selected
                  </Typography>
                </CardActions>
              )}
            </Card>
          </Grid>
        ))}
      </Grid>

      {filteredServices.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            No services found
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Try adjusting your search or filter criteria
          </Typography>
        </Box>
      )}

      {/* Selected Service Summary */}
      {selectedService && (
        <Box sx={{ mt: 4, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: 1, borderColor: 'divider' }}>
          <Typography variant="h6" gutterBottom>
            Selected Service
          </Typography>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar
                  sx={{
                    bgcolor: `${getCategoryColor(selectedService.category)}.main`,
                    mr: 2,
                  }}
                >
                  {getServiceIcon(selectedService.category)}
                </Avatar>
                <Box>
                  <Typography variant="subtitle1">{selectedService.name}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedService.description}
                  </Typography>
                </Box>
              </Box>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <ScheduleIcon sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                <Typography variant="body2">
                  {selectedService.duration} minutes
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                <MoneyIcon sx={{ fontSize: 16, mr: 0.5, color: 'success.main' }} />
                <Typography variant="h6" color="success.main">
                  ₹{selectedService.price}
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Box>
      )}

      {/* Validation Message */}
      {!selectedService && (
        <Alert severity="info" sx={{ mt: 3 }}>
          Please select a service to continue to the next step.
        </Alert>
      )}

      {/* Next Button */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
        <Button
          variant="contained"
          size="large"
          onClick={handleNext}
          disabled={!selectedService}
        >
          Next: Select Stylist
        </Button>
      </Box>
    </Box>
  );
};

export default ServiceSelection;
