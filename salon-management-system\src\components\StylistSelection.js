import React, { useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  UserIcon,
  StarIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { <PERSON><PERSON>, Card } from './ui';
import { useBooking } from '../contexts/BookingContext';

const StylistSelection = ({ onStylistSelect, onNext, onBack }) => {
  const { 
    selectedService, 
    selectedStylist, 
    getAvailableStylists 
  } = useBooking();

  // Get available stylists for the selected service
  const availableStylists = useMemo(() => {
    if (!selectedService) return [];
    return getAvailableStylists(selectedService.id);
  }, [selectedService, getAvailableStylists]);

  const handleStylistSelect = (stylist) => {
    onStylistSelect(stylist);
  };

  const handleNext = () => {
    if (selectedStylist && onNext) {
      onNext();
    }
  };

  const handleBack = () => {
    if (onBack) {
      onBack();
    }
  };

  // Get working days text
  const getWorkingDaysText = (workingDays) => {
    const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return workingDays.map(day => dayNames[day]).join(', ');
  };

  // Generate avatar gradient based on stylist name
  const getAvatarGradient = (name) => {
    const gradients = [
      'from-blue-500 to-purple-600',
      'from-purple-500 to-pink-600',
      'from-green-500 to-blue-600',
      'from-orange-500 to-red-600',
      'from-pink-500 to-rose-600'
    ];
    const index = name.length % gradients.length;
    return gradients[index];
  };

  if (!selectedService) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600" />
              <p className="text-yellow-800">Please select a service first.</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Select a Stylist</h1>
          <p className="text-lg text-gray-600 mb-1">
            Choose from our qualified stylists for <span className="font-semibold text-purple-600">{selectedService.name}</span>
          </p>
          <p className="text-sm text-gray-500">
            All stylists shown are qualified to perform this service
          </p>
        </motion.div>

        {availableStylists.length === 0 ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6"
          >
            <div className="flex items-center space-x-3">
              <InformationCircleIcon className="h-6 w-6 text-blue-600" />
              <p className="text-blue-800">
                No stylists are currently available for this service. Please try a different service or contact us directly.
              </p>
            </div>
          </motion.div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {availableStylists.map((stylist, index) => (
              <motion.div
                key={stylist.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                className="h-full"
              >
                <Card
                  className={`h-full cursor-pointer transition-all duration-300 hover:shadow-xl ${
                    selectedStylist?.id === stylist.id
                      ? 'ring-2 ring-purple-500 ring-opacity-50 shadow-lg'
                      : 'hover:shadow-lg'
                  }`}
                  onClick={() => handleStylistSelect(stylist)}
                >
                  <div className="p-6">
                    {/* Stylist Header */}
                    <div className="flex items-center space-x-4 mb-4">
                      <div className={`w-14 h-14 rounded-full bg-gradient-to-r ${getAvatarGradient(stylist.name)} flex items-center justify-center`}>
                        <UserIcon className="h-7 w-7 text-white" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900">{stylist.name}</h3>
                        <div className="flex items-center space-x-1 mt-1">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <StarIconSolid
                              key={star}
                              className={`h-4 w-4 ${
                                star <= stylist.rating ? 'text-yellow-400' : 'text-gray-300'
                              }`}
                            />
                          ))}
                          <span className="text-sm text-gray-600 ml-2">({stylist.rating})</span>
                        </div>
                      </div>
                    </div>

                    <div className="border-t border-gray-200 pt-4 mb-4">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Specialties</h4>
                      <div className="flex flex-wrap gap-2">
                        {stylist.specialties.map((specialty, index) => (
                          <span
                            key={index}
                            className={`px-2 py-1 text-xs rounded-full ${
                              selectedService.name.toLowerCase().includes(specialty.toLowerCase()) ||
                              selectedService.category.toLowerCase().includes(specialty.toLowerCase())
                                ? 'bg-purple-100 text-purple-800 border border-purple-200'
                                : 'bg-gray-100 text-gray-700 border border-gray-200'
                            }`}
                          >
                            {specialty}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <ClockIcon className="h-4 w-4" />
                        <span>Working Hours</span>
                      </div>
                      <p className="text-sm font-medium text-gray-900">
                        {stylist.workingHours.start} - {stylist.workingHours.end}
                      </p>
                      <p className="text-sm text-gray-600">
                        {getWorkingDaysText(stylist.workingDays)}
                      </p>
                    </div>

                    {selectedStylist?.id === stylist.id && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="mt-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg p-3 text-center"
                      >
                        <div className="flex items-center justify-center space-x-2">
                          <CheckCircleIcon className="h-5 w-5" />
                          <span className="font-medium">Selected</span>
                        </div>
                      </motion.div>
                    )}
                  </div>
                </Card>
              </motion.div>
          ))}
          </div>
        )}

        {/* Selected Stylist Summary */}
        {selectedStylist && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-8"
          >
            <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Selected Stylist</h3>
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                  <div className="flex items-center space-x-4">
                    <div className={`w-12 h-12 rounded-full bg-gradient-to-r ${getAvatarGradient(selectedStylist.name)} flex items-center justify-center`}>
                      <UserIcon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{selectedStylist.name}</h4>
                      <div className="flex items-center space-x-1 mt-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <StarIconSolid
                            key={star}
                            className={`h-4 w-4 ${
                              star <= selectedStylist.rating ? 'text-yellow-400' : 'text-gray-300'
                            }`}
                          />
                        ))}
                        <span className="text-sm text-gray-600 ml-2">({selectedStylist.rating})</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-sm text-gray-600">
                    <p><span className="font-medium">Working Hours:</span> {selectedStylist.workingHours.start} - {selectedStylist.workingHours.end}</p>
                    <p><span className="font-medium">Days:</span> {getWorkingDaysText(selectedStylist.workingDays)}</p>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Validation Message */}
        {availableStylists.length > 0 && !selectedStylist && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4"
          >
            <div className="flex items-center space-x-3">
              <InformationCircleIcon className="h-5 w-5 text-blue-600" />
              <p className="text-blue-800">Please select a stylist to continue to the next step.</p>
            </div>
          </motion.div>
        )}

        {/* Navigation Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0 mt-8"
        >
          <Button
            variant="outline"
            size="lg"
            onClick={handleBack}
            icon={<ChevronLeftIcon className="h-4 w-4" />}
            iconPosition="left"
            className="w-full sm:w-auto"
          >
            Back: Select Service
          </Button>
          <Button
            variant="primary"
            size="lg"
            onClick={handleNext}
            disabled={!selectedStylist}
            icon={<ChevronRightIcon className="h-4 w-4" />}
            iconPosition="right"
            className={`w-full sm:w-auto ${selectedStylist ? 'bg-gradient-to-r from-purple-500 to-pink-500 hover:shadow-lg' : ''}`}
          >
            Next: Select Date & Time
          </Button>
        </motion.div>
      </div>
    </div>
  );
};

export default StylistSelection;
