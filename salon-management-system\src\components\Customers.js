import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  PhoneIcon,
  EnvelopeIcon,
  ClockIcon,
  ChatBubbleLeftRightIcon,
  StarIcon,
  CalendarDaysIcon,
  UserIcon,
  ChevronDownIcon,
  BellIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  HeartIcon,
  EyeIcon,
  XMarkIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  GiftIcon,
  MapPinIcon,
  CakeIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { Button, Card, Input, Modal } from './ui';
import { Disclosure } from '@headlessui/react';

const Customers = () => {
  const [customers, setCustomers] = useState([
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '(*************',
      address: '123 Main St, City, State 12345',
      joinDate: '2023-06-15',
      totalVisits: 12,
      totalSpent: 1240,
      lastVisit: '2024-01-10',
      preferredStylist: '<PERSON>',
      preferredServices: ['Hair Cut & Style', 'Hair Color'],
      notes: 'Prefers natural hair colors',
      rating: 4.8,
      loyaltyPoints: 124,
      birthday: '1990-03-15',
      notifications: {
        sms: true,
        email: true,
        push: false
      },
      appointmentHistory: [
        { id: 1, date: '2024-01-10', service: 'Hair Cut & Style', stylist: 'Emma Wilson', price: 85, rating: 5, feedback: 'Excellent service as always!' },
        { id: 2, date: '2023-12-15', service: 'Hair Color', stylist: 'Emma Wilson', price: 150, rating: 5, feedback: 'Love the new color!' },
        { id: 3, date: '2023-11-20', service: 'Hair Cut & Style', stylist: 'Emma Wilson', price: 85, rating: 4, feedback: 'Great cut, very professional' }
      ]
    },
    {
      id: 2,
      name: 'Mike Davis',
      email: '<EMAIL>',
      phone: '(*************',
      address: '456 Oak Ave, City, State 12345',
      joinDate: '2023-08-22',
      totalVisits: 8,
      totalSpent: 320,
      lastVisit: '2024-01-15',
      preferredStylist: 'John Smith',
      preferredServices: ['Beard Trim', 'Hair Cut & Style'],
      notes: 'Regular beard trim every 2 weeks',
      rating: 4.5,
      loyaltyPoints: 32,
      birthday: '1985-07-22',
      notifications: {
        sms: true,
        email: false,
        push: true
      },
      appointmentHistory: [
        { id: 4, date: '2024-01-15', service: 'Beard Trim', stylist: 'John Smith', price: 35, rating: 4, feedback: 'Quick and efficient' },
        { id: 5, date: '2024-01-01', service: 'Hair Cut & Style', stylist: 'John Smith', price: 85, rating: 5, feedback: 'Perfect for the new year!' }
      ]
    },
    {
      id: 3,
      name: 'Lisa Brown',
      email: '<EMAIL>',
      phone: '(*************',
      address: '789 Pine St, City, State 12345',
      joinDate: '2023-03-10',
      totalVisits: 18,
      totalSpent: 2150,
      lastVisit: '2024-01-12',
      preferredStylist: 'Emma Wilson',
      preferredServices: ['Hair Color', 'Hair Cut & Style', 'Styling'],
      notes: 'Allergic to certain hair dyes',
      rating: 4.9,
      loyaltyPoints: 215,
      birthday: '1988-11-03',
      notifications: {
        sms: true,
        email: true,
        push: true
      },
      appointmentHistory: [
        { id: 6, date: '2024-01-12', service: 'Hair Color', stylist: 'Emma Wilson', price: 150, rating: 5, feedback: 'Amazing color transformation!' },
        { id: 7, date: '2023-12-20', service: 'Hair Cut & Style', stylist: 'Emma Wilson', price: 85, rating: 5, feedback: 'Always satisfied with Emma\'s work' }
      ]
    },
    {
      id: 4,
      name: 'Tom Wilson',
      email: '<EMAIL>',
      phone: '(*************',
      address: '321 Elm St, City, State 12345',
      joinDate: '2023-11-05',
      totalVisits: 5,
      totalSpent: 450,
      lastVisit: '2024-01-08',
      preferredStylist: 'Mike Johnson',
      preferredServices: ['Full Service'],
      notes: 'Prefers appointments in the afternoon',
      rating: 4.6,
      loyaltyPoints: 45,
      birthday: '1992-05-18',
      notifications: {
        sms: false,
        email: true,
        push: false
      },
      appointmentHistory: [
        { id: 8, date: '2024-01-08', service: 'Full Service', stylist: 'Mike Johnson', price: 120, rating: 5, feedback: 'Comprehensive service, very happy!' }
      ]
    },
  ]);

  const [open, setOpen] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentTab, setCurrentTab] = useState(0);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [customerDetailOpen, setCustomerDetailOpen] = useState(false);
  const [feedbackOpen, setFeedbackOpen] = useState(false);
  const [filterBy, setFilterBy] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [errors, setErrors] = useState({});
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    preferredStylist: '',
    preferredServices: [],
    notes: '',
    birthday: '',
    notifications: {
      sms: true,
      email: true,
      push: false
    }
  });

  const stylists = [
    'Emma Wilson',
    'John Smith',
    'Mike Johnson',
    'Sarah Davis',
    'Lisa Anderson',
  ];

  const services = [
    'Hair Cut & Style',
    'Hair Color',
    'Beard Trim',
    'Full Service',
    'Manicure',
    'Pedicure',
    'Styling',
    'Nail Art'
  ];

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.phone.includes(searchTerm);

    const matchesFilter = filterBy === 'all' ||
      (filterBy === 'vip' && customer.totalSpent > 1000) ||
      (filterBy === 'new' && new Date(customer.joinDate) > new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)) ||
      (filterBy === 'inactive' && new Date(customer.lastVisit) < new Date(Date.now() - 90 * 24 * 60 * 60 * 1000));

    return matchesSearch && matchesFilter;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'visits':
        return b.totalVisits - a.totalVisits;
      case 'spent':
        return b.totalSpent - a.totalSpent;
      case 'rating':
        return b.rating - a.rating;
      case 'lastVisit':
        return new Date(b.lastVisit) - new Date(a.lastVisit);
      default:
        return 0;
    }
  });

  const handleOpen = (customer = null) => {
    if (customer) {
      setEditingCustomer(customer);
      setFormData({
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        address: customer.address,
        preferredStylist: customer.preferredStylist,
        preferredServices: customer.preferredServices || [],
        notes: customer.notes,
        birthday: customer.birthday || '',
        notifications: customer.notifications || {
          sms: true,
          email: true,
          push: false
        }
      });
    } else {
      setEditingCustomer(null);
      setFormData({
        name: '',
        email: '',
        phone: '',
        address: '',
        preferredStylist: '',
        preferredServices: [],
        notes: '',
        birthday: '',
        notifications: {
          sms: true,
          email: true,
          push: false
        }
      });
    }
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setEditingCustomer(null);
    setErrors({});
  };

  const handleSave = () => {
    if (!validateForm()) {
      return;
    }

    // Clean and prepare data
    const cleanedData = {
      ...formData,
      name: formData.name.trim(),
      email: formData.email.trim().toLowerCase(),
      phone: formData.phone.trim(),
      address: formData.address.trim(),
      notes: formData.notes.trim()
    };

    if (editingCustomer) {
      setCustomers(customers.map(customer =>
        customer.id === editingCustomer.id
          ? { ...customer, ...cleanedData }
          : customer
      ));
    } else {
      const newCustomer = {
        ...cleanedData,
        id: Math.max(...customers.map(c => c.id)) + 1,
        joinDate: new Date().toISOString().split('T')[0],
        totalVisits: 0,
        totalSpent: 0,
        lastVisit: 'Never',
        rating: 0,
        loyaltyPoints: 0,
        appointmentHistory: []
      };
      setCustomers([...customers, newCustomer]);
    }
    handleClose();
  };

  const handleCustomerDetail = (customer) => {
    setSelectedCustomer(customer);
    setCustomerDetailOpen(true);
  };

  const handleFeedbackOpen = (customer) => {
    setSelectedCustomer(customer);
    setFeedbackOpen(true);
  };

  const handleDeleteClick = (customer) => {
    setCustomerToDelete(customer);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (customerToDelete) {
      setCustomers(customers.filter(customer => customer.id !== customerToDelete.id));
    }
    setDeleteDialogOpen(false);
    setCustomerToDelete(null);
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setCustomerToDelete(null);
  };

  const handleInputChange = (field, value) => {
    setFormData({ ...formData, [field]: value });
    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Full name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!emailRegex.test(formData.email.trim())) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Phone validation
    const phoneRegex = /^[\d\s\-()\\+]+$/;
    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!phoneRegex.test(formData.phone.trim())) {
      newErrors.phone = 'Please enter a valid phone number';
    } else if (formData.phone.trim().length < 10) {
      newErrors.phone = 'Phone number must be at least 10 digits';
    }

    // Birthday validation (optional but if provided, should be valid)
    if (formData.birthday) {
      const birthDate = new Date(formData.birthday);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();

      if (birthDate > today) {
        newErrors.birthday = 'Birthday cannot be in the future';
      } else if (age > 120) {
        newErrors.birthday = 'Please enter a valid birth date';
      }
    }

    // Address validation (optional but if provided, should have minimum length)
    if (formData.address && formData.address.trim().length < 5) {
      newErrors.address = 'Address must be at least 5 characters if provided';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const getCustomerInitials = (name) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const getCustomerStatus = (customer) => {
    const daysSinceLastVisit = Math.floor((new Date() - new Date(customer.lastVisit)) / (1000 * 60 * 60 * 24));
    if (customer.lastVisit === 'Never') return 'new';
    if (daysSinceLastVisit > 90) return 'inactive';
    if (customer.totalSpent > 1000) return 'vip';
    return 'active';
  };

  const customerStats = {
    total: customers.length,
    newThisMonth: customers.filter(c => {
      const joinDate = new Date(c.joinDate);
      const now = new Date();
      return joinDate.getMonth() === now.getMonth() && joinDate.getFullYear() === now.getFullYear();
    }).length,
    activeCustomers: customers.filter(c => c.lastVisit !== 'Never').length,
    vipCustomers: customers.filter(c => c.totalSpent > 1000).length,
    averageRating: customers.reduce((sum, c) => sum + (c.rating || 0), 0) / customers.length,
    totalRevenue: customers.reduce((sum, c) => sum + c.totalSpent, 0)
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex justify-between items-center mb-8"
        >
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Customer Management</h1>
            <p className="text-gray-600">Manage your salon customers and their information</p>
          </div>
          <Button
            variant="primary"
            onClick={() => handleOpen()}
            icon={<PlusIcon className="h-4 w-4" />}
            className="bg-gradient-to-r from-purple-500 to-pink-500 hover:shadow-lg"
          >
            Add Customer
          </Button>
        </motion.div>

        {/* Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setCurrentTab(0)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                currentTab === 0
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <UserIcon className="h-4 w-4" />
              <span>Customer List</span>
            </button>
            <button
              onClick={() => setCurrentTab(1)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                currentTab === 1
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <ArrowTrendingUpIcon className="h-4 w-4" />
              <span>Customer Analytics</span>
            </button>
          </div>
        </motion.div>

        {currentTab === 0 && (
          <>
            {/* Customer Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
            >
              <Card className="hover:shadow-lg transition-shadow">
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-xl">
                      <UserIcon className="h-6 w-6 text-white" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm text-gray-600">Total Customers</p>
                      <p className="text-2xl font-bold text-gray-900">{customerStats.total}</p>
                    </div>
                  </div>
                </div>
              </Card>
              <Card className="hover:shadow-lg transition-shadow">
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl">
                      <ArrowTrendingUpIcon className="h-6 w-6 text-white" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm text-gray-600">New This Month</p>
                      <p className="text-2xl font-bold text-green-600">{customerStats.newThisMonth}</p>
                    </div>
                  </div>
                </div>
              </Card>

              <Card className="hover:shadow-lg transition-shadow">
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl">
                      <HeartIcon className="h-6 w-6 text-white" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm text-gray-600">VIP Customers</p>
                      <p className="text-2xl font-bold text-purple-600">{customerStats.vipCustomers}</p>
                    </div>
                  </div>
                </div>
              </Card>

              <Card className="hover:shadow-lg transition-shadow">
                <div className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 bg-gradient-to-r from-orange-500 to-red-600 rounded-xl">
                      <StarIcon className="h-6 w-6 text-white" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm text-gray-600">Avg Rating</p>
                      <p className="text-2xl font-bold text-orange-600">{customerStats.averageRating.toFixed(1)}</p>
                    </div>
                  </div>
                </div>
              </Card>
            </motion.div>

            {/* Search and Filters */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mb-8"
            >
              <Card>
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="md:col-span-1">
                      <Input
                        placeholder="Search customers by name, email, or phone..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        icon={<MagnifyingGlassIcon className="h-5 w-5" />}
                        className="w-full"
                      />
                    </div>

                    <div>
                      <select
                        value={filterBy}
                        onChange={(e) => setFilterBy(e.target.value)}
                        className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200 bg-white"
                      >
                        <option value="all">All Customers</option>
                        <option value="vip">VIP Customers</option>
                        <option value="new">New Customers</option>
                        <option value="inactive">Inactive Customers</option>
                      </select>
                    </div>

                    <div>
                      <select
                        value={sortBy}
                        onChange={(e) => setSortBy(e.target.value)}
                        className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200 bg-white"
                      >
                        <option value="name">Sort by Name</option>
                        <option value="visits">Sort by Total Visits</option>
                        <option value="spent">Sort by Total Spent</option>
                        <option value="rating">Sort by Rating</option>
                        <option value="lastVisit">Sort by Last Visit</option>
                      </select>
                    </div>
                  </div>
                </div>
              </Card>
            </motion.div>
        </>
      )}

            {/* Customers Grid */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6"
            >
              {filteredCustomers.map((customer, index) => (
                <motion.div
                  key={customer.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 + index * 0.1 }}
                  whileHover={{ y: -5 }}
                >
                  <Card className="h-full hover:shadow-xl transition-all duration-300">
                    <div className="p-6">
                      {/* Customer Header */}
                      <div className="flex items-center space-x-4 mb-4">
                        <div className="relative">
                          <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white font-semibold ${
                            getCustomerStatus(customer) === 'vip' ? 'bg-gradient-to-r from-yellow-400 to-orange-500' :
                            'bg-gradient-to-r from-purple-500 to-pink-500'
                          }`}>
                            {getCustomerInitials(customer.name)}
                          </div>
                          {customer.loyaltyPoints > 0 && (
                            <div className="absolute -top-2 -right-2 bg-blue-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center">
                              {customer.loyaltyPoints > 99 ? '99+' : customer.loyaltyPoints}
                            </div>
                          )}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900">{customer.name}</h3>
                          <p className="text-sm text-gray-500">Member since {customer.joinDate}</p>
                        </div>
                        <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                          getCustomerStatus(customer) === 'vip' ? 'bg-yellow-100 text-yellow-800' :
                          getCustomerStatus(customer) === 'active' ? 'bg-green-100 text-green-800' :
                          getCustomerStatus(customer) === 'new' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {getCustomerStatus(customer)}
                        </div>
                      </div>

                      {/* Contact Info */}
                      <div className="space-y-2 mb-4">
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <EnvelopeIcon className="h-4 w-4" />
                          <span>{customer.email}</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <PhoneIcon className="h-4 w-4" />
                          <span>{customer.phone}</span>
                        </div>
                      </div>

                      {/* Stats */}
                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div className="text-center">
                          <p className="text-2xl font-bold text-gray-900">{customer.totalVisits}</p>
                          <p className="text-xs text-gray-500">Visits</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-green-600">{formatCurrency(customer.totalSpent)}</p>
                          <p className="text-xs text-gray-500">Spent</p>
                        </div>
                      </div>

                      {/* Rating */}
                      <div className="flex items-center justify-center space-x-1 mb-4">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <StarIconSolid
                            key={star}
                            className={`h-4 w-4 ${
                              star <= customer.rating ? 'text-yellow-400' : 'text-gray-300'
                            }`}
                          />
                        ))}
                        <span className="text-sm text-gray-600 ml-2">({customer.rating})</span>
                      </div>

                      {/* Preferred Stylist */}
                      <div className="mb-4">
                        <p className="text-sm font-medium text-gray-900">{customer.preferredStylist}</p>
                        <p className="text-xs text-gray-500">
                          {customer.preferredServices?.slice(0, 2).join(', ')}
                          {customer.preferredServices?.length > 2 && '...'}
                        </p>
                      </div>

                      {/* Actions */}
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleCustomerDetail(customer)}
                          className="flex-1 px-3 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors text-sm font-medium"
                        >
                          <EyeIcon className="h-4 w-4 inline mr-1" />
                          View
                        </button>
                        <button
                          onClick={() => handleOpen(customer)}
                          className="flex-1 px-3 py-2 bg-green-50 text-green-600 rounded-lg hover:bg-green-100 transition-colors text-sm font-medium"
                        >
                          <PencilIcon className="h-4 w-4 inline mr-1" />
                          Edit
                        </button>
                        <button
                          onClick={() => handleDeleteClick(customer)}
                          className="px-3 py-2 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
        )}

        {/* Customer Analytics Tab */}
        {currentTab === 1 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-6"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="hover:shadow-lg transition-shadow">
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue Analytics</h3>
                  <div className="flex items-center space-x-3">
                    <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl">
                      <CurrencyDollarIcon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <p className="text-3xl font-bold text-green-600">{formatCurrency(customerStats.totalRevenue)}</p>
                      <p className="text-sm text-gray-600">Total Revenue from All Customers</p>
                    </div>
                  </div>
                </div>
              </Card>

              <Card className="hover:shadow-lg transition-shadow">
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Satisfaction</h3>
                  <div className="flex items-center space-x-3">
                    <div className="p-3 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-xl">
                      <StarIcon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <StarIconSolid
                            key={star}
                            className={`h-5 w-5 ${
                              star <= customerStats.averageRating ? 'text-yellow-400' : 'text-gray-300'
                            }`}
                          />
                        ))}
                        <span className="text-2xl font-bold text-gray-900 ml-2">
                          {customerStats.averageRating.toFixed(1)}/5
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">Average Customer Rating</p>
                    </div>
                  </div>
                </div>
              </Card>
            </div>

            <Card className="hover:shadow-lg transition-shadow">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-6">Top Customers</h3>
                <div className="space-y-4">
                  {customers
                    .sort((a, b) => b.totalSpent - a.totalSpent)
                    .slice(0, 5)
                    .map((customer, index) => (
                      <motion.div
                        key={customer.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.3 + index * 0.1 }}
                        className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                      >
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-bold ${
                          index === 0 ? 'bg-gradient-to-r from-yellow-400 to-orange-500' :
                          'bg-gradient-to-r from-purple-500 to-pink-500'
                        }`}>
                          {index + 1}
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{customer.name}</h4>
                          <p className="text-sm text-gray-600">
                            {formatCurrency(customer.totalSpent)} • {customer.totalVisits} visits • {customer.rating}★
                          </p>
                        </div>
                        <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                          getCustomerStatus(customer) === 'vip' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-blue-100 text-blue-800'
                        }`}>
                          {getCustomerStatus(customer)}
                        </div>
                      </motion.div>
                    ))}
                </div>
              </div>
            </Card>
          </motion.div>
        )}
        {/* Customer Detail Modal */}
      <Modal
        isOpen={customerDetailOpen}
        onClose={() => setCustomerDetailOpen(false)}
        title="Customer Details"
        size="xl"
      >
        {selectedCustomer && (
          <div className="space-y-6">
            {/* Customer Header */}
            <div className="flex items-center space-x-4 p-6 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-xl">
                {getCustomerInitials(selectedCustomer.name)}
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">{selectedCustomer.name}</h2>
                <p className="text-gray-600">Customer History & Preferences</p>
              </div>
            </div>

            {/* Customer Summary Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card className="text-center">
                <div className="p-4">
                  <div className="text-2xl font-bold text-blue-600">{selectedCustomer.totalVisits}</div>
                  <div className="text-sm text-gray-600">Total Visits</div>
                </div>
              </Card>
              <Card className="text-center">
                <div className="p-4">
                  <div className="text-2xl font-bold text-green-600">{formatCurrency(selectedCustomer.totalSpent)}</div>
                  <div className="text-sm text-gray-600">Total Spent</div>
                </div>
              </Card>
              <Card className="text-center">
                <div className="p-4">
                  <div className="text-2xl font-bold text-purple-600">{selectedCustomer.loyaltyPoints}</div>
                  <div className="text-sm text-gray-600">Loyalty Points</div>
                </div>
              </Card>
              <Card className="text-center">
                <div className="p-4">
                  <div className="flex justify-center space-x-1 mb-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <StarIconSolid
                        key={star}
                        className={`h-4 w-4 ${
                          star <= selectedCustomer.rating ? 'text-yellow-400' : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <div className="text-sm text-gray-600">Average Rating</div>
                </div>
              </Card>
            </div>

            {/* Preferences */}
            <Disclosure defaultOpen>
              {({ open }) => (
                <>
                  <Disclosure.Button className="flex w-full justify-between rounded-lg bg-gray-100 px-4 py-2 text-left text-sm font-medium text-gray-900 hover:bg-gray-200 focus:outline-none focus-visible:ring focus-visible:ring-purple-500 focus-visible:ring-opacity-75">
                    <span className="text-lg font-semibold">Preferences</span>
                    <ChevronDownIcon
                      className={`${
                        open ? 'rotate-180 transform' : ''
                      } h-5 w-5 text-gray-500`}
                    />
                  </Disclosure.Button>
                  <Disclosure.Panel className="px-4 pt-4 pb-2">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Preferred Stylist</h4>
                        <div className="flex items-center space-x-2 px-3 py-2 bg-blue-50 text-blue-800 rounded-lg">
                          <UserIcon className="h-4 w-4" />
                          <span className="text-sm font-medium">{selectedCustomer.preferredStylist}</span>
                        </div>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Preferred Services</h4>
                        <div className="flex flex-wrap gap-2">
                          {selectedCustomer.preferredServices?.map((service, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full"
                            >
                              {service}
                            </span>
                          ))}
                        </div>
                      </div>
                      <div className="md:col-span-2">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Notes</h4>
                        <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                          {selectedCustomer.notes || 'No special notes'}
                        </p>
                      </div>
                    </div>
                  </Disclosure.Panel>
                </>
              )}
            </Disclosure>

            {/* Appointment History */}
            <Disclosure>
              {({ open }) => (
                <>
                  <Disclosure.Button className="flex w-full justify-between rounded-lg bg-gray-100 px-4 py-2 text-left text-sm font-medium text-gray-900 hover:bg-gray-200 focus:outline-none focus-visible:ring focus-visible:ring-purple-500 focus-visible:ring-opacity-75">
                    <span className="text-lg font-semibold">Appointment History</span>
                    <ChevronDownIcon
                      className={`${
                        open ? 'rotate-180 transform' : ''
                      } h-5 w-5 text-gray-500`}
                    />
                  </Disclosure.Button>
                  <Disclosure.Panel className="px-4 pt-4 pb-2">
                    <div className="space-y-4">
                      {selectedCustomer.appointmentHistory?.map((appointment) => (
                        <div key={appointment.id} className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                            <CalendarDaysIcon className="h-5 w-5 text-white" />
                          </div>
                          <div className="flex-1">
                            <div className="flex justify-between items-start mb-2">
                              <h4 className="font-medium text-gray-900">{appointment.service}</h4>
                              <span className="text-lg font-bold text-green-600">{formatCurrency(appointment.price)}</span>
                            </div>
                            <p className="text-sm text-gray-600 mb-2">
                              {appointment.date} • {appointment.stylist}
                            </p>
                            <div className="flex items-center space-x-2">
                              <div className="flex space-x-1">
                                {[1, 2, 3, 4, 5].map((star) => (
                                  <StarIconSolid
                                    key={star}
                                    className={`h-4 w-4 ${
                                      star <= appointment.rating ? 'text-yellow-400' : 'text-gray-300'
                                    }`}
                                  />
                                ))}
                              </div>
                              <span className="text-sm text-gray-600">"{appointment.feedback}"</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </Disclosure.Panel>
                </>
              )}
            </Disclosure>
          </div>
        )}

        <Modal.Footer>
          <Button variant="outline" onClick={() => setCustomerDetailOpen(false)}>
            Close
          </Button>
          <Button
            variant="primary"
            onClick={() => {
              setCustomerDetailOpen(false);
              handleOpen(selectedCustomer);
            }}
          >
            Edit Customer
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Feedback Modal */}
      <Modal
        isOpen={feedbackOpen}
        onClose={() => setFeedbackOpen(false)}
        title={`Add Feedback for ${selectedCustomer?.name}`}
        size="md"
      >
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-2">
              Service Rating
            </label>
            <div className="flex space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <StarIcon
                  key={star}
                  className="h-8 w-8 text-yellow-400 cursor-pointer hover:text-yellow-500"
                />
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-900 mb-2">
              Feedback Comments
            </label>
            <textarea
              rows={4}
              className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200"
              placeholder="Share your experience with this customer's service..."
            />
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <ChatBubbleLeftRightIcon className="h-5 w-5 text-blue-600 mt-0.5" />
              <p className="text-sm text-blue-800">
                This feedback will be added to the customer's history and help improve service quality.
              </p>
            </div>
          </div>
        </div>

        <Modal.Footer>
          <Button variant="outline" onClick={() => setFeedbackOpen(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={() => setFeedbackOpen(false)}>
            Submit Feedback
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Add/Edit Customer Modal */}
      <Modal
        isOpen={open}
        onClose={handleClose}
        title={editingCustomer ? 'Edit Customer' : 'Add New Customer'}
        size="lg"
      >
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              label="Full Name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              error={errors.name}
              required
              icon={<UserIcon className="h-5 w-5" />}
            />

            <Input
              label="Email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              error={errors.email}
              required
              icon={<EnvelopeIcon className="h-5 w-5" />}
            />

            <Input
              label="Phone"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              error={errors.phone}
              required
              icon={<PhoneIcon className="h-5 w-5" />}
            />

            <Input
              label="Birthday"
              type="date"
              value={formData.birthday}
              onChange={(e) => handleInputChange('birthday', e.target.value)}
              error={errors.birthday}
              icon={<CakeIcon className="h-5 w-5" />}
            />

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Preferred Stylist
              </label>
              <select
                value={formData.preferredStylist}
                onChange={(e) => handleInputChange('preferredStylist', e.target.value)}
                className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200 bg-white"
              >
                <option value="">Select a stylist</option>
                {stylists.map((stylist) => (
                  <option key={stylist} value={stylist}>
                    {stylist}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Preferred Services
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {services.map((service) => (
                <label key={service} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.preferredServices.includes(service)}
                    onChange={(e) => {
                      const newServices = e.target.checked
                        ? [...formData.preferredServices, service]
                        : formData.preferredServices.filter(s => s !== service);
                      handleInputChange('preferredServices', newServices);
                    }}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="text-sm text-gray-700">{service}</span>
                </label>
              ))}
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Address
            </label>
            <textarea
              value={formData.address}
              onChange={(e) => handleInputChange('address', e.target.value)}
              rows={2}
              className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200"
            />
            {errors.address && (
              <p className="mt-1 text-sm text-red-600">{errors.address}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              rows={3}
              placeholder="Any special preferences, allergies, or notes..."
              className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200"
            />
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-3">Notification Preferences</h4>
            <div className="flex space-x-6">
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.notifications.sms}
                  onChange={(e) => handleInputChange('notifications', {
                    ...formData.notifications,
                    sms: e.target.checked
                  })}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="text-sm text-gray-700">SMS</span>
              </label>

              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.notifications.email}
                  onChange={(e) => handleInputChange('notifications', {
                    ...formData.notifications,
                    email: e.target.checked
                  })}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="text-sm text-gray-700">Email</span>
              </label>

              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.notifications.push}
                  onChange={(e) => handleInputChange('notifications', {
                    ...formData.notifications,
                    push: e.target.checked
                  })}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="text-sm text-gray-700">Push Notifications</span>
              </label>
            </div>
          </div>
        </div>

        <Modal.Footer>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleSave}>
            {editingCustomer ? 'Update Customer' : 'Add Customer'}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={deleteDialogOpen}
        onClose={handleDeleteCancel}
        title="Confirm Delete Customer"
        size="md"
      >
        {customerToDelete && (
          <div className="space-y-4">
            <div className="flex items-center space-x-3 p-4 bg-red-50 rounded-lg">
              <ExclamationTriangleIcon className="h-8 w-8 text-red-600" />
              <div>
                <p className="text-lg font-medium text-gray-900">
                  Are you sure you want to delete "{customerToDelete.name}"?
                </p>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4 space-y-2">
              <div className="flex justify-between">
                <span className="font-medium text-gray-700">Email:</span>
                <span className="text-gray-900">{customerToDelete.email}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium text-gray-700">Phone:</span>
                <span className="text-gray-900">{customerToDelete.phone}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium text-gray-700">Total Visits:</span>
                <span className="text-gray-900">{customerToDelete.totalVisits}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium text-gray-700">Total Spent:</span>
                <span className="text-gray-900">${customerToDelete.totalSpent}</span>
              </div>
            </div>

            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-sm text-red-800 font-medium">
                ⚠️ This action cannot be undone. All customer data, appointment history, and feedback will be permanently deleted.
              </p>
            </div>
          </div>
        )}

        <Modal.Footer>
          <Button variant="outline" onClick={handleDeleteCancel}>
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={handleDeleteConfirm}
            icon={<TrashIcon className="h-4 w-4" />}
          >
            Delete Customer
          </Button>
        </Modal.Footer>
      </Modal>
      </div>
    </div>
  );
};

export default Customers;
