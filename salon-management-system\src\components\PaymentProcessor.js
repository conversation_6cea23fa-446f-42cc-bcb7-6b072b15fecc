import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  CreditCardIcon,
  BanknotesIcon,
  BuildingLibraryIcon,
  ReceiptPercentIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  LockClosedIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';
import { Button, Card, Input, Modal } from './ui';
import { useBilling } from '../contexts/BillingContext';

const PaymentProcessor = ({ open, onClose, invoice = null }) => {
  const { processPayment, validateDiscount, applyDiscount } = useBilling();
  
  const [activeStep, setActiveStep] = useState(0);
  const [paymentMethod, setPaymentMethod] = useState('card');
  const [discountCode, setDiscountCode] = useState('');
  const [appliedDiscount, setAppliedDiscount] = useState(null);
  const [discountError, setDiscountError] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  
  const [cardDetails, setCardDetails] = useState({
    number: '',
    expiry: '',
    cvv: '',
    name: '',
    email: '',
    phone: ''
  });

  const [bankDetails, setBankDetails] = useState({
    accountNumber: '',
    routingNumber: '',
    accountType: 'checking',
    bankName: ''
  });

  const steps = ['Payment Method', 'Payment Details', 'Confirmation'];

  const handleDiscountApply = () => {
    if (!discountCode.trim()) {
      setDiscountError('Please enter a discount code');
      return;
    }

    const validation = validateDiscount(discountCode, invoice?.subtotal || 0);
    
    if (validation.valid) {
      setAppliedDiscount(validation.discount);
      setDiscountError('');
    } else {
      setDiscountError(validation.error);
      setAppliedDiscount(null);
    }
  };

  const calculateFinalAmount = () => {
    if (!invoice) return 0;
    
    let amount = invoice.subtotal;
    
    if (appliedDiscount) {
      if (appliedDiscount.type === 'percentage') {
        const discountAmount = Math.min(
          (amount * appliedDiscount.value) / 100,
          appliedDiscount.maxDiscount || Infinity
        );
        amount -= discountAmount;
      } else {
        amount -= appliedDiscount.value;
      }
    }
    
    // Add tax
    const taxAmount = (amount * (invoice.taxRate || 8.5)) / 100;
    return amount + taxAmount;
  };

  const handlePaymentProcess = async () => {
    setIsProcessing(true);
    
    try {
      // Simulate payment processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const paymentData = {
        invoiceId: invoice.id,
        amount: calculateFinalAmount(),
        method: paymentMethod,
        transactionId: `txn_${Date.now()}`,
        gateway: paymentMethod === 'card' ? 'stripe' : 'bank_transfer',
        cardLast4: paymentMethod === 'card' ? cardDetails.number.slice(-4) : null,
        cardBrand: paymentMethod === 'card' ? 'visa' : null
      };
      
      processPayment(paymentData);
      
      if (appliedDiscount) {
        applyDiscount(appliedDiscount.code);
      }
      
      setPaymentSuccess(true);
      setActiveStep(2);
    } catch (error) {
      console.error('Payment processing error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleNext = () => {
    if (activeStep === 1) {
      handlePaymentProcess();
    } else {
      setActiveStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleClose = () => {
    if (!isProcessing) {
      setActiveStep(0);
      setPaymentSuccess(false);
      setAppliedDiscount(null);
      setDiscountCode('');
      setDiscountError('');
      onClose();
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const renderPaymentMethodStep = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Select Payment Method</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <Card
            className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
              paymentMethod === 'card'
                ? 'ring-2 ring-green-500 border-green-500'
                : 'border-gray-300 hover:border-green-300'
            }`}
            onClick={() => setPaymentMethod('card')}
          >
            <div className="p-6 text-center">
              <CreditCardIcon className="h-12 w-12 text-green-600 mx-auto mb-3" />
              <h4 className="text-lg font-semibold text-gray-900 mb-2">Credit/Debit Card</h4>
              <p className="text-sm text-gray-600">
                Visa, Mastercard, American Express
              </p>
            </div>
          </Card>

          <Card
            className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
              paymentMethod === 'bank'
                ? 'ring-2 ring-green-500 border-green-500'
                : 'border-gray-300 hover:border-green-300'
            }`}
            onClick={() => setPaymentMethod('bank')}
          >
            <div className="p-6 text-center">
              <BuildingLibraryIcon className="h-12 w-12 text-green-600 mx-auto mb-3" />
              <h4 className="text-lg font-semibold text-gray-900 mb-2">Bank Transfer</h4>
              <p className="text-sm text-gray-600">
                Direct bank account transfer
              </p>
            </div>
          </Card>
        </div>
      </div>

      {/* Discount Code Section */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="text-md font-medium text-gray-900 mb-3">Have a discount code?</h4>

        <div className="flex space-x-3">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Enter discount code"
              value={discountCode}
              onChange={(e) => setDiscountCode(e.target.value.toUpperCase())}
              className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 ${
                discountError
                  ? 'border-red-500 focus:border-red-500 focus:ring-red-200'
                  : 'border-gray-300 focus:border-green-500 focus:ring-green-200'
              } focus:ring-2`}
            />
            {discountError && (
              <p className="mt-1 text-sm text-red-600">{discountError}</p>
            )}
          </div>
          <Button
            variant="outline"
            onClick={handleDiscountApply}
            disabled={!discountCode.trim()}
            className="px-6"
          >
            Apply
          </Button>
        </div>

        {appliedDiscount && (
          <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <CheckCircleIcon className="h-5 w-5 text-green-600" />
              <div>
                <p className="font-medium text-green-900">
                  Discount Applied: {appliedDiscount.name}
                </p>
                <p className="text-sm text-green-700">
                  {appliedDiscount.type === 'percentage'
                    ? `${appliedDiscount.value}% off`
                    : `${formatCurrency(appliedDiscount.value)} off`}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  const renderPaymentDetailsStep = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Payment Details</h3>

      {paymentMethod === 'card' ? (
        <Card>
          <div className="p-6">
            <div className="flex items-center space-x-2 mb-6">
              <CreditCardIcon className="h-5 w-5 text-green-600" />
              <h4 className="text-md font-semibold text-gray-900">Credit/Debit Card Information</h4>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Card Number
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <CreditCardIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    value={cardDetails.number}
                    onChange={(e) => setCardDetails(prev => ({ ...prev, number: e.target.value }))}
                    placeholder="1234 5678 9012 3456"
                    className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Expiry Date
                  </label>
                  <input
                    type="text"
                    value={cardDetails.expiry}
                    onChange={(e) => setCardDetails(prev => ({ ...prev, expiry: e.target.value }))}
                    placeholder="MM/YY"
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    CVV
                  </label>
                  <input
                    type="text"
                    value={cardDetails.cvv}
                    onChange={(e) => setCardDetails(prev => ({ ...prev, cvv: e.target.value }))}
                    placeholder="123"
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Cardholder Name
                </label>
                <input
                  type="text"
                  value={cardDetails.name}
                  onChange={(e) => setCardDetails(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="John Doe"
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                />
              </div>
            </div>
          </div>
        </Card>
      ) : (
        <Card>
          <div className="p-6">
            <div className="flex items-center space-x-2 mb-6">
              <BuildingLibraryIcon className="h-5 w-5 text-green-600" />
              <h4 className="text-md font-semibold text-gray-900">Bank Transfer Information</h4>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Bank Name
                </label>
                <input
                  type="text"
                  value={bankDetails.bankName}
                  onChange={(e) => setBankDetails(prev => ({ ...prev, bankName: e.target.value }))}
                  placeholder="Bank of America"
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Account Number
                </label>
                <input
                  type="text"
                  value={bankDetails.accountNumber}
                  onChange={(e) => setBankDetails(prev => ({ ...prev, accountNumber: e.target.value }))}
                  placeholder="**********"
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Routing Number
                  </label>
                  <input
                    type="text"
                    value={bankDetails.routingNumber}
                    onChange={(e) => setBankDetails(prev => ({ ...prev, routingNumber: e.target.value }))}
                    placeholder="*********"
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Account Type
                  </label>
                  <select
                    value={bankDetails.accountType}
                    onChange={(e) => setBankDetails(prev => ({ ...prev, accountType: e.target.value }))}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200 bg-white"
                  >
                    <option value="checking">Checking</option>
                    <option value="savings">Savings</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </Card>
      )}
    </div>
  );

  const renderConfirmationStep = () => (
    <div className="text-center py-8">
      {paymentSuccess ? (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", stiffness: 200 }}
        >
          <CheckCircleIcon className="h-16 w-16 text-green-600 mx-auto mb-4" />
          <h3 className="text-2xl font-bold text-gray-900 mb-2">
            Payment Successful!
          </h3>
          <p className="text-gray-600 mb-4">
            Your payment has been processed successfully.
          </p>
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 inline-block">
            <p className="text-lg font-semibold text-green-900">
              Amount Paid: {formatCurrency(calculateFinalAmount())}
            </p>
          </div>
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <div className="animate-spin h-16 w-16 border-4 border-green-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <h3 className="text-2xl font-bold text-gray-900 mb-2">
            Processing Payment...
          </h3>
          <p className="text-gray-600">
            Please wait while we process your payment.
          </p>
        </motion.div>
      )}
    </div>
  );

  return (
    <Modal isOpen={open} onClose={handleClose}>
      <Modal.Header>
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-900">Process Payment</h2>
          {invoice && (
            <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
              Invoice: {invoice.id}
            </span>
          )}
        </div>
      </Modal.Header>

      <Modal.Body>
        {/* Progress Steps */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step} className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-200 ${
                  index <= activeStep
                    ? 'bg-green-600 border-green-600 text-white'
                    : 'border-gray-300 text-gray-400'
                }`}>
                  {index < activeStep ? (
                    <CheckCircleIcon className="h-5 w-5" />
                  ) : (
                    <span className="text-sm font-medium">{index + 1}</span>
                  )}
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-2 transition-all duration-200 ${
                    index < activeStep ? 'bg-green-600' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-between mt-2">
            {steps.map((step, index) => (
              <span key={step} className={`text-xs font-medium ${
                index <= activeStep ? 'text-green-600' : 'text-gray-400'
              }`}>
                {step}
              </span>
            ))}
          </div>
        </div>

        {/* Payment Summary */}
        {invoice && (
          <Card className="mb-6 bg-gray-50">
            <div className="p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Summary</h3>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal:</span>
                  <span className="font-medium">{formatCurrency(invoice.subtotal)}</span>
                </div>

                {appliedDiscount && (
                  <div className="flex justify-between text-green-600">
                    <span>Discount ({appliedDiscount.code}):</span>
                    <span className="font-medium">
                      -{formatCurrency(
                        appliedDiscount.type === 'percentage'
                          ? Math.min((invoice.subtotal * appliedDiscount.value) / 100, appliedDiscount.maxDiscount || Infinity)
                          : appliedDiscount.value
                      )}
                    </span>
                  </div>
                )}

                <div className="flex justify-between">
                  <span className="text-gray-600">Tax ({invoice.taxRate || 8.5}%):</span>
                  <span className="font-medium">
                    {formatCurrency((calculateFinalAmount() - (invoice.subtotal - (appliedDiscount ?
                      (appliedDiscount.type === 'percentage'
                        ? Math.min((invoice.subtotal * appliedDiscount.value) / 100, appliedDiscount.maxDiscount || Infinity)
                        : appliedDiscount.value) : 0))))}
                  </span>
                </div>

                <div className="border-t border-gray-200 pt-2 mt-2">
                  <div className="flex justify-between">
                    <span className="text-lg font-bold text-gray-900">Total:</span>
                    <span className="text-lg font-bold text-green-600">
                      {formatCurrency(calculateFinalAmount())}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        )}

        {/* Step Content */}
        <div className="min-h-[400px]">
          {activeStep === 0 && renderPaymentMethodStep()}
          {activeStep === 1 && renderPaymentDetailsStep()}
          {activeStep === 2 && renderConfirmationStep()}
        </div>
      </Modal.Body>

      <Modal.Footer>
        {!paymentSuccess && (
          <div className="flex justify-between w-full">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isProcessing}
            >
              Cancel
            </Button>

            <div className="flex space-x-3">
              {activeStep > 0 && (
                <Button
                  variant="outline"
                  onClick={handleBack}
                  disabled={isProcessing}
                >
                  Back
              </Button>
              )}

              {activeStep < 2 && (
                <Button
                  onClick={handleNext}
                  variant="primary"
                  disabled={isProcessing}
                  className="bg-gradient-to-r from-green-500 to-green-600 hover:shadow-lg"
                >
                  {activeStep === 1 ? (isProcessing ? 'Processing...' : 'Pay Now') : 'Next'}
                </Button>
              )}
            </div>
          </div>
        )}

        {paymentSuccess && (
          <div className="flex justify-center w-full">
            <Button
              onClick={handleClose}
              variant="primary"
              className="bg-gradient-to-r from-green-500 to-green-600 hover:shadow-lg"
            >
              Close
            </Button>
          </div>
        )}
      </Modal.Footer>
    </Modal>
  );
};

export default PaymentProcessor;
