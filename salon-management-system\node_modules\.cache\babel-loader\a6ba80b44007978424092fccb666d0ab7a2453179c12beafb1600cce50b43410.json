{"ast": null, "code": "var _jsxFileName = \"D:\\\\Project\\\\salon-management-system\\\\src\\\\components\\\\ProductForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { XMarkIcon, CubeIcon, TagIcon, CurrencyDollarIcon, BuildingStorefrontIcon, ClockIcon, ExclamationTriangleIcon, CheckCircleIcon, InformationCircleIcon } from '@heroicons/react/24/outline';\nimport { Button, Card, Input, Modal } from './ui';\nimport { useInventory } from '../contexts/InventoryContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProductForm = ({\n  open,\n  onClose,\n  product = null,\n  mode = 'add'\n}) => {\n  _s();\n  const {\n    addProduct,\n    updateProduct,\n    getCategories,\n    getSuppliers\n  } = useInventory();\n  const [formData, setFormData] = useState({\n    name: '',\n    category: '',\n    brand: '',\n    sku: '',\n    currentStock: 0,\n    minStockLevel: 0,\n    maxStockLevel: 0,\n    unitPrice: 0,\n    supplier: '',\n    description: '',\n    expiryDate: '',\n    location: '',\n    barcode: '',\n    usageRate: 0,\n    status: 'active'\n  });\n  const [errors, setErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const categories = getCategories();\n  const suppliers = getSuppliers();\n\n  // Predefined categories for new products\n  const predefinedCategories = ['Hair Care', 'Hair Color', 'Styling Products', 'Nail Care', 'Skincare', 'Tools & Equipment', 'Cleaning Supplies', 'Other'];\n  const allCategories = [...new Set([...categories, ...predefinedCategories])];\n  useEffect(() => {\n    if (product && mode === 'edit') {\n      setFormData({\n        name: product.name || '',\n        category: product.category || '',\n        brand: product.brand || '',\n        sku: product.sku || '',\n        currentStock: product.currentStock || 0,\n        minStockLevel: product.minStockLevel || 0,\n        maxStockLevel: product.maxStockLevel || 0,\n        unitPrice: product.unitPrice || 0,\n        supplier: product.supplier || '',\n        description: product.description || '',\n        expiryDate: product.expiryDate || '',\n        location: product.location || '',\n        barcode: product.barcode || '',\n        usageRate: product.usageRate || 0,\n        status: product.status || 'active'\n      });\n    } else {\n      // Reset form for add mode\n      setFormData({\n        name: '',\n        category: '',\n        brand: '',\n        sku: '',\n        currentStock: 0,\n        minStockLevel: 0,\n        maxStockLevel: 0,\n        unitPrice: 0,\n        supplier: '',\n        description: '',\n        expiryDate: '',\n        location: '',\n        barcode: '',\n        usageRate: 0,\n        status: 'active'\n      });\n    }\n    setErrors({});\n  }, [product, mode, open]);\n  const handleChange = field => event => {\n    const value = event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.name.trim()) {\n      newErrors.name = 'Product name is required';\n    }\n    if (!formData.category.trim()) {\n      newErrors.category = 'Category is required';\n    }\n    if (!formData.brand.trim()) {\n      newErrors.brand = 'Brand is required';\n    }\n    if (!formData.sku.trim()) {\n      newErrors.sku = 'SKU is required';\n    }\n    if (formData.currentStock < 0) {\n      newErrors.currentStock = 'Current stock cannot be negative';\n    }\n    if (formData.minStockLevel < 0) {\n      newErrors.minStockLevel = 'Minimum stock level cannot be negative';\n    }\n    if (formData.maxStockLevel <= 0) {\n      newErrors.maxStockLevel = 'Maximum stock level must be greater than 0';\n    }\n    if (formData.minStockLevel >= formData.maxStockLevel) {\n      newErrors.minStockLevel = 'Minimum stock level must be less than maximum';\n    }\n    if (formData.unitPrice <= 0) {\n      newErrors.unitPrice = 'Unit price must be greater than 0';\n    }\n    if (!formData.supplier.trim()) {\n      newErrors.supplier = 'Supplier is required';\n    }\n    if (formData.usageRate < 0) {\n      newErrors.usageRate = 'Usage rate cannot be negative';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async () => {\n    if (!validateForm()) {\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      const productData = {\n        ...formData,\n        currentStock: Number(formData.currentStock),\n        minStockLevel: Number(formData.minStockLevel),\n        maxStockLevel: Number(formData.maxStockLevel),\n        unitPrice: Number(formData.unitPrice),\n        usageRate: Number(formData.usageRate)\n      };\n      if (mode === 'edit' && product) {\n        updateProduct(product.id, productData);\n      } else {\n        addProduct(productData);\n      }\n      onClose();\n    } catch (error) {\n      console.error('Error saving product:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleClose = () => {\n    if (!isSubmitting) {\n      onClose();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    isOpen: open,\n    onClose: handleClose,\n    title: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(CubeIcon, {\n          className: \"h-6 w-6 text-white\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900\",\n          children: mode === 'edit' ? 'Edit Product' : 'Add New Product'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: mode === 'edit' ? 'Update product information' : 'Create a new product in your inventory'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 9\n    }, this),\n    size: \"xl\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.1\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(TagIcon, {\n                className: \"h-5 w-5 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900\",\n                children: \"Basic Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  label: \"Product Name\",\n                  value: formData.name,\n                  onChange: handleChange('name'),\n                  error: errors.name,\n                  required: true,\n                  placeholder: \"Enter product name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  label: \"Brand\",\n                  value: formData.brand,\n                  onChange: handleChange('brand'),\n                  error: errors.brand,\n                  required: true,\n                  placeholder: \"Enter brand name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Category *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: formData.category,\n                  onChange: handleChange('category'),\n                  className: `w-full px-4 py-3 rounded-lg border transition-all duration-200 bg-white ${errors.category ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-200'} focus:ring-2`,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select a category\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 21\n                  }, this), allCategories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: category,\n                    children: category\n                  }, category, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 19\n                }, this), errors.category && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-sm text-red-600\",\n                  children: errors.category\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  label: \"SKU\",\n                  value: formData.sku,\n                  onChange: handleChange('sku'),\n                  error: errors.sku,\n                  required: true,\n                  placeholder: \"Enter SKU code\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: formData.description,\n                onChange: handleChange('description'),\n                rows: 3,\n                placeholder: \"Enter product description...\",\n                className: \"w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 resize-none\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(CubeIcon, {\n                className: \"h-5 w-5 text-green-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900\",\n                children: \"Stock Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  label: \"Current Stock\",\n                  type: \"number\",\n                  value: formData.currentStock,\n                  onChange: handleChange('currentStock'),\n                  error: errors.currentStock,\n                  min: \"0\",\n                  placeholder: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  label: \"Minimum Stock Level\",\n                  type: \"number\",\n                  value: formData.minStockLevel,\n                  onChange: handleChange('minStockLevel'),\n                  error: errors.minStockLevel,\n                  min: \"0\",\n                  required: true,\n                  placeholder: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  label: \"Maximum Stock Level\",\n                  type: \"number\",\n                  value: formData.maxStockLevel,\n                  onChange: handleChange('maxStockLevel'),\n                  error: errors.maxStockLevel,\n                  min: \"1\",\n                  required: true,\n                  placeholder: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this), formData.currentStock !== '' && formData.minStockLevel !== '' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6 p-4 rounded-lg bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: parseInt(formData.currentStock) === 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(ExclamationTriangleIcon, {\n                    className: \"h-5 w-5 text-red-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-red-700 font-medium\",\n                    children: \"Out of Stock\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true) : parseInt(formData.currentStock) <= parseInt(formData.minStockLevel) ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(ExclamationTriangleIcon, {\n                    className: \"h-5 w-5 text-yellow-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-yellow-700 font-medium\",\n                    children: \"Low Stock\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"h-5 w-5 text-green-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 382,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-green-700 font-medium\",\n                    children: \"In Stock\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.3\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(CurrencyDollarIcon, {\n                className: \"h-5 w-5 text-purple-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900\",\n                children: \"Pricing & Supplier\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Unit Price *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-500 sm:text-sm\",\n                      children: \"\\u20B9\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 413,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    value: formData.unitPrice,\n                    onChange: handleChange('unitPrice'),\n                    min: \"0\",\n                    step: \"0.01\",\n                    placeholder: \"0.00\",\n                    className: `w-full pl-8 pr-4 py-3 rounded-lg border transition-all duration-200 ${errors.unitPrice ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-200'} focus:ring-2`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 415,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 19\n                }, this), errors.unitPrice && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-sm text-red-600\",\n                  children: errors.unitPrice\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  label: \"Supplier\",\n                  value: formData.supplier,\n                  onChange: handleChange('supplier'),\n                  error: errors.supplier,\n                  required: true,\n                  placeholder: \"Enter supplier name\",\n                  icon: /*#__PURE__*/_jsxDEV(BuildingStorefrontIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.4\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(InformationCircleIcon, {\n                className: \"h-5 w-5 text-indigo-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900\",\n                children: \"Additional Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Expiry Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  value: formData.expiryDate,\n                  onChange: handleChange('expiryDate'),\n                  className: \"w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  label: \"Storage Location\",\n                  value: formData.location,\n                  onChange: handleChange('location'),\n                  placeholder: \"e.g., Storage Room A - Shelf 1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  label: \"Barcode\",\n                  value: formData.barcode,\n                  onChange: handleChange('barcode'),\n                  placeholder: \"Enter barcode\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  label: \"Usage Rate (per week)\",\n                  type: \"number\",\n                  value: formData.usageRate,\n                  onChange: handleChange('usageRate'),\n                  error: errors.usageRate,\n                  min: \"0\",\n                  step: \"0.1\",\n                  placeholder: \"0.0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: formData.status,\n                  onChange: handleChange('status'),\n                  className: \"w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 bg-white\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"active\",\n                    children: \"Active\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 514,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"inactive\",\n                    children: \"Inactive\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"discontinued\",\n                    children: \"Discontinued\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 516,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 449,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline\",\n        onClick: handleClose,\n        disabled: isSubmitting,\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"primary\",\n        onClick: handleSubmit,\n        disabled: isSubmitting,\n        className: \"bg-gradient-to-r from-blue-500 to-purple-600 hover:shadow-lg\",\n        children: isSubmitting ? 'Saving...' : mode === 'edit' ? 'Update Product' : 'Add Product'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 525,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 205,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductForm, \"P/+HSsHEn6oxtacVDhddYiFkvw0=\", false, function () {\n  return [useInventory];\n});\n_c = ProductForm;\nexport default ProductForm;\nvar _c;\n$RefreshReg$(_c, \"ProductForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "XMarkIcon", "CubeIcon", "TagIcon", "CurrencyDollarIcon", "BuildingStorefrontIcon", "ClockIcon", "ExclamationTriangleIcon", "CheckCircleIcon", "InformationCircleIcon", "<PERSON><PERSON>", "Card", "Input", "Modal", "useInventory", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductForm", "open", "onClose", "product", "mode", "_s", "addProduct", "updateProduct", "getCategories", "getSuppliers", "formData", "setFormData", "name", "category", "brand", "sku", "currentStock", "minStockLevel", "maxStockLevel", "unitPrice", "supplier", "description", "expiryDate", "location", "barcode", "usageRate", "status", "errors", "setErrors", "isSubmitting", "setIsSubmitting", "categories", "suppliers", "predefinedCategories", "allCategories", "Set", "handleChange", "field", "event", "value", "target", "prev", "validateForm", "newErrors", "trim", "Object", "keys", "length", "handleSubmit", "productData", "Number", "id", "error", "console", "handleClose", "isOpen", "title", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "div", "initial", "opacity", "y", "animate", "transition", "delay", "label", "onChange", "required", "placeholder", "map", "rows", "type", "min", "parseInt", "step", "icon", "Footer", "variant", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Project/salon-management-system/src/components/ProductForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  XMarkIcon,\n  CubeIcon,\n  TagIcon,\n  CurrencyDollarIcon,\n  BuildingStorefrontIcon,\n  ClockIcon,\n  ExclamationTriangleIcon,\n  CheckCircleIcon,\n  InformationCircleIcon\n} from '@heroicons/react/24/outline';\nimport { Button, Card, Input, Modal } from './ui';\nimport { useInventory } from '../contexts/InventoryContext';\n\nconst ProductForm = ({ open, onClose, product = null, mode = 'add' }) => {\n  const { addProduct, updateProduct, getCategories, getSuppliers } = useInventory();\n  \n  const [formData, setFormData] = useState({\n    name: '',\n    category: '',\n    brand: '',\n    sku: '',\n    currentStock: 0,\n    minStockLevel: 0,\n    maxStockLevel: 0,\n    unitPrice: 0,\n    supplier: '',\n    description: '',\n    expiryDate: '',\n    location: '',\n    barcode: '',\n    usageRate: 0,\n    status: 'active'\n  });\n\n  const [errors, setErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const categories = getCategories();\n  const suppliers = getSuppliers();\n\n  // Predefined categories for new products\n  const predefinedCategories = [\n    'Hair Care',\n    'Hair Color',\n    'Styling Products',\n    'Nail Care',\n    'Skincare',\n    'Tools & Equipment',\n    'Cleaning Supplies',\n    'Other'\n  ];\n\n  const allCategories = [...new Set([...categories, ...predefinedCategories])];\n\n  useEffect(() => {\n    if (product && mode === 'edit') {\n      setFormData({\n        name: product.name || '',\n        category: product.category || '',\n        brand: product.brand || '',\n        sku: product.sku || '',\n        currentStock: product.currentStock || 0,\n        minStockLevel: product.minStockLevel || 0,\n        maxStockLevel: product.maxStockLevel || 0,\n        unitPrice: product.unitPrice || 0,\n        supplier: product.supplier || '',\n        description: product.description || '',\n        expiryDate: product.expiryDate || '',\n        location: product.location || '',\n        barcode: product.barcode || '',\n        usageRate: product.usageRate || 0,\n        status: product.status || 'active'\n      });\n    } else {\n      // Reset form for add mode\n      setFormData({\n        name: '',\n        category: '',\n        brand: '',\n        sku: '',\n        currentStock: 0,\n        minStockLevel: 0,\n        maxStockLevel: 0,\n        unitPrice: 0,\n        supplier: '',\n        description: '',\n        expiryDate: '',\n        location: '',\n        barcode: '',\n        usageRate: 0,\n        status: 'active'\n      });\n    }\n    setErrors({});\n  }, [product, mode, open]);\n\n  const handleChange = (field) => (event) => {\n    const value = event.target.value;\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    \n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'Product name is required';\n    }\n\n    if (!formData.category.trim()) {\n      newErrors.category = 'Category is required';\n    }\n\n    if (!formData.brand.trim()) {\n      newErrors.brand = 'Brand is required';\n    }\n\n    if (!formData.sku.trim()) {\n      newErrors.sku = 'SKU is required';\n    }\n\n    if (formData.currentStock < 0) {\n      newErrors.currentStock = 'Current stock cannot be negative';\n    }\n\n    if (formData.minStockLevel < 0) {\n      newErrors.minStockLevel = 'Minimum stock level cannot be negative';\n    }\n\n    if (formData.maxStockLevel <= 0) {\n      newErrors.maxStockLevel = 'Maximum stock level must be greater than 0';\n    }\n\n    if (formData.minStockLevel >= formData.maxStockLevel) {\n      newErrors.minStockLevel = 'Minimum stock level must be less than maximum';\n    }\n\n    if (formData.unitPrice <= 0) {\n      newErrors.unitPrice = 'Unit price must be greater than 0';\n    }\n\n    if (!formData.supplier.trim()) {\n      newErrors.supplier = 'Supplier is required';\n    }\n\n    if (formData.usageRate < 0) {\n      newErrors.usageRate = 'Usage rate cannot be negative';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async () => {\n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      const productData = {\n        ...formData,\n        currentStock: Number(formData.currentStock),\n        minStockLevel: Number(formData.minStockLevel),\n        maxStockLevel: Number(formData.maxStockLevel),\n        unitPrice: Number(formData.unitPrice),\n        usageRate: Number(formData.usageRate)\n      };\n\n      if (mode === 'edit' && product) {\n        updateProduct(product.id, productData);\n      } else {\n        addProduct(productData);\n      }\n\n      onClose();\n    } catch (error) {\n      console.error('Error saving product:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleClose = () => {\n    if (!isSubmitting) {\n      onClose();\n    }\n  };\n\n  return (\n    <Modal\n      isOpen={open}\n      onClose={handleClose}\n      title={\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg\">\n            <CubeIcon className=\"h-6 w-6 text-white\" />\n          </div>\n          <div>\n            <h2 className=\"text-xl font-semibold text-gray-900\">\n              {mode === 'edit' ? 'Edit Product' : 'Add New Product'}\n            </h2>\n            <p className=\"text-sm text-gray-500\">\n              {mode === 'edit' ? 'Update product information' : 'Create a new product in your inventory'}\n            </p>\n          </div>\n        </div>\n      }\n      size=\"xl\"\n    >\n      <div className=\"space-y-8\">\n        {/* Basic Information Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.1 }}\n        >\n          <Card>\n            <div className=\"p-6\">\n              <div className=\"flex items-center space-x-2 mb-6\">\n                <TagIcon className=\"h-5 w-5 text-blue-600\" />\n                <h3 className=\"text-lg font-semibold text-gray-900\">Basic Information</h3>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <Input\n                    label=\"Product Name\"\n                    value={formData.name}\n                    onChange={handleChange('name')}\n                    error={errors.name}\n                    required\n                    placeholder=\"Enter product name\"\n                  />\n                </div>\n\n                <div>\n                  <Input\n                    label=\"Brand\"\n                    value={formData.brand}\n                    onChange={handleChange('brand')}\n                    error={errors.brand}\n                    required\n                    placeholder=\"Enter brand name\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Category *\n                  </label>\n                  <select\n                    value={formData.category}\n                    onChange={handleChange('category')}\n                    className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 bg-white ${\n                      errors.category ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-200'\n                    } focus:ring-2`}\n                  >\n                    <option value=\"\">Select a category</option>\n                    {allCategories.map((category) => (\n                      <option key={category} value={category}>\n                        {category}\n                      </option>\n                    ))}\n                  </select>\n                  {errors.category && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.category}</p>\n                  )}\n                </div>\n\n                <div>\n                  <Input\n                    label=\"SKU\"\n                    value={formData.sku}\n                    onChange={handleChange('sku')}\n                    error={errors.sku}\n                    required\n                    placeholder=\"Enter SKU code\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"mt-6\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Description\n                </label>\n                <textarea\n                  value={formData.description}\n                  onChange={handleChange('description')}\n                  rows={3}\n                  placeholder=\"Enter product description...\"\n                  className=\"w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 resize-none\"\n                />\n              </div>\n            </div>\n          </Card>\n        </motion.div>\n\n        {/* Stock Information Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n        >\n          <Card>\n            <div className=\"p-6\">\n              <div className=\"flex items-center space-x-2 mb-6\">\n                <CubeIcon className=\"h-5 w-5 text-green-600\" />\n                <h3 className=\"text-lg font-semibold text-gray-900\">Stock Information</h3>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                <div>\n                  <Input\n                    label=\"Current Stock\"\n                    type=\"number\"\n                    value={formData.currentStock}\n                    onChange={handleChange('currentStock')}\n                    error={errors.currentStock}\n                    min=\"0\"\n                    placeholder=\"0\"\n                  />\n                </div>\n\n                <div>\n                  <Input\n                    label=\"Minimum Stock Level\"\n                    type=\"number\"\n                    value={formData.minStockLevel}\n                    onChange={handleChange('minStockLevel')}\n                    error={errors.minStockLevel}\n                    min=\"0\"\n                    required\n                    placeholder=\"0\"\n                  />\n                </div>\n\n                <div>\n                  <Input\n                    label=\"Maximum Stock Level\"\n                    type=\"number\"\n                    value={formData.maxStockLevel}\n                    onChange={handleChange('maxStockLevel')}\n                    error={errors.maxStockLevel}\n                    min=\"1\"\n                    required\n                    placeholder=\"0\"\n                  />\n                </div>\n              </div>\n\n              {/* Stock Status Indicator */}\n              {formData.currentStock !== '' && formData.minStockLevel !== '' && (\n                <div className=\"mt-6 p-4 rounded-lg bg-gray-50\">\n                  <div className=\"flex items-center space-x-2\">\n                    {parseInt(formData.currentStock) === 0 ? (\n                      <>\n                        <ExclamationTriangleIcon className=\"h-5 w-5 text-red-500\" />\n                        <span className=\"text-red-700 font-medium\">Out of Stock</span>\n                      </>\n                    ) : parseInt(formData.currentStock) <= parseInt(formData.minStockLevel) ? (\n                      <>\n                        <ExclamationTriangleIcon className=\"h-5 w-5 text-yellow-500\" />\n                        <span className=\"text-yellow-700 font-medium\">Low Stock</span>\n                      </>\n                    ) : (\n                      <>\n                        <CheckCircleIcon className=\"h-5 w-5 text-green-500\" />\n                        <span className=\"text-green-700 font-medium\">In Stock</span>\n                      </>\n                    )}\n                  </div>\n                </div>\n              )}\n            </div>\n          </Card>\n        </motion.div>\n\n        {/* Pricing and Supplier Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.3 }}\n        >\n          <Card>\n            <div className=\"p-6\">\n              <div className=\"flex items-center space-x-2 mb-6\">\n                <CurrencyDollarIcon className=\"h-5 w-5 text-purple-600\" />\n                <h3 className=\"text-lg font-semibold text-gray-900\">Pricing & Supplier</h3>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Unit Price *\n                  </label>\n                  <div className=\"relative\">\n                    <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                      <span className=\"text-gray-500 sm:text-sm\">₹</span>\n                    </div>\n                    <input\n                      type=\"number\"\n                      value={formData.unitPrice}\n                      onChange={handleChange('unitPrice')}\n                      min=\"0\"\n                      step=\"0.01\"\n                      placeholder=\"0.00\"\n                      className={`w-full pl-8 pr-4 py-3 rounded-lg border transition-all duration-200 ${\n                        errors.unitPrice ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-200'\n                      } focus:ring-2`}\n                    />\n                  </div>\n                  {errors.unitPrice && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.unitPrice}</p>\n                  )}\n                </div>\n\n                <div>\n                  <Input\n                    label=\"Supplier\"\n                    value={formData.supplier}\n                    onChange={handleChange('supplier')}\n                    error={errors.supplier}\n                    required\n                    placeholder=\"Enter supplier name\"\n                    icon={<BuildingStorefrontIcon className=\"h-5 w-5\" />}\n                  />\n                </div>\n              </div>\n            </div>\n          </Card>\n        </motion.div>\n\n        {/* Additional Information Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n        >\n          <Card>\n            <div className=\"p-6\">\n              <div className=\"flex items-center space-x-2 mb-6\">\n                <InformationCircleIcon className=\"h-5 w-5 text-indigo-600\" />\n                <h3 className=\"text-lg font-semibold text-gray-900\">Additional Information</h3>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Expiry Date\n                  </label>\n                  <input\n                    type=\"date\"\n                    value={formData.expiryDate}\n                    onChange={handleChange('expiryDate')}\n                    className=\"w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200\"\n                  />\n                </div>\n\n                <div>\n                  <Input\n                    label=\"Storage Location\"\n                    value={formData.location}\n                    onChange={handleChange('location')}\n                    placeholder=\"e.g., Storage Room A - Shelf 1\"\n                  />\n                </div>\n\n                <div>\n                  <Input\n                    label=\"Barcode\"\n                    value={formData.barcode}\n                    onChange={handleChange('barcode')}\n                    placeholder=\"Enter barcode\"\n                  />\n                </div>\n\n                <div>\n                  <Input\n                    label=\"Usage Rate (per week)\"\n                    type=\"number\"\n                    value={formData.usageRate}\n                    onChange={handleChange('usageRate')}\n                    error={errors.usageRate}\n                    min=\"0\"\n                    step=\"0.1\"\n                    placeholder=\"0.0\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Status\n                  </label>\n                  <select\n                    value={formData.status}\n                    onChange={handleChange('status')}\n                    className=\"w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 bg-white\"\n                  >\n                    <option value=\"active\">Active</option>\n                    <option value=\"inactive\">Inactive</option>\n                    <option value=\"discontinued\">Discontinued</option>\n                  </select>\n                </div>\n              </div>\n            </div>\n          </Card>\n        </motion.div>\n      </div>\n\n      <Modal.Footer>\n        <Button variant=\"outline\" onClick={handleClose} disabled={isSubmitting}>\n          Cancel\n        </Button>\n        <Button\n          variant=\"primary\"\n          onClick={handleSubmit}\n          disabled={isSubmitting}\n          className=\"bg-gradient-to-r from-blue-500 to-purple-600 hover:shadow-lg\"\n        >\n          {isSubmitting ? 'Saving...' : (mode === 'edit' ? 'Update Product' : 'Add Product')}\n        </Button>\n      </Modal.Footer>\n    </Modal>\n  );\n};\n\nexport default ProductForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,SAAS,EACTC,QAAQ,EACRC,OAAO,EACPC,kBAAkB,EAClBC,sBAAsB,EACtBC,SAAS,EACTC,uBAAuB,EACvBC,eAAe,EACfC,qBAAqB,QAChB,6BAA6B;AACpC,SAASC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AACjD,SAASC,YAAY,QAAQ,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5D,MAAMC,WAAW,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,OAAO,GAAG,IAAI;EAAEC,IAAI,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAM;IAAEC,UAAU;IAAEC,aAAa;IAAEC,aAAa;IAAEC;EAAa,CAAC,GAAGd,YAAY,CAAC,CAAC;EAEjF,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC;IACvCkC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,GAAG,EAAE,EAAE;IACPC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC;IAChBC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,CAAC;IACZC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGlD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACmD,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMqD,UAAU,GAAGvB,aAAa,CAAC,CAAC;EAClC,MAAMwB,SAAS,GAAGvB,YAAY,CAAC,CAAC;;EAEhC;EACA,MAAMwB,oBAAoB,GAAG,CAC3B,WAAW,EACX,YAAY,EACZ,kBAAkB,EAClB,WAAW,EACX,UAAU,EACV,mBAAmB,EACnB,mBAAmB,EACnB,OAAO,CACR;EAED,MAAMC,aAAa,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC,CAAC,GAAGJ,UAAU,EAAE,GAAGE,oBAAoB,CAAC,CAAC,CAAC;EAE5EtD,SAAS,CAAC,MAAM;IACd,IAAIwB,OAAO,IAAIC,IAAI,KAAK,MAAM,EAAE;MAC9BO,WAAW,CAAC;QACVC,IAAI,EAAET,OAAO,CAACS,IAAI,IAAI,EAAE;QACxBC,QAAQ,EAAEV,OAAO,CAACU,QAAQ,IAAI,EAAE;QAChCC,KAAK,EAAEX,OAAO,CAACW,KAAK,IAAI,EAAE;QAC1BC,GAAG,EAAEZ,OAAO,CAACY,GAAG,IAAI,EAAE;QACtBC,YAAY,EAAEb,OAAO,CAACa,YAAY,IAAI,CAAC;QACvCC,aAAa,EAAEd,OAAO,CAACc,aAAa,IAAI,CAAC;QACzCC,aAAa,EAAEf,OAAO,CAACe,aAAa,IAAI,CAAC;QACzCC,SAAS,EAAEhB,OAAO,CAACgB,SAAS,IAAI,CAAC;QACjCC,QAAQ,EAAEjB,OAAO,CAACiB,QAAQ,IAAI,EAAE;QAChCC,WAAW,EAAElB,OAAO,CAACkB,WAAW,IAAI,EAAE;QACtCC,UAAU,EAAEnB,OAAO,CAACmB,UAAU,IAAI,EAAE;QACpCC,QAAQ,EAAEpB,OAAO,CAACoB,QAAQ,IAAI,EAAE;QAChCC,OAAO,EAAErB,OAAO,CAACqB,OAAO,IAAI,EAAE;QAC9BC,SAAS,EAAEtB,OAAO,CAACsB,SAAS,IAAI,CAAC;QACjCC,MAAM,EAAEvB,OAAO,CAACuB,MAAM,IAAI;MAC5B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAf,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,EAAE;QACTC,GAAG,EAAE,EAAE;QACPC,YAAY,EAAE,CAAC;QACfC,aAAa,EAAE,CAAC;QAChBC,aAAa,EAAE,CAAC;QAChBC,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,EAAE;QACfC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,EAAE;QACZC,OAAO,EAAE,EAAE;QACXC,SAAS,EAAE,CAAC;QACZC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IACAE,SAAS,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,EAAE,CAACzB,OAAO,EAAEC,IAAI,EAAEH,IAAI,CAAC,CAAC;EAEzB,MAAMmC,YAAY,GAAIC,KAAK,IAAMC,KAAK,IAAK;IACzC,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAChC5B,WAAW,CAAC8B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACJ,KAAK,GAAGE;IACX,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIZ,MAAM,CAACU,KAAK,CAAC,EAAE;MACjBT,SAAS,CAACa,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACJ,KAAK,GAAG;MACX,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACjC,QAAQ,CAACE,IAAI,CAACgC,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAAC/B,IAAI,GAAG,0BAA0B;IAC7C;IAEA,IAAI,CAACF,QAAQ,CAACG,QAAQ,CAAC+B,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAAC9B,QAAQ,GAAG,sBAAsB;IAC7C;IAEA,IAAI,CAACH,QAAQ,CAACI,KAAK,CAAC8B,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAAC7B,KAAK,GAAG,mBAAmB;IACvC;IAEA,IAAI,CAACJ,QAAQ,CAACK,GAAG,CAAC6B,IAAI,CAAC,CAAC,EAAE;MACxBD,SAAS,CAAC5B,GAAG,GAAG,iBAAiB;IACnC;IAEA,IAAIL,QAAQ,CAACM,YAAY,GAAG,CAAC,EAAE;MAC7B2B,SAAS,CAAC3B,YAAY,GAAG,kCAAkC;IAC7D;IAEA,IAAIN,QAAQ,CAACO,aAAa,GAAG,CAAC,EAAE;MAC9B0B,SAAS,CAAC1B,aAAa,GAAG,wCAAwC;IACpE;IAEA,IAAIP,QAAQ,CAACQ,aAAa,IAAI,CAAC,EAAE;MAC/ByB,SAAS,CAACzB,aAAa,GAAG,4CAA4C;IACxE;IAEA,IAAIR,QAAQ,CAACO,aAAa,IAAIP,QAAQ,CAACQ,aAAa,EAAE;MACpDyB,SAAS,CAAC1B,aAAa,GAAG,+CAA+C;IAC3E;IAEA,IAAIP,QAAQ,CAACS,SAAS,IAAI,CAAC,EAAE;MAC3BwB,SAAS,CAACxB,SAAS,GAAG,mCAAmC;IAC3D;IAEA,IAAI,CAACT,QAAQ,CAACU,QAAQ,CAACwB,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACvB,QAAQ,GAAG,sBAAsB;IAC7C;IAEA,IAAIV,QAAQ,CAACe,SAAS,GAAG,CAAC,EAAE;MAC1BkB,SAAS,CAAClB,SAAS,GAAG,+BAA+B;IACvD;IAEAG,SAAS,CAACe,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACI,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACN,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEAZ,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF,MAAMmB,WAAW,GAAG;QAClB,GAAGvC,QAAQ;QACXM,YAAY,EAAEkC,MAAM,CAACxC,QAAQ,CAACM,YAAY,CAAC;QAC3CC,aAAa,EAAEiC,MAAM,CAACxC,QAAQ,CAACO,aAAa,CAAC;QAC7CC,aAAa,EAAEgC,MAAM,CAACxC,QAAQ,CAACQ,aAAa,CAAC;QAC7CC,SAAS,EAAE+B,MAAM,CAACxC,QAAQ,CAACS,SAAS,CAAC;QACrCM,SAAS,EAAEyB,MAAM,CAACxC,QAAQ,CAACe,SAAS;MACtC,CAAC;MAED,IAAIrB,IAAI,KAAK,MAAM,IAAID,OAAO,EAAE;QAC9BI,aAAa,CAACJ,OAAO,CAACgD,EAAE,EAAEF,WAAW,CAAC;MACxC,CAAC,MAAM;QACL3C,UAAU,CAAC2C,WAAW,CAAC;MACzB;MAEA/C,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAOkD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,SAAS;MACRtB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMwB,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACzB,YAAY,EAAE;MACjB3B,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,oBACEL,OAAA,CAACH,KAAK;IACJ6D,MAAM,EAAEtD,IAAK;IACbC,OAAO,EAAEoD,WAAY;IACrBE,KAAK,eACH3D,OAAA;MAAK4D,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1C7D,OAAA;QAAK4D,SAAS,EAAC,6DAA6D;QAAAC,QAAA,eAC1E7D,OAAA,CAACd,QAAQ;UAAC0E,SAAS,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eACNjE,OAAA;QAAA6D,QAAA,gBACE7D,OAAA;UAAI4D,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAChDtD,IAAI,KAAK,MAAM,GAAG,cAAc,GAAG;QAAiB;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACLjE,OAAA;UAAG4D,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EACjCtD,IAAI,KAAK,MAAM,GAAG,4BAA4B,GAAG;QAAwC;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;IACDC,IAAI,EAAC,IAAI;IAAAL,QAAA,gBAET7D,OAAA;MAAK4D,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAExB7D,OAAA,CAACjB,MAAM,CAACoF,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAZ,QAAA,eAE3B7D,OAAA,CAACL,IAAI;UAAAkE,QAAA,eACH7D,OAAA;YAAK4D,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB7D,OAAA;cAAK4D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/C7D,OAAA,CAACb,OAAO;gBAACyE,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7CjE,OAAA;gBAAI4D,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eAENjE,OAAA;cAAK4D,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD7D,OAAA;gBAAA6D,QAAA,eACE7D,OAAA,CAACJ,KAAK;kBACJ8E,KAAK,EAAC,cAAc;kBACpBhC,KAAK,EAAE7B,QAAQ,CAACE,IAAK;kBACrB4D,QAAQ,EAAEpC,YAAY,CAAC,MAAM,CAAE;kBAC/BgB,KAAK,EAAEzB,MAAM,CAACf,IAAK;kBACnB6D,QAAQ;kBACRC,WAAW,EAAC;gBAAoB;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENjE,OAAA;gBAAA6D,QAAA,eACE7D,OAAA,CAACJ,KAAK;kBACJ8E,KAAK,EAAC,OAAO;kBACbhC,KAAK,EAAE7B,QAAQ,CAACI,KAAM;kBACtB0D,QAAQ,EAAEpC,YAAY,CAAC,OAAO,CAAE;kBAChCgB,KAAK,EAAEzB,MAAM,CAACb,KAAM;kBACpB2D,QAAQ;kBACRC,WAAW,EAAC;gBAAkB;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENjE,OAAA;gBAAA6D,QAAA,gBACE7D,OAAA;kBAAO4D,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRjE,OAAA;kBACE0C,KAAK,EAAE7B,QAAQ,CAACG,QAAS;kBACzB2D,QAAQ,EAAEpC,YAAY,CAAC,UAAU,CAAE;kBACnCqB,SAAS,EAAE,2EACT9B,MAAM,CAACd,QAAQ,GAAG,wDAAwD,GAAG,2DAA2D,eAC1H;kBAAA6C,QAAA,gBAEhB7D,OAAA;oBAAQ0C,KAAK,EAAC,EAAE;oBAAAmB,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAC1C5B,aAAa,CAACyC,GAAG,CAAE9D,QAAQ,iBAC1BhB,OAAA;oBAAuB0C,KAAK,EAAE1B,QAAS;oBAAA6C,QAAA,EACpC7C;kBAAQ,GADEA,QAAQ;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEb,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACRnC,MAAM,CAACd,QAAQ,iBACdhB,OAAA;kBAAG4D,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAE/B,MAAM,CAACd;gBAAQ;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAC9D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENjE,OAAA;gBAAA6D,QAAA,eACE7D,OAAA,CAACJ,KAAK;kBACJ8E,KAAK,EAAC,KAAK;kBACXhC,KAAK,EAAE7B,QAAQ,CAACK,GAAI;kBACpByD,QAAQ,EAAEpC,YAAY,CAAC,KAAK,CAAE;kBAC9BgB,KAAK,EAAEzB,MAAM,CAACZ,GAAI;kBAClB0D,QAAQ;kBACRC,WAAW,EAAC;gBAAgB;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENjE,OAAA;cAAK4D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB7D,OAAA;gBAAO4D,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjE,OAAA;gBACE0C,KAAK,EAAE7B,QAAQ,CAACW,WAAY;gBAC5BmD,QAAQ,EAAEpC,YAAY,CAAC,aAAa,CAAE;gBACtCwC,IAAI,EAAE,CAAE;gBACRF,WAAW,EAAC,8BAA8B;gBAC1CjB,SAAS,EAAC;cAAmJ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9J,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGbjE,OAAA,CAACjB,MAAM,CAACoF,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAZ,QAAA,eAE3B7D,OAAA,CAACL,IAAI;UAAAkE,QAAA,eACH7D,OAAA;YAAK4D,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB7D,OAAA;cAAK4D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/C7D,OAAA,CAACd,QAAQ;gBAAC0E,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/CjE,OAAA;gBAAI4D,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eAENjE,OAAA;cAAK4D,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD7D,OAAA;gBAAA6D,QAAA,eACE7D,OAAA,CAACJ,KAAK;kBACJ8E,KAAK,EAAC,eAAe;kBACrBM,IAAI,EAAC,QAAQ;kBACbtC,KAAK,EAAE7B,QAAQ,CAACM,YAAa;kBAC7BwD,QAAQ,EAAEpC,YAAY,CAAC,cAAc,CAAE;kBACvCgB,KAAK,EAAEzB,MAAM,CAACX,YAAa;kBAC3B8D,GAAG,EAAC,GAAG;kBACPJ,WAAW,EAAC;gBAAG;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENjE,OAAA;gBAAA6D,QAAA,eACE7D,OAAA,CAACJ,KAAK;kBACJ8E,KAAK,EAAC,qBAAqB;kBAC3BM,IAAI,EAAC,QAAQ;kBACbtC,KAAK,EAAE7B,QAAQ,CAACO,aAAc;kBAC9BuD,QAAQ,EAAEpC,YAAY,CAAC,eAAe,CAAE;kBACxCgB,KAAK,EAAEzB,MAAM,CAACV,aAAc;kBAC5B6D,GAAG,EAAC,GAAG;kBACPL,QAAQ;kBACRC,WAAW,EAAC;gBAAG;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENjE,OAAA;gBAAA6D,QAAA,eACE7D,OAAA,CAACJ,KAAK;kBACJ8E,KAAK,EAAC,qBAAqB;kBAC3BM,IAAI,EAAC,QAAQ;kBACbtC,KAAK,EAAE7B,QAAQ,CAACQ,aAAc;kBAC9BsD,QAAQ,EAAEpC,YAAY,CAAC,eAAe,CAAE;kBACxCgB,KAAK,EAAEzB,MAAM,CAACT,aAAc;kBAC5B4D,GAAG,EAAC,GAAG;kBACPL,QAAQ;kBACRC,WAAW,EAAC;gBAAG;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGLpD,QAAQ,CAACM,YAAY,KAAK,EAAE,IAAIN,QAAQ,CAACO,aAAa,KAAK,EAAE,iBAC5DpB,OAAA;cAAK4D,SAAS,EAAC,gCAAgC;cAAAC,QAAA,eAC7C7D,OAAA;gBAAK4D,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EACzCqB,QAAQ,CAACrE,QAAQ,CAACM,YAAY,CAAC,KAAK,CAAC,gBACpCnB,OAAA,CAAAE,SAAA;kBAAA2D,QAAA,gBACE7D,OAAA,CAACT,uBAAuB;oBAACqE,SAAS,EAAC;kBAAsB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5DjE,OAAA;oBAAM4D,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,eAC9D,CAAC,GACDiB,QAAQ,CAACrE,QAAQ,CAACM,YAAY,CAAC,IAAI+D,QAAQ,CAACrE,QAAQ,CAACO,aAAa,CAAC,gBACrEpB,OAAA,CAAAE,SAAA;kBAAA2D,QAAA,gBACE7D,OAAA,CAACT,uBAAuB;oBAACqE,SAAS,EAAC;kBAAyB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/DjE,OAAA;oBAAM4D,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,eAC9D,CAAC,gBAEHjE,OAAA,CAAAE,SAAA;kBAAA2D,QAAA,gBACE7D,OAAA,CAACR,eAAe;oBAACoE,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtDjE,OAAA;oBAAM4D,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,eAC5D;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGbjE,OAAA,CAACjB,MAAM,CAACoF,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAZ,QAAA,eAE3B7D,OAAA,CAACL,IAAI;UAAAkE,QAAA,eACH7D,OAAA;YAAK4D,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB7D,OAAA;cAAK4D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/C7D,OAAA,CAACZ,kBAAkB;gBAACwE,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1DjE,OAAA;gBAAI4D,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eAENjE,OAAA;cAAK4D,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD7D,OAAA;gBAAA6D,QAAA,gBACE7D,OAAA;kBAAO4D,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRjE,OAAA;kBAAK4D,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvB7D,OAAA;oBAAK4D,SAAS,EAAC,sEAAsE;oBAAAC,QAAA,eACnF7D,OAAA;sBAAM4D,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,eACNjE,OAAA;oBACEgF,IAAI,EAAC,QAAQ;oBACbtC,KAAK,EAAE7B,QAAQ,CAACS,SAAU;oBAC1BqD,QAAQ,EAAEpC,YAAY,CAAC,WAAW,CAAE;oBACpC0C,GAAG,EAAC,GAAG;oBACPE,IAAI,EAAC,MAAM;oBACXN,WAAW,EAAC,MAAM;oBAClBjB,SAAS,EAAE,uEACT9B,MAAM,CAACR,SAAS,GAAG,wDAAwD,GAAG,2DAA2D;kBAC3H;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EACLnC,MAAM,CAACR,SAAS,iBACftB,OAAA;kBAAG4D,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAE/B,MAAM,CAACR;gBAAS;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAC/D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENjE,OAAA;gBAAA6D,QAAA,eACE7D,OAAA,CAACJ,KAAK;kBACJ8E,KAAK,EAAC,UAAU;kBAChBhC,KAAK,EAAE7B,QAAQ,CAACU,QAAS;kBACzBoD,QAAQ,EAAEpC,YAAY,CAAC,UAAU,CAAE;kBACnCgB,KAAK,EAAEzB,MAAM,CAACP,QAAS;kBACvBqD,QAAQ;kBACRC,WAAW,EAAC,qBAAqB;kBACjCO,IAAI,eAAEpF,OAAA,CAACX,sBAAsB;oBAACuE,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGbjE,OAAA,CAACjB,MAAM,CAACoF,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAZ,QAAA,eAE3B7D,OAAA,CAACL,IAAI;UAAAkE,QAAA,eACH7D,OAAA;YAAK4D,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB7D,OAAA;cAAK4D,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/C7D,OAAA,CAACP,qBAAqB;gBAACmE,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7DjE,OAAA;gBAAI4D,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC,eAENjE,OAAA;cAAK4D,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD7D,OAAA;gBAAA6D,QAAA,gBACE7D,OAAA;kBAAO4D,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRjE,OAAA;kBACEgF,IAAI,EAAC,MAAM;kBACXtC,KAAK,EAAE7B,QAAQ,CAACY,UAAW;kBAC3BkD,QAAQ,EAAEpC,YAAY,CAAC,YAAY,CAAE;kBACrCqB,SAAS,EAAC;gBAAuI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENjE,OAAA;gBAAA6D,QAAA,eACE7D,OAAA,CAACJ,KAAK;kBACJ8E,KAAK,EAAC,kBAAkB;kBACxBhC,KAAK,EAAE7B,QAAQ,CAACa,QAAS;kBACzBiD,QAAQ,EAAEpC,YAAY,CAAC,UAAU,CAAE;kBACnCsC,WAAW,EAAC;gBAAgC;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENjE,OAAA;gBAAA6D,QAAA,eACE7D,OAAA,CAACJ,KAAK;kBACJ8E,KAAK,EAAC,SAAS;kBACfhC,KAAK,EAAE7B,QAAQ,CAACc,OAAQ;kBACxBgD,QAAQ,EAAEpC,YAAY,CAAC,SAAS,CAAE;kBAClCsC,WAAW,EAAC;gBAAe;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENjE,OAAA;gBAAA6D,QAAA,eACE7D,OAAA,CAACJ,KAAK;kBACJ8E,KAAK,EAAC,uBAAuB;kBAC7BM,IAAI,EAAC,QAAQ;kBACbtC,KAAK,EAAE7B,QAAQ,CAACe,SAAU;kBAC1B+C,QAAQ,EAAEpC,YAAY,CAAC,WAAW,CAAE;kBACpCgB,KAAK,EAAEzB,MAAM,CAACF,SAAU;kBACxBqD,GAAG,EAAC,GAAG;kBACPE,IAAI,EAAC,KAAK;kBACVN,WAAW,EAAC;gBAAK;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENjE,OAAA;gBAAA6D,QAAA,gBACE7D,OAAA;kBAAO4D,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRjE,OAAA;kBACE0C,KAAK,EAAE7B,QAAQ,CAACgB,MAAO;kBACvB8C,QAAQ,EAAEpC,YAAY,CAAC,QAAQ,CAAE;kBACjCqB,SAAS,EAAC,gJAAgJ;kBAAAC,QAAA,gBAE1J7D,OAAA;oBAAQ0C,KAAK,EAAC,QAAQ;oBAAAmB,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCjE,OAAA;oBAAQ0C,KAAK,EAAC,UAAU;oBAAAmB,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC1CjE,OAAA;oBAAQ0C,KAAK,EAAC,cAAc;oBAAAmB,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAENjE,OAAA,CAACH,KAAK,CAACwF,MAAM;MAAAxB,QAAA,gBACX7D,OAAA,CAACN,MAAM;QAAC4F,OAAO,EAAC,SAAS;QAACC,OAAO,EAAE9B,WAAY;QAAC+B,QAAQ,EAAExD,YAAa;QAAA6B,QAAA,EAAC;MAExE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTjE,OAAA,CAACN,MAAM;QACL4F,OAAO,EAAC,SAAS;QACjBC,OAAO,EAAEpC,YAAa;QACtBqC,QAAQ,EAAExD,YAAa;QACvB4B,SAAS,EAAC,8DAA8D;QAAAC,QAAA,EAEvE7B,YAAY,GAAG,WAAW,GAAIzB,IAAI,KAAK,MAAM,GAAG,gBAAgB,GAAG;MAAc;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEZ,CAAC;AAACzD,EAAA,CA3gBIL,WAAW;EAAA,QACoDL,YAAY;AAAA;AAAA2F,EAAA,GAD3EtF,WAAW;AA6gBjB,eAAeA,WAAW;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}