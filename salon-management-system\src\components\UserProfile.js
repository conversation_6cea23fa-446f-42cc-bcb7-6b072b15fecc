import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  ShieldCheckIcon,
  UserGroupIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  CogIcon,
  KeyIcon,
  BellIcon,
  EyeIcon,
  LockClosedIcon,
  CalendarDaysIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { Button, Card, Input } from './ui';
import { useAuth } from '../contexts/AuthContext';

const UserProfile = () => {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');
  const [editData, setEditData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    phone: user?.phone || '',
    bio: user?.bio || '',
    department: user?.department || '',
    position: user?.position || ''
  });
  const [message, setMessage] = useState('');

  const handleEditToggle = () => {
    if (isEditing) {
      // Reset data when canceling
      setEditData({
        name: user?.name || '',
        email: user?.email || '',
        phone: user?.phone || '',
        bio: user?.bio || '',
        department: user?.department || '',
        position: user?.position || ''
      });
    }
    setIsEditing(!isEditing);
    setMessage('');
  };

  const handleInputChange = (e) => {
    setEditData({
      ...editData,
      [e.target.name]: e.target.value
    });
  };

  const handleSave = () => {
    // In a real app, this would make an API call to update user data
    setMessage('Profile updated successfully!');
    setIsEditing(false);

    // Simulate updating the user data
    // In real implementation, you'd update the context or make an API call
    setTimeout(() => setMessage(''), 3000);
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'manager':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'staff':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getRoleIcon = (role) => {
    switch (role) {
      case 'admin':
        return <ShieldCheckIcon className="h-4 w-4" />;
      case 'manager':
        return <UserGroupIcon className="h-4 w-4" />;
      case 'staff':
        return <UserIcon className="h-4 w-4" />;
      default:
        return <UserIcon className="h-4 w-4" />;
    }
  };

  const getInitials = (name) => {
    return name
      ? name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
      : 'U';
  };

  const tabs = [
    { id: 'profile', label: 'Profile', icon: UserIcon },
    { id: 'security', label: 'Security', icon: LockClosedIcon },
    { id: 'preferences', label: 'Preferences', icon: CogIcon },
  ];

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="max-w-md w-full">
          <div className="p-6 text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <XMarkIcon className="h-8 w-8 text-red-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">User Not Found</h3>
            <p className="text-gray-600">Please log in again to access your profile.</p>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-900 mb-2">User Profile</h1>
          <p className="text-gray-600">Manage your account settings and preferences</p>
        </motion.div>

        {/* Success Message */}
        {message && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg"
          >
            <div className="flex items-center space-x-2">
              <CheckIcon className="h-5 w-5 text-green-600" />
              <p className="text-green-800 font-medium">{message}</p>
            </div>
          </motion.div>
        )}

        {/* Profile Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="mb-8">
            <div className="p-8">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div className="flex items-center space-x-6 mb-6 lg:mb-0">
                  {/* Avatar */}
                  <div className="relative">
                    <div className="w-24 h-24 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-2xl shadow-lg">
                      {getInitials(user.name)}
                    </div>
                    <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white flex items-center justify-center">
                      <div className="w-3 h-3 bg-white rounded-full"></div>
                    </div>
                  </div>

                  {/* User Info */}
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">{user.name}</h2>
                    <div className="flex flex-wrap gap-2 mb-3">
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getRoleColor(user.role)}`}>
                        {getRoleIcon(user.role)}
                        <span className="ml-1">{user.role.charAt(0).toUpperCase() + user.role.slice(1)}</span>
                      </span>
                      {user.position && (
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 border border-gray-200">
                          {user.position}
                        </span>
                      )}
                      {user.department && (
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-50 text-blue-700 border border-blue-200">
                          {user.department}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <div className="flex items-center space-x-1">
                        <EnvelopeIcon className="h-4 w-4" />
                        <span>{user.email}</span>
                      </div>
                      {user.phone && (
                        <div className="flex items-center space-x-1">
                          <PhoneIcon className="h-4 w-4" />
                          <span>{user.phone}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Action Button */}
                <div className="flex space-x-3">
                  <Button
                    variant={isEditing ? "outline" : "primary"}
                    onClick={handleEditToggle}
                    className={isEditing ? "" : "bg-gradient-to-r from-purple-500 to-pink-500 hover:shadow-lg"}
                  >
                    {isEditing ? (
                      <>
                        <XMarkIcon className="h-4 w-4 mr-2" />
                        Cancel
                      </>
                    ) : (
                      <>
                        <PencilIcon className="h-4 w-4 mr-2" />
                        Edit Profile
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Tab Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                    activeTab === tab.id
                      ? 'border-purple-500 text-purple-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="h-5 w-5" />
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>
        </motion.div>

        {/* Tab Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {activeTab === 'profile' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card>
                  <div className="p-6">
                    <div className="flex items-center space-x-2 mb-6">
                      <UserIcon className="h-5 w-5 text-purple-600" />
                      <h3 className="text-lg font-semibold text-gray-900">Personal Information</h3>
                    </div>

                    <div className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Input
                          label="Full Name"
                          name="name"
                          value={isEditing ? editData.name : user.name}
                          onChange={handleInputChange}
                          disabled={!isEditing}
                          icon={<UserIcon className="h-4 w-4" />}
                        />

                        <Input
                          label="Email Address"
                          name="email"
                          type="email"
                          value={isEditing ? editData.email : user.email}
                          onChange={handleInputChange}
                          disabled={!isEditing}
                          icon={<EnvelopeIcon className="h-4 w-4" />}
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Input
                          label="Phone Number"
                          name="phone"
                          value={isEditing ? editData.phone : user.phone}
                          onChange={handleInputChange}
                          disabled={!isEditing}
                          icon={<PhoneIcon className="h-4 w-4" />}
                          placeholder="Enter phone number"
                        />

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Username
                          </label>
                          <input
                            type="text"
                            value={user.username}
                            disabled
                            className="w-full px-4 py-3 rounded-lg border border-gray-300 bg-gray-50 text-gray-500 cursor-not-allowed"
                          />
                          <p className="mt-1 text-sm text-gray-500">Username cannot be changed</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Input
                          label="Department"
                          name="department"
                          value={isEditing ? editData.department : user.department}
                          onChange={handleInputChange}
                          disabled={!isEditing}
                          placeholder="Enter department"
                        />

                        <Input
                          label="Position"
                          name="position"
                          value={isEditing ? editData.position : user.position}
                          onChange={handleInputChange}
                          disabled={!isEditing}
                          placeholder="Enter position"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Bio
                        </label>
                        <textarea
                          name="bio"
                          value={isEditing ? editData.bio : user.bio}
                          onChange={handleInputChange}
                          disabled={!isEditing}
                          rows={4}
                          placeholder="Tell us about yourself..."
                          className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 resize-none ${
                            isEditing
                              ? 'border-gray-300 focus:border-purple-500 focus:ring-2 focus:ring-purple-200'
                              : 'border-gray-300 bg-gray-50 text-gray-500 cursor-not-allowed'
                          }`}
                        />
                      </div>

                      {isEditing && (
                        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                          <Button
                            variant="outline"
                            onClick={handleEditToggle}
                          >
                            <XMarkIcon className="h-4 w-4 mr-2" />
                            Cancel
                          </Button>
                          <Button
                            variant="primary"
                            onClick={handleSave}
                            className="bg-gradient-to-r from-purple-500 to-pink-500 hover:shadow-lg"
                          >
                            <CheckIcon className="h-4 w-4 mr-2" />
                            Save Changes
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </Card>
              </motion.div>
            )}

            {activeTab === 'security' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card>
                  <div className="p-6">
                    <div className="flex items-center space-x-2 mb-6">
                      <LockClosedIcon className="h-5 w-5 text-purple-600" />
                      <h3 className="text-lg font-semibold text-gray-900">Security Settings</h3>
                    </div>

                    <div className="space-y-6">
                      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div className="flex items-center space-x-2">
                          <KeyIcon className="h-5 w-5 text-yellow-600" />
                          <h4 className="font-medium text-yellow-800">Password Security</h4>
                        </div>
                        <p className="text-sm text-yellow-700 mt-1">
                          Last changed 30 days ago. Consider updating your password regularly.
                        </p>
                        <Button variant="outline" className="mt-3">
                          Change Password
                        </Button>
                      </div>

                      <div className="space-y-4">
                        <h4 className="font-medium text-gray-900">Login Activity</h4>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center space-x-3">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              <div>
                                <p className="text-sm font-medium text-gray-900">Current Session</p>
                                <p className="text-xs text-gray-500">Chrome on Windows • 192.168.1.1</p>
                              </div>
                            </div>
                            <span className="text-xs text-gray-500">Active now</span>
                          </div>

                          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center space-x-3">
                              <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                              <div>
                                <p className="text-sm font-medium text-gray-900">Previous Session</p>
                                <p className="text-xs text-gray-500">Safari on iPhone • ***********</p>
                              </div>
                            </div>
                            <span className="text-xs text-gray-500">2 hours ago</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            )}

            {activeTab === 'preferences' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card>
                  <div className="p-6">
                    <div className="flex items-center space-x-2 mb-6">
                      <CogIcon className="h-5 w-5 text-purple-600" />
                      <h3 className="text-lg font-semibold text-gray-900">Preferences</h3>
                    </div>

                    <div className="space-y-6">
                      <div>
                        <h4 className="font-medium text-gray-900 mb-4">Notifications</h4>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <BellIcon className="h-5 w-5 text-gray-400" />
                              <div>
                                <p className="text-sm font-medium text-gray-900">Email Notifications</p>
                                <p className="text-xs text-gray-500">Receive updates via email</p>
                              </div>
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input type="checkbox" className="sr-only peer" defaultChecked />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                            </label>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <EyeIcon className="h-5 w-5 text-gray-400" />
                              <div>
                                <p className="text-sm font-medium text-gray-900">Activity Visibility</p>
                                <p className="text-xs text-gray-500">Show your activity to other users</p>
                              </div>
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input type="checkbox" className="sr-only peer" />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                            </label>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium text-gray-900 mb-4">Display</h4>
                        <div className="space-y-3">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Theme
                            </label>
                            <select className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all duration-200 bg-white">
                              <option>Light</option>
                              <option>Dark</option>
                              <option>System</option>
                            </select>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Language
                            </label>
                            <select className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all duration-200 bg-white">
                              <option>English</option>
                              <option>Spanish</option>
                              <option>French</option>
                            </select>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Role & Permissions */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
            >
              <Card>
                <div className="p-6">
                  <div className="flex items-center space-x-2 mb-4">
                    <ShieldCheckIcon className="h-5 w-5 text-purple-600" />
                    <h3 className="text-lg font-semibold text-gray-900">Access & Permissions</h3>
                  </div>

                  <div className="space-y-4">
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Role</h4>
                      <span className={`inline-flex items-center px-3 py-2 rounded-full text-sm font-medium border ${getRoleColor(user.role)}`}>
                        {getRoleIcon(user.role)}
                        <span className="ml-2">{user.role.charAt(0).toUpperCase() + user.role.slice(1)}</span>
                      </span>
                    </div>

                    {user.permissions && (
                      <div className="p-4 bg-gray-50 rounded-lg">
                        <h4 className="text-sm font-medium text-gray-700 mb-3">Permissions</h4>
                        <div className="flex flex-wrap gap-2">
                          {user.permissions.map((permission, index) => (
                            <span
                              key={index}
                              className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200"
                            >
                              {permission}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    {user.role === 'admin' && (
                      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="flex items-center space-x-2">
                          <ShieldCheckIcon className="h-5 w-5 text-blue-600" />
                          <p className="text-sm font-medium text-blue-800">Administrator Access</p>
                        </div>
                        <p className="text-sm text-blue-700 mt-1">
                          You have full access to all features and settings.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            </motion.div>

            {/* Quick Stats */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5 }}
            >
              <Card>
                <div className="p-6">
                  <div className="flex items-center space-x-2 mb-4">
                    <CalendarDaysIcon className="h-5 w-5 text-purple-600" />
                    <h3 className="text-lg font-semibold text-gray-900">Account Info</h3>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Member since</span>
                      <span className="text-sm font-medium text-gray-900">
                        {user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'Jan 2024'}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Last login</span>
                      <span className="text-sm font-medium text-gray-900">
                        {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Today'}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Status</span>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1"></div>
                        Active
                      </span>
                    </div>

                    <div className="pt-4 border-t border-gray-200">
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <ClockIcon className="h-4 w-4" />
                        <span>Profile updated {user.updatedAt ? new Date(user.updatedAt).toLocaleDateString() : 'recently'}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfile;
