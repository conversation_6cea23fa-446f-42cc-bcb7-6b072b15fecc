{"ast": null, "code": "var _jsxFileName = \"D:\\\\Project\\\\salon-management-system\\\\src\\\\components\\\\BookingConfirmation.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Grid, Card, CardContent, Button, TextField, Divider, Alert, Paper, Avatar, Chip, Dialog, DialogTitle, DialogContent, DialogActions, CircularProgress } from '@mui/material';\nimport { Person as PersonIcon, Schedule as ScheduleIcon, CalendarToday as CalendarIcon, AttachMoney as MoneyIcon, ContentCut as ServiceIcon, Phone as PhoneIcon, Email as EmailIcon, Notes as NotesIcon, CheckCircle as CheckIcon } from '@mui/icons-material';\nimport { format } from 'date-fns';\nimport { useBooking } from '../contexts/BookingContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BookingConfirmation = ({\n  onConfirm,\n  onBack\n}) => {\n  _s();\n  const {\n    selectedService,\n    selectedStylist,\n    selectedDate,\n    selectedTime,\n    customerInfo,\n    setCustomerInfo,\n    createAppointment,\n    resetBooking\n  } = useBooking();\n  const [loading, setLoading] = useState(false);\n  const [confirmationDialog, setConfirmationDialog] = useState(false);\n  const [bookingComplete, setBookingComplete] = useState(false);\n  const [newAppointment, setNewAppointment] = useState(null);\n  const [errors, setErrors] = useState({});\n  const handleInputChange = (field, value) => {\n    // Apply field-specific restrictions\n    let processedValue = value;\n    if (field === 'phone') {\n      // Only allow digits - remove all non-digit characters\n      processedValue = value.replace(/[^\\d]/g, '');\n\n      // Limit to exactly 10 digits\n      if (processedValue.length > 10) {\n        processedValue = processedValue.substring(0, 10);\n      }\n    } else if (field === 'name') {\n      // Limit name to 50 characters\n      if (value.length > 50) {\n        processedValue = value.substring(0, 50);\n      }\n    } else if (field === 'email') {\n      // Remove leading/trailing spaces and limit email to 100 characters\n      processedValue = value.trim();\n      if (processedValue.length > 100) {\n        processedValue = processedValue.substring(0, 100);\n      }\n    } else if (field === 'notes') {\n      // Limit notes to 500 characters\n      if (value.length > 500) {\n        processedValue = value.substring(0, 500);\n      }\n    }\n    setCustomerInfo({\n      ...customerInfo,\n      [field]: processedValue\n    });\n\n    // Clear error for this field when user starts typing\n    if (errors[field]) {\n      setErrors({\n        ...errors,\n        [field]: ''\n      });\n    }\n  };\n  const handleConfirmBooking = () => {\n    if (validateForm()) {\n      setConfirmationDialog(true);\n    }\n  };\n  const handleFinalConfirm = async () => {\n    setLoading(true);\n    setConfirmationDialog(false);\n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      const appointmentData = {\n        customer: customerInfo.name,\n        phone: customerInfo.phone,\n        email: customerInfo.email,\n        service: selectedService.name,\n        serviceId: selectedService.id,\n        stylist: selectedStylist.name,\n        stylistId: selectedStylist.id,\n        date: selectedDate,\n        time: selectedTime,\n        duration: selectedService.duration,\n        price: selectedService.price,\n        notes: customerInfo.notes\n      };\n      const appointment = createAppointment(appointmentData);\n      setNewAppointment(appointment);\n      setBookingComplete(true);\n      if (onConfirm) {\n        onConfirm(appointment);\n      }\n    } catch (error) {\n      console.error('Booking failed:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleNewBooking = () => {\n    resetBooking();\n    setBookingComplete(false);\n    setNewAppointment(null);\n  };\n  const handleBack = () => {\n    if (onBack) {\n      onBack();\n    }\n  };\n\n  // Validate form fields\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Name validation\n    if (!customerInfo.name.trim()) {\n      newErrors.name = 'Full name is required';\n    } else if (customerInfo.name.trim().length < 2) {\n      newErrors.name = 'Name must be at least 2 characters long';\n    }\n\n    // Phone validation - only pure digits allowed\n    if (!customerInfo.phone.trim()) {\n      newErrors.phone = 'Phone number is required';\n    } else {\n      // Check if phone contains only digits (no formatting allowed)\n      if (!/^\\d{10}$/.test(customerInfo.phone)) {\n        if (!/^\\d+$/.test(customerInfo.phone)) {\n          newErrors.phone = 'Phone number can only contain digits (no spaces, hyphens, or other characters)';\n        } else if (customerInfo.phone.length !== 10) {\n          newErrors.phone = 'Phone number must be exactly 10 digits';\n        }\n      }\n    }\n\n    // Email validation (optional but if provided, must be valid)\n    if (customerInfo.email.trim()) {\n      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\n      const emailValue = customerInfo.email.trim();\n      if (!emailRegex.test(emailValue)) {\n        newErrors.email = 'Please enter a valid email address (e.g., <EMAIL>)';\n      }\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const isFormValid = () => {\n    return customerInfo.name.trim() && customerInfo.phone.trim() && Object.keys(errors).length === 0;\n  };\n  if (bookingComplete && newAppointment) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CheckIcon, {\n        sx: {\n          fontSize: 80,\n          color: 'success.main',\n          mb: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        color: \"success.main\",\n        children: \"Booking Confirmed!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 3\n        },\n        children: \"Your appointment has been successfully booked.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3,\n          textAlign: 'left'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Appointment Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Customer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: newAppointment.customer\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Phone\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: newAppointment.phone\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Service\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: newAppointment.service\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Stylist\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: newAppointment.stylist\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Date & Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [format(new Date(newAppointment.date), 'EEEE, MMMM d, yyyy'), \" at \", newAppointment.time]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Total Price\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              color: \"success.main\",\n              children: [\"\\u20B9\", newAppointment.price]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mb: 3\n        },\n        children: \"Please arrive 10 minutes before your appointment time. You will receive a confirmation email shortly.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        size: \"large\",\n        onClick: handleNewBooking,\n        children: \"Book Another Appointment\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this);\n  }\n  if (!selectedService || !selectedStylist || !selectedDate || !selectedTime) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        children: \"Please complete all previous steps before confirming your booking.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Confirm Your Booking\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      color: \"text.secondary\",\n      sx: {\n        mb: 3\n      },\n      children: \"Please review your appointment details and provide your contact information\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            height: 'fit-content'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Appointment Summary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(ServiceIcon, {\n                sx: {\n                  mr: 2,\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  children: selectedService.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: selectedService.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  mr: 2,\n                  bgcolor: 'secondary.main'\n                },\n                children: /*#__PURE__*/_jsxDEV(PersonIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  children: selectedStylist.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Rating: \", selectedStylist.rating, \"/5\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(CalendarIcon, {\n                sx: {\n                  mr: 2,\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  children: format(new Date(selectedDate), 'EEEE, MMMM d, yyyy')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"at \", selectedTime]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(ScheduleIcon, {\n                sx: {\n                  mr: 2,\n                  color: 'primary.main'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                children: [selectedService.duration, \" minutes\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                children: \"Total Price:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                color: \"success.main\",\n                children: [\"$\", selectedService.price]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Your Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Full Name\",\n                value: customerInfo.name,\n                onChange: e => handleInputChange('name', e.target.value),\n                required: true,\n                error: !!errors.name,\n                helperText: errors.name || `${customerInfo.name.length}/50 characters`,\n                InputProps: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(PersonIcon, {\n                    sx: {\n                      mr: 1,\n                      color: 'text.secondary'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 37\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Phone Number\",\n                value: customerInfo.phone,\n                onChange: e => handleInputChange('phone', e.target.value),\n                required: true,\n                type: \"tel\",\n                inputMode: \"numeric\",\n                pattern: \"[0-9]*\",\n                error: !!errors.phone,\n                helperText: errors.phone || `Enter exactly 10 digits only (e.g., 9876543210) - ${customerInfo.phone.length}/10`,\n                InputProps: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(PhoneIcon, {\n                    sx: {\n                      mr: 1,\n                      color: 'text.secondary'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 371,\n                    columnNumber: 37\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Email Address (Optional)\",\n                type: \"email\",\n                value: customerInfo.email,\n                onChange: e => handleInputChange('email', e.target.value),\n                error: !!errors.email,\n                helperText: errors.email || `Optional: We'll send confirmation to this email (${customerInfo.email.length}/100)`,\n                InputProps: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(EmailIcon, {\n                    sx: {\n                      mr: 1,\n                      color: 'text.secondary'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 37\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Special Notes (Optional)\",\n                multiline: true,\n                rows: 3,\n                value: customerInfo.notes,\n                onChange: e => handleInputChange('notes', e.target.value),\n                placeholder: \"Any special requests or notes for your stylist...\",\n                helperText: `${customerInfo.notes.length}/500 characters`,\n                InputProps: {\n                  startAdornment: /*#__PURE__*/_jsxDEV(NotesIcon, {\n                    sx: {\n                      mr: 1,\n                      color: 'text.secondary',\n                      alignSelf: 'flex-start',\n                      mt: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 402,\n                    columnNumber: 37\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        size: \"large\",\n        onClick: handleBack,\n        children: \"Back: Select Time\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        size: \"large\",\n        onClick: handleConfirmBooking,\n        disabled: !isFormValid(),\n        children: \"Confirm Booking\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 412,\n      columnNumber: 7\n    }, this), Object.keys(errors).length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mt: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        gutterBottom: true,\n        children: \"Please fix the following errors:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        style: {\n          margin: 0,\n          paddingLeft: 20\n        },\n        children: Object.entries(errors).map(([field, error]) => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 17\n          }, this)\n        }, field, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 432,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: confirmationDialog,\n      onClose: () => setConfirmationDialog(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Confirm Your Appointment\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          sx: {\n            mb: 2\n          },\n          children: \"Are you sure you want to book this appointment?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Service:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 13\n          }, this), \" \", selectedService.name, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 61\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Stylist:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this), \" \", selectedStylist.name, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 61\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Date & Time:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 13\n          }, this), \" \", format(new Date(selectedDate), 'EEEE, MMMM d, yyyy'), \" at \", selectedTime, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 115\n          }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Price:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this), \" $\", selectedService.price]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 449,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setConfirmationDialog(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleFinalConfirm,\n          variant: \"contained\",\n          disabled: loading,\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 24\n          }, this) : 'Confirm'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 460,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 447,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 265,\n    columnNumber: 5\n  }, this);\n};\n_s(BookingConfirmation, \"1Bvm5ju9OefTPLLBAJrWe78Lnow=\", false, function () {\n  return [useBooking];\n});\n_c = BookingConfirmation;\nexport default BookingConfirmation;\nvar _c;\n$RefreshReg$(_c, \"BookingConfirmation\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "TextField", "Divider", "<PERSON><PERSON>", "Paper", "Avatar", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "CircularProgress", "Person", "PersonIcon", "Schedule", "ScheduleIcon", "CalendarToday", "CalendarIcon", "AttachMoney", "MoneyIcon", "ContentCut", "ServiceIcon", "Phone", "PhoneIcon", "Email", "EmailIcon", "Notes", "NotesIcon", "CheckCircle", "CheckIcon", "format", "useBooking", "jsxDEV", "_jsxDEV", "BookingConfirmation", "onConfirm", "onBack", "_s", "selectedService", "selectedStylist", "selectedDate", "selectedTime", "customerInfo", "setCustomerInfo", "createAppointment", "resetBooking", "loading", "setLoading", "confirmationDialog", "setConfirmationDialog", "bookingComplete", "setBookingComplete", "newAppointment", "setNewAppointment", "errors", "setErrors", "handleInputChange", "field", "value", "processedValue", "replace", "length", "substring", "trim", "handleConfirmBooking", "validateForm", "handleFinalConfirm", "Promise", "resolve", "setTimeout", "appointmentData", "customer", "name", "phone", "email", "service", "serviceId", "id", "stylist", "stylistId", "date", "time", "duration", "price", "notes", "appointment", "error", "console", "handleNewBooking", "handleBack", "newErrors", "test", "emailRegex", "emailValue", "Object", "keys", "isFormValid", "sx", "p", "textAlign", "children", "fontSize", "color", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "gutterBottom", "container", "spacing", "item", "xs", "sm", "Date", "severity", "size", "onClick", "md", "height", "display", "alignItems", "mr", "description", "bgcolor", "rating", "my", "justifyContent", "fullWidth", "label", "onChange", "e", "target", "required", "helperText", "InputProps", "startAdornment", "type", "inputMode", "pattern", "multiline", "rows", "placeholder", "alignSelf", "mt", "disabled", "style", "margin", "paddingLeft", "entries", "map", "open", "onClose", "_c", "$RefreshReg$"], "sources": ["D:/Project/salon-management-system/src/components/BookingConfirmation.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typo<PERSON>,\n  Grid,\n  Card,\n  CardContent,\n  Button,\n  TextField,\n  Divider,\n  Alert,\n  Paper,\n  Avatar,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  CircularProgress,\n} from '@mui/material';\nimport {\n  Person as PersonIcon,\n  Schedule as ScheduleIcon,\n  CalendarToday as CalendarIcon,\n  AttachMoney as MoneyIcon,\n  ContentCut as ServiceIcon,\n  Phone as PhoneIcon,\n  Email as EmailIcon,\n  Notes as NotesIcon,\n  CheckCircle as CheckIcon,\n} from '@mui/icons-material';\nimport { format } from 'date-fns';\nimport { useBooking } from '../contexts/BookingContext';\n\nconst BookingConfirmation = ({ onConfirm, onBack }) => {\n  const {\n    selectedService,\n    selectedStylist,\n    selectedDate,\n    selectedTime,\n    customerInfo,\n    setCustomerInfo,\n    createAppointment,\n    resetBooking,\n  } = useBooking();\n\n  const [loading, setLoading] = useState(false);\n  const [confirmationDialog, setConfirmationDialog] = useState(false);\n  const [bookingComplete, setBookingComplete] = useState(false);\n  const [newAppointment, setNewAppointment] = useState(null);\n  const [errors, setErrors] = useState({});\n\n  const handleInputChange = (field, value) => {\n    // Apply field-specific restrictions\n    let processedValue = value;\n\n    if (field === 'phone') {\n      // Only allow digits - remove all non-digit characters\n      processedValue = value.replace(/[^\\d]/g, '');\n\n      // Limit to exactly 10 digits\n      if (processedValue.length > 10) {\n        processedValue = processedValue.substring(0, 10);\n      }\n    } else if (field === 'name') {\n      // Limit name to 50 characters\n      if (value.length > 50) {\n        processedValue = value.substring(0, 50);\n      }\n    } else if (field === 'email') {\n      // Remove leading/trailing spaces and limit email to 100 characters\n      processedValue = value.trim();\n      if (processedValue.length > 100) {\n        processedValue = processedValue.substring(0, 100);\n      }\n    } else if (field === 'notes') {\n      // Limit notes to 500 characters\n      if (value.length > 500) {\n        processedValue = value.substring(0, 500);\n      }\n    }\n\n    setCustomerInfo({\n      ...customerInfo,\n      [field]: processedValue,\n    });\n\n    // Clear error for this field when user starts typing\n    if (errors[field]) {\n      setErrors({\n        ...errors,\n        [field]: '',\n      });\n    }\n  };\n\n  const handleConfirmBooking = () => {\n    if (validateForm()) {\n      setConfirmationDialog(true);\n    }\n  };\n\n  const handleFinalConfirm = async () => {\n    setLoading(true);\n    setConfirmationDialog(false);\n\n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      const appointmentData = {\n        customer: customerInfo.name,\n        phone: customerInfo.phone,\n        email: customerInfo.email,\n        service: selectedService.name,\n        serviceId: selectedService.id,\n        stylist: selectedStylist.name,\n        stylistId: selectedStylist.id,\n        date: selectedDate,\n        time: selectedTime,\n        duration: selectedService.duration,\n        price: selectedService.price,\n        notes: customerInfo.notes,\n      };\n\n      const appointment = createAppointment(appointmentData);\n      setNewAppointment(appointment);\n      setBookingComplete(true);\n\n      if (onConfirm) {\n        onConfirm(appointment);\n      }\n    } catch (error) {\n      console.error('Booking failed:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleNewBooking = () => {\n    resetBooking();\n    setBookingComplete(false);\n    setNewAppointment(null);\n  };\n\n  const handleBack = () => {\n    if (onBack) {\n      onBack();\n    }\n  };\n\n  // Validate form fields\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Name validation\n    if (!customerInfo.name.trim()) {\n      newErrors.name = 'Full name is required';\n    } else if (customerInfo.name.trim().length < 2) {\n      newErrors.name = 'Name must be at least 2 characters long';\n    }\n\n    // Phone validation - only pure digits allowed\n    if (!customerInfo.phone.trim()) {\n      newErrors.phone = 'Phone number is required';\n    } else {\n      // Check if phone contains only digits (no formatting allowed)\n      if (!/^\\d{10}$/.test(customerInfo.phone)) {\n        if (!/^\\d+$/.test(customerInfo.phone)) {\n          newErrors.phone = 'Phone number can only contain digits (no spaces, hyphens, or other characters)';\n        } else if (customerInfo.phone.length !== 10) {\n          newErrors.phone = 'Phone number must be exactly 10 digits';\n        }\n      }\n    }\n\n    // Email validation (optional but if provided, must be valid)\n    if (customerInfo.email.trim()) {\n      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\n      const emailValue = customerInfo.email.trim();\n      if (!emailRegex.test(emailValue)) {\n        newErrors.email = 'Please enter a valid email address (e.g., <EMAIL>)';\n      }\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const isFormValid = () => {\n    return customerInfo.name.trim() && customerInfo.phone.trim() && Object.keys(errors).length === 0;\n  };\n\n  if (bookingComplete && newAppointment) {\n    return (\n      <Box sx={{ p: 3, textAlign: 'center' }}>\n        <CheckIcon sx={{ fontSize: 80, color: 'success.main', mb: 2 }} />\n        <Typography variant=\"h4\" gutterBottom color=\"success.main\">\n          Booking Confirmed!\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 3 }}>\n          Your appointment has been successfully booked.\n        </Typography>\n\n        <Paper sx={{ p: 3, mb: 3, textAlign: 'left' }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Appointment Details\n          </Typography>\n          <Grid container spacing={2}>\n            <Grid item xs={12} sm={6}>\n              <Typography variant=\"body2\" color=\"text.secondary\">Customer</Typography>\n              <Typography variant=\"body1\">{newAppointment.customer}</Typography>\n            </Grid>\n            <Grid item xs={12} sm={6}>\n              <Typography variant=\"body2\" color=\"text.secondary\">Phone</Typography>\n              <Typography variant=\"body1\">{newAppointment.phone}</Typography>\n            </Grid>\n            <Grid item xs={12} sm={6}>\n              <Typography variant=\"body2\" color=\"text.secondary\">Service</Typography>\n              <Typography variant=\"body1\">{newAppointment.service}</Typography>\n            </Grid>\n            <Grid item xs={12} sm={6}>\n              <Typography variant=\"body2\" color=\"text.secondary\">Stylist</Typography>\n              <Typography variant=\"body1\">{newAppointment.stylist}</Typography>\n            </Grid>\n            <Grid item xs={12} sm={6}>\n              <Typography variant=\"body2\" color=\"text.secondary\">Date & Time</Typography>\n              <Typography variant=\"body1\">\n                {format(new Date(newAppointment.date), 'EEEE, MMMM d, yyyy')} at {newAppointment.time}\n              </Typography>\n            </Grid>\n            <Grid item xs={12} sm={6}>\n              <Typography variant=\"body2\" color=\"text.secondary\">Total Price</Typography>\n              <Typography variant=\"h6\" color=\"success.main\">₹{newAppointment.price}</Typography>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        <Alert severity=\"info\" sx={{ mb: 3 }}>\n          Please arrive 10 minutes before your appointment time. You will receive a confirmation email shortly.\n        </Alert>\n\n        <Button\n          variant=\"contained\"\n          size=\"large\"\n          onClick={handleNewBooking}\n        >\n          Book Another Appointment\n        </Button>\n      </Box>\n    );\n  }\n\n  if (!selectedService || !selectedStylist || !selectedDate || !selectedTime) {\n    return (\n      <Box sx={{ p: 3 }}>\n        <Alert severity=\"warning\">\n          Please complete all previous steps before confirming your booking.\n        </Alert>\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <Typography variant=\"h4\" gutterBottom>\n        Confirm Your Booking\n      </Typography>\n      <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 3 }}>\n        Please review your appointment details and provide your contact information\n      </Typography>\n\n      <Grid container spacing={3}>\n        {/* Appointment Summary */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 3, height: 'fit-content' }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Appointment Summary\n            </Typography>\n\n            <Box sx={{ mb: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <ServiceIcon sx={{ mr: 2, color: 'primary.main' }} />\n                <Box>\n                  <Typography variant=\"subtitle1\">{selectedService.name}</Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    {selectedService.description}\n                  </Typography>\n                </Box>\n              </Box>\n\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <Avatar sx={{ mr: 2, bgcolor: 'secondary.main' }}>\n                  <PersonIcon />\n                </Avatar>\n                <Box>\n                  <Typography variant=\"subtitle1\">{selectedStylist.name}</Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Rating: {selectedStylist.rating}/5\n                  </Typography>\n                </Box>\n              </Box>\n\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <CalendarIcon sx={{ mr: 2, color: 'primary.main' }} />\n                <Box>\n                  <Typography variant=\"subtitle1\">\n                    {format(new Date(selectedDate), 'EEEE, MMMM d, yyyy')}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    at {selectedTime}\n                  </Typography>\n                </Box>\n              </Box>\n\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <ScheduleIcon sx={{ mr: 2, color: 'primary.main' }} />\n                <Typography variant=\"subtitle1\">\n                  {selectedService.duration} minutes\n                </Typography>\n              </Box>\n\n              <Divider sx={{ my: 2 }} />\n\n              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                <Typography variant=\"h6\">Total Price:</Typography>\n                <Typography variant=\"h5\" color=\"success.main\">\n                  ${selectedService.price}\n                </Typography>\n              </Box>\n            </Box>\n          </Paper>\n        </Grid>\n\n        {/* Customer Information */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Your Information\n            </Typography>\n\n            <Grid container spacing={2}>\n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  label=\"Full Name\"\n                  value={customerInfo.name}\n                  onChange={(e) => handleInputChange('name', e.target.value)}\n                  required\n                  error={!!errors.name}\n                  helperText={errors.name || `${customerInfo.name.length}/50 characters`}\n                  InputProps={{\n                    startAdornment: <PersonIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                  }}\n                />\n              </Grid>\n\n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  label=\"Phone Number\"\n                  value={customerInfo.phone}\n                  onChange={(e) => handleInputChange('phone', e.target.value)}\n                  required\n                  type=\"tel\"\n                  inputMode=\"numeric\"\n                  pattern=\"[0-9]*\"\n                  error={!!errors.phone}\n                  helperText={errors.phone || `Enter exactly 10 digits only (e.g., 9876543210) - ${customerInfo.phone.length}/10`}\n                  InputProps={{\n                    startAdornment: <PhoneIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                  }}\n                />\n              </Grid>\n\n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  label=\"Email Address (Optional)\"\n                  type=\"email\"\n                  value={customerInfo.email}\n                  onChange={(e) => handleInputChange('email', e.target.value)}\n                  error={!!errors.email}\n                  helperText={errors.email || `Optional: We'll send confirmation to this email (${customerInfo.email.length}/100)`}\n                  InputProps={{\n                    startAdornment: <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                  }}\n                />\n              </Grid>\n\n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  label=\"Special Notes (Optional)\"\n                  multiline\n                  rows={3}\n                  value={customerInfo.notes}\n                  onChange={(e) => handleInputChange('notes', e.target.value)}\n                  placeholder=\"Any special requests or notes for your stylist...\"\n                  helperText={`${customerInfo.notes.length}/500 characters`}\n                  InputProps={{\n                    startAdornment: <NotesIcon sx={{ mr: 1, color: 'text.secondary', alignSelf: 'flex-start', mt: 1 }} />,\n                  }}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n        </Grid>\n      </Grid>\n\n      {/* Navigation Buttons */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>\n        <Button\n          variant=\"outlined\"\n          size=\"large\"\n          onClick={handleBack}\n        >\n          Back: Select Time\n        </Button>\n        <Button\n          variant=\"contained\"\n          size=\"large\"\n          onClick={handleConfirmBooking}\n          disabled={!isFormValid()}\n        >\n          Confirm Booking\n        </Button>\n      </Box>\n\n      {/* Validation Summary */}\n      {Object.keys(errors).length > 0 && (\n        <Alert severity=\"error\" sx={{ mt: 2 }}>\n          <Typography variant=\"subtitle2\" gutterBottom>\n            Please fix the following errors:\n          </Typography>\n          <ul style={{ margin: 0, paddingLeft: 20 }}>\n            {Object.entries(errors).map(([field, error]) => (\n              <li key={field}>\n                <Typography variant=\"body2\">{error}</Typography>\n              </li>\n            ))}\n          </ul>\n        </Alert>\n      )}\n\n      {/* Confirmation Dialog */}\n      <Dialog open={confirmationDialog} onClose={() => setConfirmationDialog(false)}>\n        <DialogTitle>Confirm Your Appointment</DialogTitle>\n        <DialogContent>\n          <Typography variant=\"body1\" sx={{ mb: 2 }}>\n            Are you sure you want to book this appointment?\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            <strong>Service:</strong> {selectedService.name}<br />\n            <strong>Stylist:</strong> {selectedStylist.name}<br />\n            <strong>Date & Time:</strong> {format(new Date(selectedDate), 'EEEE, MMMM d, yyyy')} at {selectedTime}<br />\n            <strong>Price:</strong> ${selectedService.price}\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setConfirmationDialog(false)}>Cancel</Button>\n          <Button onClick={handleFinalConfirm} variant=\"contained\" disabled={loading}>\n            {loading ? <CircularProgress size={20} /> : 'Confirm'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default BookingConfirmation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,SAAS,EACTC,OAAO,EACPC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,gBAAgB,QACX,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,aAAa,IAAIC,YAAY,EAC7BC,WAAW,IAAIC,SAAS,EACxBC,UAAU,IAAIC,WAAW,EACzBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,SAAS,QACnB,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,UAAU,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EACrD,MAAM;IACJC,eAAe;IACfC,eAAe;IACfC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,eAAe;IACfC,iBAAiB;IACjBC;EACF,CAAC,GAAGd,UAAU,CAAC,CAAC;EAEhB,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACwD,eAAe,EAAEC,kBAAkB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0D,cAAc,EAAEC,iBAAiB,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC4D,MAAM,EAAEC,SAAS,CAAC,GAAG7D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAExC,MAAM8D,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1C;IACA,IAAIC,cAAc,GAAGD,KAAK;IAE1B,IAAID,KAAK,KAAK,OAAO,EAAE;MACrB;MACAE,cAAc,GAAGD,KAAK,CAACE,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;;MAE5C;MACA,IAAID,cAAc,CAACE,MAAM,GAAG,EAAE,EAAE;QAC9BF,cAAc,GAAGA,cAAc,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;MAClD;IACF,CAAC,MAAM,IAAIL,KAAK,KAAK,MAAM,EAAE;MAC3B;MACA,IAAIC,KAAK,CAACG,MAAM,GAAG,EAAE,EAAE;QACrBF,cAAc,GAAGD,KAAK,CAACI,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;MACzC;IACF,CAAC,MAAM,IAAIL,KAAK,KAAK,OAAO,EAAE;MAC5B;MACAE,cAAc,GAAGD,KAAK,CAACK,IAAI,CAAC,CAAC;MAC7B,IAAIJ,cAAc,CAACE,MAAM,GAAG,GAAG,EAAE;QAC/BF,cAAc,GAAGA,cAAc,CAACG,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;MACnD;IACF,CAAC,MAAM,IAAIL,KAAK,KAAK,OAAO,EAAE;MAC5B;MACA,IAAIC,KAAK,CAACG,MAAM,GAAG,GAAG,EAAE;QACtBF,cAAc,GAAGD,KAAK,CAACI,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;MAC1C;IACF;IAEAnB,eAAe,CAAC;MACd,GAAGD,YAAY;MACf,CAACe,KAAK,GAAGE;IACX,CAAC,CAAC;;IAEF;IACA,IAAIL,MAAM,CAACG,KAAK,CAAC,EAAE;MACjBF,SAAS,CAAC;QACR,GAAGD,MAAM;QACT,CAACG,KAAK,GAAG;MACX,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMO,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIC,YAAY,CAAC,CAAC,EAAE;MAClBhB,qBAAqB,CAAC,IAAI,CAAC;IAC7B;EACF,CAAC;EAED,MAAMiB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrCnB,UAAU,CAAC,IAAI,CAAC;IAChBE,qBAAqB,CAAC,KAAK,CAAC;IAE5B,IAAI;MACF;MACA,MAAM,IAAIkB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD,MAAME,eAAe,GAAG;QACtBC,QAAQ,EAAE7B,YAAY,CAAC8B,IAAI;QAC3BC,KAAK,EAAE/B,YAAY,CAAC+B,KAAK;QACzBC,KAAK,EAAEhC,YAAY,CAACgC,KAAK;QACzBC,OAAO,EAAErC,eAAe,CAACkC,IAAI;QAC7BI,SAAS,EAAEtC,eAAe,CAACuC,EAAE;QAC7BC,OAAO,EAAEvC,eAAe,CAACiC,IAAI;QAC7BO,SAAS,EAAExC,eAAe,CAACsC,EAAE;QAC7BG,IAAI,EAAExC,YAAY;QAClByC,IAAI,EAAExC,YAAY;QAClByC,QAAQ,EAAE5C,eAAe,CAAC4C,QAAQ;QAClCC,KAAK,EAAE7C,eAAe,CAAC6C,KAAK;QAC5BC,KAAK,EAAE1C,YAAY,CAAC0C;MACtB,CAAC;MAED,MAAMC,WAAW,GAAGzC,iBAAiB,CAAC0B,eAAe,CAAC;MACtDjB,iBAAiB,CAACgC,WAAW,CAAC;MAC9BlC,kBAAkB,CAAC,IAAI,CAAC;MAExB,IAAIhB,SAAS,EAAE;QACbA,SAAS,CAACkD,WAAW,CAAC;MACxB;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;IACzC,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B3C,YAAY,CAAC,CAAC;IACdM,kBAAkB,CAAC,KAAK,CAAC;IACzBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMoC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIrD,MAAM,EAAE;MACVA,MAAM,CAAC,CAAC;IACV;EACF,CAAC;;EAED;EACA,MAAM6B,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMyB,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,IAAI,CAAChD,YAAY,CAAC8B,IAAI,CAACT,IAAI,CAAC,CAAC,EAAE;MAC7B2B,SAAS,CAAClB,IAAI,GAAG,uBAAuB;IAC1C,CAAC,MAAM,IAAI9B,YAAY,CAAC8B,IAAI,CAACT,IAAI,CAAC,CAAC,CAACF,MAAM,GAAG,CAAC,EAAE;MAC9C6B,SAAS,CAAClB,IAAI,GAAG,yCAAyC;IAC5D;;IAEA;IACA,IAAI,CAAC9B,YAAY,CAAC+B,KAAK,CAACV,IAAI,CAAC,CAAC,EAAE;MAC9B2B,SAAS,CAACjB,KAAK,GAAG,0BAA0B;IAC9C,CAAC,MAAM;MACL;MACA,IAAI,CAAC,UAAU,CAACkB,IAAI,CAACjD,YAAY,CAAC+B,KAAK,CAAC,EAAE;QACxC,IAAI,CAAC,OAAO,CAACkB,IAAI,CAACjD,YAAY,CAAC+B,KAAK,CAAC,EAAE;UACrCiB,SAAS,CAACjB,KAAK,GAAG,gFAAgF;QACpG,CAAC,MAAM,IAAI/B,YAAY,CAAC+B,KAAK,CAACZ,MAAM,KAAK,EAAE,EAAE;UAC3C6B,SAAS,CAACjB,KAAK,GAAG,wCAAwC;QAC5D;MACF;IACF;;IAEA;IACA,IAAI/B,YAAY,CAACgC,KAAK,CAACX,IAAI,CAAC,CAAC,EAAE;MAC7B,MAAM6B,UAAU,GAAG,kDAAkD;MACrE,MAAMC,UAAU,GAAGnD,YAAY,CAACgC,KAAK,CAACX,IAAI,CAAC,CAAC;MAC5C,IAAI,CAAC6B,UAAU,CAACD,IAAI,CAACE,UAAU,CAAC,EAAE;QAChCH,SAAS,CAAChB,KAAK,GAAG,6DAA6D;MACjF;IACF;IAEAnB,SAAS,CAACmC,SAAS,CAAC;IACpB,OAAOI,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC,CAAC7B,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMmC,WAAW,GAAGA,CAAA,KAAM;IACxB,OAAOtD,YAAY,CAAC8B,IAAI,CAACT,IAAI,CAAC,CAAC,IAAIrB,YAAY,CAAC+B,KAAK,CAACV,IAAI,CAAC,CAAC,IAAI+B,MAAM,CAACC,IAAI,CAACzC,MAAM,CAAC,CAACO,MAAM,KAAK,CAAC;EAClG,CAAC;EAED,IAAIX,eAAe,IAAIE,cAAc,EAAE;IACrC,oBACEnB,OAAA,CAACtC,GAAG;MAACsG,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACrCnE,OAAA,CAACJ,SAAS;QAACoE,EAAE,EAAE;UAAEI,QAAQ,EAAE,EAAE;UAAEC,KAAK,EAAE,cAAc;UAAEC,EAAE,EAAE;QAAE;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjE1E,OAAA,CAACrC,UAAU;QAACgH,OAAO,EAAC,IAAI;QAACC,YAAY;QAACP,KAAK,EAAC,cAAc;QAAAF,QAAA,EAAC;MAE3D;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb1E,OAAA,CAACrC,UAAU;QAACgH,OAAO,EAAC,OAAO;QAACN,KAAK,EAAC,gBAAgB;QAACL,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,EAAC;MAElE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb1E,OAAA,CAAC7B,KAAK;QAAC6F,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEK,EAAE,EAAE,CAAC;UAAEJ,SAAS,EAAE;QAAO,CAAE;QAAAC,QAAA,gBAC5CnE,OAAA,CAACrC,UAAU;UAACgH,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAT,QAAA,EAAC;QAEtC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb1E,OAAA,CAACpC,IAAI;UAACiH,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAX,QAAA,gBACzBnE,OAAA,CAACpC,IAAI;YAACmH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,gBACvBnE,OAAA,CAACrC,UAAU;cAACgH,OAAO,EAAC,OAAO;cAACN,KAAK,EAAC,gBAAgB;cAAAF,QAAA,EAAC;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACxE1E,OAAA,CAACrC,UAAU;cAACgH,OAAO,EAAC,OAAO;cAAAR,QAAA,EAAEhD,cAAc,CAACmB;YAAQ;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACP1E,OAAA,CAACpC,IAAI;YAACmH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,gBACvBnE,OAAA,CAACrC,UAAU;cAACgH,OAAO,EAAC,OAAO;cAACN,KAAK,EAAC,gBAAgB;cAAAF,QAAA,EAAC;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACrE1E,OAAA,CAACrC,UAAU;cAACgH,OAAO,EAAC,OAAO;cAAAR,QAAA,EAAEhD,cAAc,CAACqB;YAAK;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACP1E,OAAA,CAACpC,IAAI;YAACmH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,gBACvBnE,OAAA,CAACrC,UAAU;cAACgH,OAAO,EAAC,OAAO;cAACN,KAAK,EAAC,gBAAgB;cAAAF,QAAA,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvE1E,OAAA,CAACrC,UAAU;cAACgH,OAAO,EAAC,OAAO;cAAAR,QAAA,EAAEhD,cAAc,CAACuB;YAAO;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACP1E,OAAA,CAACpC,IAAI;YAACmH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,gBACvBnE,OAAA,CAACrC,UAAU;cAACgH,OAAO,EAAC,OAAO;cAACN,KAAK,EAAC,gBAAgB;cAAAF,QAAA,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvE1E,OAAA,CAACrC,UAAU;cAACgH,OAAO,EAAC,OAAO;cAAAR,QAAA,EAAEhD,cAAc,CAAC0B;YAAO;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACP1E,OAAA,CAACpC,IAAI;YAACmH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,gBACvBnE,OAAA,CAACrC,UAAU;cAACgH,OAAO,EAAC,OAAO;cAACN,KAAK,EAAC,gBAAgB;cAAAF,QAAA,EAAC;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3E1E,OAAA,CAACrC,UAAU;cAACgH,OAAO,EAAC,OAAO;cAAAR,QAAA,GACxBtE,MAAM,CAAC,IAAIqF,IAAI,CAAC/D,cAAc,CAAC4B,IAAI,CAAC,EAAE,oBAAoB,CAAC,EAAC,MAAI,EAAC5B,cAAc,CAAC6B,IAAI;YAAA;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACP1E,OAAA,CAACpC,IAAI;YAACmH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,gBACvBnE,OAAA,CAACrC,UAAU;cAACgH,OAAO,EAAC,OAAO;cAACN,KAAK,EAAC,gBAAgB;cAAAF,QAAA,EAAC;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3E1E,OAAA,CAACrC,UAAU;cAACgH,OAAO,EAAC,IAAI;cAACN,KAAK,EAAC,cAAc;cAAAF,QAAA,GAAC,QAAC,EAAChD,cAAc,CAAC+B,KAAK;YAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAER1E,OAAA,CAAC9B,KAAK;QAACiH,QAAQ,EAAC,MAAM;QAACnB,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,EAAC;MAEtC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAER1E,OAAA,CAACjC,MAAM;QACL4G,OAAO,EAAC,WAAW;QACnBS,IAAI,EAAC,OAAO;QACZC,OAAO,EAAE9B,gBAAiB;QAAAY,QAAA,EAC3B;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,IAAI,CAACrE,eAAe,IAAI,CAACC,eAAe,IAAI,CAACC,YAAY,IAAI,CAACC,YAAY,EAAE;IAC1E,oBACER,OAAA,CAACtC,GAAG;MAACsG,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAE,QAAA,eAChBnE,OAAA,CAAC9B,KAAK;QAACiH,QAAQ,EAAC,SAAS;QAAAhB,QAAA,EAAC;MAE1B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EAEA,oBACE1E,OAAA,CAACtC,GAAG;IAACsG,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAE,QAAA,gBAChBnE,OAAA,CAACrC,UAAU;MAACgH,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAT,QAAA,EAAC;IAEtC;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACb1E,OAAA,CAACrC,UAAU;MAACgH,OAAO,EAAC,OAAO;MAACN,KAAK,EAAC,gBAAgB;MAACL,EAAE,EAAE;QAAEM,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,EAAC;IAElE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb1E,OAAA,CAACpC,IAAI;MAACiH,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAX,QAAA,gBAEzBnE,OAAA,CAACpC,IAAI;QAACmH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACM,EAAE,EAAE,CAAE;QAAAnB,QAAA,eACvBnE,OAAA,CAAC7B,KAAK;UAAC6F,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEsB,MAAM,EAAE;UAAc,CAAE;UAAApB,QAAA,gBACzCnE,OAAA,CAACrC,UAAU;YAACgH,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAT,QAAA,EAAC;UAEtC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEb1E,OAAA,CAACtC,GAAG;YAACsG,EAAE,EAAE;cAAEM,EAAE,EAAE;YAAE,CAAE;YAAAH,QAAA,gBACjBnE,OAAA,CAACtC,GAAG;cAACsG,EAAE,EAAE;gBAAEwB,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEnB,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,gBACxDnE,OAAA,CAACZ,WAAW;gBAAC4E,EAAE,EAAE;kBAAE0B,EAAE,EAAE,CAAC;kBAAErB,KAAK,EAAE;gBAAe;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrD1E,OAAA,CAACtC,GAAG;gBAAAyG,QAAA,gBACFnE,OAAA,CAACrC,UAAU;kBAACgH,OAAO,EAAC,WAAW;kBAAAR,QAAA,EAAE9D,eAAe,CAACkC;gBAAI;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACnE1E,OAAA,CAACrC,UAAU;kBAACgH,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,EAC/C9D,eAAe,CAACsF;gBAAW;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1E,OAAA,CAACtC,GAAG;cAACsG,EAAE,EAAE;gBAAEwB,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEnB,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,gBACxDnE,OAAA,CAAC5B,MAAM;gBAAC4F,EAAE,EAAE;kBAAE0B,EAAE,EAAE,CAAC;kBAAEE,OAAO,EAAE;gBAAiB,CAAE;gBAAAzB,QAAA,eAC/CnE,OAAA,CAACpB,UAAU;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACT1E,OAAA,CAACtC,GAAG;gBAAAyG,QAAA,gBACFnE,OAAA,CAACrC,UAAU;kBAACgH,OAAO,EAAC,WAAW;kBAAAR,QAAA,EAAE7D,eAAe,CAACiC;gBAAI;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACnE1E,OAAA,CAACrC,UAAU;kBAACgH,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,UACzC,EAAC7D,eAAe,CAACuF,MAAM,EAAC,IAClC;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1E,OAAA,CAACtC,GAAG;cAACsG,EAAE,EAAE;gBAAEwB,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEnB,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,gBACxDnE,OAAA,CAAChB,YAAY;gBAACgF,EAAE,EAAE;kBAAE0B,EAAE,EAAE,CAAC;kBAAErB,KAAK,EAAE;gBAAe;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtD1E,OAAA,CAACtC,GAAG;gBAAAyG,QAAA,gBACFnE,OAAA,CAACrC,UAAU;kBAACgH,OAAO,EAAC,WAAW;kBAAAR,QAAA,EAC5BtE,MAAM,CAAC,IAAIqF,IAAI,CAAC3E,YAAY,CAAC,EAAE,oBAAoB;gBAAC;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACb1E,OAAA,CAACrC,UAAU;kBAACgH,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,KAC9C,EAAC3D,YAAY;gBAAA;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1E,OAAA,CAACtC,GAAG;cAACsG,EAAE,EAAE;gBAAEwB,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEnB,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,gBACxDnE,OAAA,CAAClB,YAAY;gBAACkF,EAAE,EAAE;kBAAE0B,EAAE,EAAE,CAAC;kBAAErB,KAAK,EAAE;gBAAe;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtD1E,OAAA,CAACrC,UAAU;gBAACgH,OAAO,EAAC,WAAW;gBAAAR,QAAA,GAC5B9D,eAAe,CAAC4C,QAAQ,EAAC,UAC5B;cAAA;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEN1E,OAAA,CAAC/B,OAAO;cAAC+F,EAAE,EAAE;gBAAE8B,EAAE,EAAE;cAAE;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAE1B1E,OAAA,CAACtC,GAAG;cAACsG,EAAE,EAAE;gBAAEwB,OAAO,EAAE,MAAM;gBAAEO,cAAc,EAAE,eAAe;gBAAEN,UAAU,EAAE;cAAS,CAAE;cAAAtB,QAAA,gBAClFnE,OAAA,CAACrC,UAAU;gBAACgH,OAAO,EAAC,IAAI;gBAAAR,QAAA,EAAC;cAAY;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClD1E,OAAA,CAACrC,UAAU;gBAACgH,OAAO,EAAC,IAAI;gBAACN,KAAK,EAAC,cAAc;gBAAAF,QAAA,GAAC,GAC3C,EAAC9D,eAAe,CAAC6C,KAAK;cAAA;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP1E,OAAA,CAACpC,IAAI;QAACmH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACM,EAAE,EAAE,CAAE;QAAAnB,QAAA,eACvBnE,OAAA,CAAC7B,KAAK;UAAC6F,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,gBAClBnE,OAAA,CAACrC,UAAU;YAACgH,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAT,QAAA,EAAC;UAEtC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEb1E,OAAA,CAACpC,IAAI;YAACiH,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAX,QAAA,gBACzBnE,OAAA,CAACpC,IAAI;cAACmH,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAb,QAAA,eAChBnE,OAAA,CAAChC,SAAS;gBACRgI,SAAS;gBACTC,KAAK,EAAC,WAAW;gBACjBxE,KAAK,EAAEhB,YAAY,CAAC8B,IAAK;gBACzB2D,QAAQ,EAAGC,CAAC,IAAK5E,iBAAiB,CAAC,MAAM,EAAE4E,CAAC,CAACC,MAAM,CAAC3E,KAAK,CAAE;gBAC3D4E,QAAQ;gBACRhD,KAAK,EAAE,CAAC,CAAChC,MAAM,CAACkB,IAAK;gBACrB+D,UAAU,EAAEjF,MAAM,CAACkB,IAAI,IAAI,GAAG9B,YAAY,CAAC8B,IAAI,CAACX,MAAM,gBAAiB;gBACvE2E,UAAU,EAAE;kBACVC,cAAc,eAAExG,OAAA,CAACpB,UAAU;oBAACoF,EAAE,EAAE;sBAAE0B,EAAE,EAAE,CAAC;sBAAErB,KAAK,EAAE;oBAAiB;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACvE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEP1E,OAAA,CAACpC,IAAI;cAACmH,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAb,QAAA,eAChBnE,OAAA,CAAChC,SAAS;gBACRgI,SAAS;gBACTC,KAAK,EAAC,cAAc;gBACpBxE,KAAK,EAAEhB,YAAY,CAAC+B,KAAM;gBAC1B0D,QAAQ,EAAGC,CAAC,IAAK5E,iBAAiB,CAAC,OAAO,EAAE4E,CAAC,CAACC,MAAM,CAAC3E,KAAK,CAAE;gBAC5D4E,QAAQ;gBACRI,IAAI,EAAC,KAAK;gBACVC,SAAS,EAAC,SAAS;gBACnBC,OAAO,EAAC,QAAQ;gBAChBtD,KAAK,EAAE,CAAC,CAAChC,MAAM,CAACmB,KAAM;gBACtB8D,UAAU,EAAEjF,MAAM,CAACmB,KAAK,IAAI,qDAAqD/B,YAAY,CAAC+B,KAAK,CAACZ,MAAM,KAAM;gBAChH2E,UAAU,EAAE;kBACVC,cAAc,eAAExG,OAAA,CAACV,SAAS;oBAAC0E,EAAE,EAAE;sBAAE0B,EAAE,EAAE,CAAC;sBAAErB,KAAK,EAAE;oBAAiB;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACtE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEP1E,OAAA,CAACpC,IAAI;cAACmH,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAb,QAAA,eAChBnE,OAAA,CAAChC,SAAS;gBACRgI,SAAS;gBACTC,KAAK,EAAC,0BAA0B;gBAChCQ,IAAI,EAAC,OAAO;gBACZhF,KAAK,EAAEhB,YAAY,CAACgC,KAAM;gBAC1ByD,QAAQ,EAAGC,CAAC,IAAK5E,iBAAiB,CAAC,OAAO,EAAE4E,CAAC,CAACC,MAAM,CAAC3E,KAAK,CAAE;gBAC5D4B,KAAK,EAAE,CAAC,CAAChC,MAAM,CAACoB,KAAM;gBACtB6D,UAAU,EAAEjF,MAAM,CAACoB,KAAK,IAAI,oDAAoDhC,YAAY,CAACgC,KAAK,CAACb,MAAM,OAAQ;gBACjH2E,UAAU,EAAE;kBACVC,cAAc,eAAExG,OAAA,CAACR,SAAS;oBAACwE,EAAE,EAAE;sBAAE0B,EAAE,EAAE,CAAC;sBAAErB,KAAK,EAAE;oBAAiB;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACtE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEP1E,OAAA,CAACpC,IAAI;cAACmH,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAb,QAAA,eAChBnE,OAAA,CAAChC,SAAS;gBACRgI,SAAS;gBACTC,KAAK,EAAC,0BAA0B;gBAChCW,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACRpF,KAAK,EAAEhB,YAAY,CAAC0C,KAAM;gBAC1B+C,QAAQ,EAAGC,CAAC,IAAK5E,iBAAiB,CAAC,OAAO,EAAE4E,CAAC,CAACC,MAAM,CAAC3E,KAAK,CAAE;gBAC5DqF,WAAW,EAAC,mDAAmD;gBAC/DR,UAAU,EAAE,GAAG7F,YAAY,CAAC0C,KAAK,CAACvB,MAAM,iBAAkB;gBAC1D2E,UAAU,EAAE;kBACVC,cAAc,eAAExG,OAAA,CAACN,SAAS;oBAACsE,EAAE,EAAE;sBAAE0B,EAAE,EAAE,CAAC;sBAAErB,KAAK,EAAE,gBAAgB;sBAAE0C,SAAS,EAAE,YAAY;sBAAEC,EAAE,EAAE;oBAAE;kBAAE;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACtG;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP1E,OAAA,CAACtC,GAAG;MAACsG,EAAE,EAAE;QAAEwB,OAAO,EAAE,MAAM;QAAEO,cAAc,EAAE,eAAe;QAAEiB,EAAE,EAAE;MAAE,CAAE;MAAA7C,QAAA,gBACnEnE,OAAA,CAACjC,MAAM;QACL4G,OAAO,EAAC,UAAU;QAClBS,IAAI,EAAC,OAAO;QACZC,OAAO,EAAE7B,UAAW;QAAAW,QAAA,EACrB;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1E,OAAA,CAACjC,MAAM;QACL4G,OAAO,EAAC,WAAW;QACnBS,IAAI,EAAC,OAAO;QACZC,OAAO,EAAEtD,oBAAqB;QAC9BkF,QAAQ,EAAE,CAAClD,WAAW,CAAC,CAAE;QAAAI,QAAA,EAC1B;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLb,MAAM,CAACC,IAAI,CAACzC,MAAM,CAAC,CAACO,MAAM,GAAG,CAAC,iBAC7B5B,OAAA,CAAC9B,KAAK;MAACiH,QAAQ,EAAC,OAAO;MAACnB,EAAE,EAAE;QAAEgD,EAAE,EAAE;MAAE,CAAE;MAAA7C,QAAA,gBACpCnE,OAAA,CAACrC,UAAU;QAACgH,OAAO,EAAC,WAAW;QAACC,YAAY;QAAAT,QAAA,EAAC;MAE7C;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb1E,OAAA;QAAIkH,KAAK,EAAE;UAAEC,MAAM,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAG,CAAE;QAAAjD,QAAA,EACvCN,MAAM,CAACwD,OAAO,CAAChG,MAAM,CAAC,CAACiG,GAAG,CAAC,CAAC,CAAC9F,KAAK,EAAE6B,KAAK,CAAC,kBACzCrD,OAAA;UAAAmE,QAAA,eACEnE,OAAA,CAACrC,UAAU;YAACgH,OAAO,EAAC,OAAO;YAAAR,QAAA,EAAEd;UAAK;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa;QAAC,GADzClD,KAAK;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACR,eAGD1E,OAAA,CAAC1B,MAAM;MAACiJ,IAAI,EAAExG,kBAAmB;MAACyG,OAAO,EAAEA,CAAA,KAAMxG,qBAAqB,CAAC,KAAK,CAAE;MAAAmD,QAAA,gBAC5EnE,OAAA,CAACzB,WAAW;QAAA4F,QAAA,EAAC;MAAwB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACnD1E,OAAA,CAACxB,aAAa;QAAA2F,QAAA,gBACZnE,OAAA,CAACrC,UAAU;UAACgH,OAAO,EAAC,OAAO;UAACX,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,EAAC;QAE3C;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb1E,OAAA,CAACrC,UAAU;UAACgH,OAAO,EAAC,OAAO;UAACN,KAAK,EAAC,gBAAgB;UAAAF,QAAA,gBAChDnE,OAAA;YAAAmE,QAAA,EAAQ;UAAQ;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACrE,eAAe,CAACkC,IAAI,eAACvC,OAAA;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtD1E,OAAA;YAAAmE,QAAA,EAAQ;UAAQ;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACpE,eAAe,CAACiC,IAAI,eAACvC,OAAA;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtD1E,OAAA;YAAAmE,QAAA,EAAQ;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC7E,MAAM,CAAC,IAAIqF,IAAI,CAAC3E,YAAY,CAAC,EAAE,oBAAoB,CAAC,EAAC,MAAI,EAACC,YAAY,eAACR,OAAA;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5G1E,OAAA;YAAAmE,QAAA,EAAQ;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,MAAE,EAACrE,eAAe,CAAC6C,KAAK;QAAA;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChB1E,OAAA,CAACvB,aAAa;QAAA0F,QAAA,gBACZnE,OAAA,CAACjC,MAAM;UAACsH,OAAO,EAAEA,CAAA,KAAMrE,qBAAqB,CAAC,KAAK,CAAE;UAAAmD,QAAA,EAAC;QAAM;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpE1E,OAAA,CAACjC,MAAM;UAACsH,OAAO,EAAEpD,kBAAmB;UAAC0C,OAAO,EAAC,WAAW;UAACsC,QAAQ,EAAEpG,OAAQ;UAAAsD,QAAA,EACxEtD,OAAO,gBAAGb,OAAA,CAACtB,gBAAgB;YAAC0G,IAAI,EAAE;UAAG;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAS;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACtE,EAAA,CAlbIH,mBAAmB;EAAA,QAUnBH,UAAU;AAAA;AAAA2H,EAAA,GAVVxH,mBAAmB;AAobzB,eAAeA,mBAAmB;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}