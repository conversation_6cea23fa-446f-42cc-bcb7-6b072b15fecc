[{"D:\\Project\\salon-management-system\\src\\index.js": "1", "D:\\Project\\salon-management-system\\src\\reportWebVitals.js": "2", "D:\\Project\\salon-management-system\\src\\App.js": "3", "D:\\Project\\salon-management-system\\src\\components\\Dashboard.js": "4", "D:\\Project\\salon-management-system\\src\\components\\Navbar.js": "5", "D:\\Project\\salon-management-system\\src\\components\\Appointments.js": "6", "D:\\Project\\salon-management-system\\src\\components\\Staff.js": "7", "D:\\Project\\salon-management-system\\src\\components\\Reports.js": "8", "D:\\Project\\salon-management-system\\src\\components\\Customers.js": "9", "D:\\Project\\salon-management-system\\src\\components\\Services.js": "10", "D:\\Project\\salon-management-system\\src\\components\\ProtectedRoute.js": "11", "D:\\Project\\salon-management-system\\src\\components\\UserProfile.js": "12", "D:\\Project\\salon-management-system\\src\\components\\Login.js": "13", "D:\\Project\\salon-management-system\\src\\contexts\\AuthContext.js": "14", "D:\\Project\\salon-management-system\\src\\components\\CalendarView.js": "15", "D:\\Project\\salon-management-system\\src\\components\\BookingFlow.js": "16", "D:\\Project\\salon-management-system\\src\\components\\CancelBookingDialog.js": "17", "D:\\Project\\salon-management-system\\src\\contexts\\BookingContext.js": "18", "D:\\Project\\salon-management-system\\src\\components\\ServiceSelection.js": "19", "D:\\Project\\salon-management-system\\src\\components\\StylistSelection.js": "20", "D:\\Project\\salon-management-system\\src\\components\\TimeSlotSelection.js": "21", "D:\\Project\\salon-management-system\\src\\components\\BookingConfirmation.js": "22", "D:\\Project\\salon-management-system\\src\\components\\Inventory.js": "23", "D:\\Project\\salon-management-system\\src\\contexts\\InventoryContext.js": "24", "D:\\Project\\salon-management-system\\src\\components\\InventoryAlerts.js": "25", "D:\\Project\\salon-management-system\\src\\components\\ProductForm.js": "26", "D:\\Project\\salon-management-system\\src\\components\\Billing.js": "27", "D:\\Project\\salon-management-system\\src\\components\\BillingReports.js": "28", "D:\\Project\\salon-management-system\\src\\contexts\\BillingContext.js": "29", "D:\\Project\\salon-management-system\\src\\components\\DiscountForm.js": "30", "D:\\Project\\salon-management-system\\src\\components\\PaymentProcessor.js": "31", "D:\\Project\\salon-management-system\\src\\components\\InvoiceForm.js": "32", "D:\\Project\\salon-management-system\\src\\utils\\pdfGenerator.js": "33", "D:\\Project\\salon-management-system\\src\\components\\ui\\index.js": "34", "D:\\Project\\salon-management-system\\src\\components\\ui\\Card.js": "35", "D:\\Project\\salon-management-system\\src\\components\\ui\\Modal.js": "36", "D:\\Project\\salon-management-system\\src\\components\\ui\\Button.js": "37", "D:\\Project\\salon-management-system\\src\\components\\ui\\LoadingSpinner.js": "38", "D:\\Project\\salon-management-system\\src\\components\\ui\\Input.js": "39"}, {"size": 535, "mtime": 1752673505339, "results": "40", "hashOfConfig": "41"}, {"size": 362, "mtime": 1752673505478, "results": "42", "hashOfConfig": "41"}, {"size": 4509, "mtime": 1752778535190, "results": "43", "hashOfConfig": "41"}, {"size": 14380, "mtime": 1752836908499, "results": "44", "hashOfConfig": "41"}, {"size": 11413, "mtime": 1752767316487, "results": "45", "hashOfConfig": "41"}, {"size": 14688, "mtime": 1752836908616, "results": "46", "hashOfConfig": "41"}, {"size": 15856, "mtime": 1752836908617, "results": "47", "hashOfConfig": "41"}, {"size": 15811, "mtime": 1752836908613, "results": "48", "hashOfConfig": "41"}, {"size": 50976, "mtime": 1752836908545, "results": "49", "hashOfConfig": "41"}, {"size": 50254, "mtime": 1752836908549, "results": "50", "hashOfConfig": "41"}, {"size": 4385, "mtime": 1752764952348, "results": "51", "hashOfConfig": "41"}, {"size": 28116, "mtime": 1752776295213, "results": "52", "hashOfConfig": "41"}, {"size": 8461, "mtime": 1752778535121, "results": "53", "hashOfConfig": "41"}, {"size": 3206, "mtime": 1752778535189, "results": "54", "hashOfConfig": "41"}, {"size": 10537, "mtime": 1752836908619, "results": "55", "hashOfConfig": "41"}, {"size": 2694, "mtime": 1752677885241, "results": "56", "hashOfConfig": "41"}, {"size": 6927, "mtime": 1752677912052, "results": "57", "hashOfConfig": "41"}, {"size": 9242, "mtime": 1752836908557, "results": "58", "hashOfConfig": "41"}, {"size": 9467, "mtime": 1752836908617, "results": "59", "hashOfConfig": "41"}, {"size": 11828, "mtime": 1752769177096, "results": "60", "hashOfConfig": "41"}, {"size": 9468, "mtime": 1752680301517, "results": "61", "hashOfConfig": "41"}, {"size": 15996, "mtime": 1752836908619, "results": "62", "hashOfConfig": "41"}, {"size": 33634, "mtime": 1752836908553, "results": "63", "hashOfConfig": "41"}, {"size": 9331, "mtime": 1752836908613, "results": "64", "hashOfConfig": "41"}, {"size": 11375, "mtime": 1752714307959, "results": "65", "hashOfConfig": "41"}, {"size": 17975, "mtime": 1752836908554, "results": "66", "hashOfConfig": "41"}, {"size": 41519, "mtime": 1752840781163, "results": "67", "hashOfConfig": "41"}, {"size": 22119, "mtime": 1752836908556, "results": "68", "hashOfConfig": "41"}, {"size": 16399, "mtime": 1752836908613, "results": "69", "hashOfConfig": "41"}, {"size": 16999, "mtime": 1752836908554, "results": "70", "hashOfConfig": "41"}, {"size": 21253, "mtime": 1752836908556, "results": "71", "hashOfConfig": "41"}, {"size": 20104, "mtime": 1752836908554, "results": "72", "hashOfConfig": "41"}, {"size": 13528, "mtime": 1752836908551, "results": "73", "hashOfConfig": "41"}, {"size": 283, "mtime": 1752767093634, "results": "74", "hashOfConfig": "41"}, {"size": 2072, "mtime": 1752767028749, "results": "75", "hashOfConfig": "41"}, {"size": 3708, "mtime": 1752767062661, "results": "76", "hashOfConfig": "41"}, {"size": 2543, "mtime": 1752767014120, "results": "77", "hashOfConfig": "41"}, {"size": 3812, "mtime": 1752767084198, "results": "78", "hashOfConfig": "41"}, {"size": 2581, "mtime": 1752767045087, "results": "79", "hashOfConfig": "41"}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "wsws0p", {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Project\\salon-management-system\\src\\index.js", [], [], "D:\\Project\\salon-management-system\\src\\reportWebVitals.js", [], [], "D:\\Project\\salon-management-system\\src\\App.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Dashboard.js", ["197", "198", "199", "200", "201"], [], "D:\\Project\\salon-management-system\\src\\components\\Navbar.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Appointments.js", ["202"], [], "D:\\Project\\salon-management-system\\src\\components\\Staff.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Reports.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Customers.js", ["203", "204", "205", "206", "207", "208", "209", "210"], [], "D:\\Project\\salon-management-system\\src\\components\\Services.js", ["211", "212", "213", "214", "215", "216", "217", "218"], [], "D:\\Project\\salon-management-system\\src\\components\\ProtectedRoute.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\UserProfile.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Login.js", [], [], "D:\\Project\\salon-management-system\\src\\contexts\\AuthContext.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\CalendarView.js", ["219"], [], "D:\\Project\\salon-management-system\\src\\components\\BookingFlow.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\CancelBookingDialog.js", [], [], "D:\\Project\\salon-management-system\\src\\contexts\\BookingContext.js", ["220", "221", "222", "223", "224"], [], "D:\\Project\\salon-management-system\\src\\components\\ServiceSelection.js", ["225"], [], "D:\\Project\\salon-management-system\\src\\components\\StylistSelection.js", ["226", "227"], [], "D:\\Project\\salon-management-system\\src\\components\\TimeSlotSelection.js", ["228", "229"], [], "D:\\Project\\salon-management-system\\src\\components\\BookingConfirmation.js", ["230", "231", "232", "233"], [], "D:\\Project\\salon-management-system\\src\\components\\Inventory.js", [], [], "D:\\Project\\salon-management-system\\src\\contexts\\InventoryContext.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\InventoryAlerts.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\ProductForm.js", ["234", "235", "236", "237"], [], "D:\\Project\\salon-management-system\\src\\components\\Billing.js", ["238", "239", "240", "241", "242", "243"], [], "D:\\Project\\salon-management-system\\src\\components\\BillingReports.js", ["244", "245", "246", "247", "248", "249"], [], "D:\\Project\\salon-management-system\\src\\contexts\\BillingContext.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\DiscountForm.js", ["250", "251"], [], "D:\\Project\\salon-management-system\\src\\components\\PaymentProcessor.js", ["252", "253", "254", "255", "256", "257", "258"], [], "D:\\Project\\salon-management-system\\src\\components\\InvoiceForm.js", ["259", "260", "261", "262", "263", "264"], [], "D:\\Project\\salon-management-system\\src\\utils\\pdfGenerator.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\ui\\index.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\ui\\Card.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\ui\\Modal.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\ui\\Button.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\ui\\LoadingSpinner.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\ui\\Input.js", [], [], {"ruleId": "265", "severity": 1, "message": "266", "line": 4, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 4, "endColumn": 15}, {"ruleId": "265", "severity": 1, "message": "269", "line": 11, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 11, "endColumn": 17}, {"ruleId": "265", "severity": 1, "message": "270", "line": 16, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 16, "endColumn": 19}, {"ruleId": "265", "severity": 1, "message": "271", "line": 28, "column": 5, "nodeType": "267", "messageId": "268", "endLine": 28, "endColumn": 13}, {"ruleId": "265", "severity": 1, "message": "272", "line": 151, "column": 9, "nodeType": "267", "messageId": "268", "endLine": 151, "endColumn": 23}, {"ruleId": "265", "severity": 1, "message": "273", "line": 26, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 26, "endColumn": 8}, {"ruleId": "265", "severity": 1, "message": "274", "line": 2, "column": 18, "nodeType": "267", "messageId": "268", "endLine": 2, "endColumn": 33}, {"ruleId": "265", "severity": 1, "message": "275", "line": 10, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 10, "endColumn": 12}, {"ruleId": "265", "severity": 1, "message": "276", "line": 16, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 16, "endColumn": 11}, {"ruleId": "265", "severity": 1, "message": "277", "line": 21, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 21, "endColumn": 12}, {"ruleId": "265", "severity": 1, "message": "278", "line": 22, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 22, "endColumn": 12}, {"ruleId": "265", "severity": 1, "message": "279", "line": 24, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 24, "endColumn": 11}, {"ruleId": "265", "severity": 1, "message": "280", "line": 25, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 25, "endColumn": 13}, {"ruleId": "265", "severity": 1, "message": "281", "line": 303, "column": 9, "nodeType": "267", "messageId": "268", "endLine": 303, "endColumn": 27}, {"ruleId": "265", "severity": 1, "message": "274", "line": 2, "column": 18, "nodeType": "267", "messageId": "268", "endLine": 2, "endColumn": 33}, {"ruleId": "265", "severity": 1, "message": "277", "line": 20, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 20, "endColumn": 12}, {"ruleId": "265", "severity": 1, "message": "278", "line": 21, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 21, "endColumn": 12}, {"ruleId": "265", "severity": 1, "message": "282", "line": 165, "column": 10, "nodeType": "267", "messageId": "268", "endLine": 165, "endColumn": 23}, {"ruleId": "265", "severity": 1, "message": "283", "line": 165, "column": 25, "nodeType": "267", "messageId": "268", "endLine": 165, "endColumn": 41}, {"ruleId": "265", "severity": 1, "message": "284", "line": 229, "column": 9, "nodeType": "267", "messageId": "268", "endLine": 229, "endColumn": 27}, {"ruleId": "265", "severity": 1, "message": "285", "line": 242, "column": 9, "nodeType": "267", "messageId": "268", "endLine": 242, "endColumn": 25}, {"ruleId": "265", "severity": 1, "message": "272", "line": 476, "column": 9, "nodeType": "267", "messageId": "268", "endLine": 476, "endColumn": 23}, {"ruleId": "265", "severity": 1, "message": "286", "line": 18, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 18, "endColumn": 10}, {"ruleId": "265", "severity": 1, "message": "287", "line": 2, "column": 30, "nodeType": "267", "messageId": "268", "endLine": 2, "endColumn": 37}, {"ruleId": "265", "severity": 1, "message": "288", "line": 2, "column": 39, "nodeType": "267", "messageId": "268", "endLine": 2, "endColumn": 47}, {"ruleId": "265", "severity": 1, "message": "289", "line": 2, "column": 49, "nodeType": "267", "messageId": "268", "endLine": 2, "endColumn": 58}, {"ruleId": "265", "severity": 1, "message": "290", "line": 2, "column": 60, "nodeType": "267", "messageId": "268", "endLine": 2, "endColumn": 68}, {"ruleId": "291", "severity": 1, "message": "292", "line": 264, "column": 47, "nodeType": "293", "messageId": "294", "endLine": 277, "endColumn": 10}, {"ruleId": "265", "severity": 1, "message": "295", "line": 26, "column": 14, "nodeType": "267", "messageId": "268", "endLine": 26, "endColumn": 23}, {"ruleId": "265", "severity": 1, "message": "274", "line": 2, "column": 18, "nodeType": "267", "messageId": "268", "endLine": 2, "endColumn": 33}, {"ruleId": "265", "severity": 1, "message": "296", "line": 5, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 5, "endColumn": 11}, {"ruleId": "265", "severity": 1, "message": "297", "line": 9, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 9, "endColumn": 7}, {"ruleId": "265", "severity": 1, "message": "298", "line": 13, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 13, "endColumn": 10}, {"ruleId": "265", "severity": 1, "message": "299", "line": 6, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 6, "endColumn": 7}, {"ruleId": "265", "severity": 1, "message": "300", "line": 7, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 7, "endColumn": 14}, {"ruleId": "265", "severity": 1, "message": "297", "line": 14, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 14, "endColumn": 7}, {"ruleId": "265", "severity": 1, "message": "301", "line": 25, "column": 18, "nodeType": "267", "messageId": "268", "endLine": 25, "endColumn": 27}, {"ruleId": "265", "severity": 1, "message": "274", "line": 2, "column": 18, "nodeType": "267", "messageId": "268", "endLine": 2, "endColumn": 33}, {"ruleId": "265", "severity": 1, "message": "277", "line": 4, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 4, "endColumn": 12}, {"ruleId": "265", "severity": 1, "message": "275", "line": 9, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 9, "endColumn": 12}, {"ruleId": "265", "severity": 1, "message": "302", "line": 42, "column": 9, "nodeType": "267", "messageId": "268", "endLine": 42, "endColumn": 18}, {"ruleId": "265", "severity": 1, "message": "273", "line": 32, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 32, "endColumn": 8}, {"ruleId": "265", "severity": 1, "message": "303", "line": 42, "column": 14, "nodeType": "267", "messageId": "268", "endLine": 42, "endColumn": 25}, {"ruleId": "265", "severity": 1, "message": "304", "line": 46, "column": 17, "nodeType": "267", "messageId": "268", "endLine": 46, "endColumn": 29}, {"ruleId": "265", "severity": 1, "message": "305", "line": 62, "column": 5, "nodeType": "267", "messageId": "268", "endLine": 62, "endColumn": 19}, {"ruleId": "265", "severity": 1, "message": "306", "line": 65, "column": 5, "nodeType": "267", "messageId": "268", "endLine": 65, "endColumn": 23}, {"ruleId": "265", "severity": 1, "message": "307", "line": 181, "column": 9, "nodeType": "267", "messageId": "268", "endLine": 181, "endColumn": 29}, {"ruleId": "265", "severity": 1, "message": "308", "line": 28, "column": 13, "nodeType": "267", "messageId": "268", "endLine": 28, "endColumn": 23}, {"ruleId": "265", "severity": 1, "message": "309", "line": 31, "column": 17, "nodeType": "267", "messageId": "268", "endLine": 31, "endColumn": 27}, {"ruleId": "265", "severity": 1, "message": "310", "line": 47, "column": 10, "nodeType": "267", "messageId": "268", "endLine": 47, "endColumn": 23}, {"ruleId": "265", "severity": 1, "message": "311", "line": 47, "column": 25, "nodeType": "267", "messageId": "268", "endLine": 47, "endColumn": 41}, {"ruleId": "265", "severity": 1, "message": "312", "line": 48, "column": 10, "nodeType": "267", "messageId": "268", "endLine": 48, "endColumn": 22}, {"ruleId": "265", "severity": 1, "message": "313", "line": 48, "column": 24, "nodeType": "267", "messageId": "268", "endLine": 48, "endColumn": 39}, {"ruleId": "265", "severity": 1, "message": "314", "line": 6, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 6, "endColumn": 21}, {"ruleId": "265", "severity": 1, "message": "315", "line": 9, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 9, "endColumn": 19}, {"ruleId": "265", "severity": 1, "message": "316", "line": 5, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 5, "endColumn": 16}, {"ruleId": "265", "severity": 1, "message": "317", "line": 7, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 7, "endColumn": 21}, {"ruleId": "265", "severity": 1, "message": "318", "line": 9, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 9, "endColumn": 26}, {"ruleId": "265", "severity": 1, "message": "319", "line": 10, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 10, "endColumn": 17}, {"ruleId": "265", "severity": 1, "message": "320", "line": 11, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 11, "endColumn": 18}, {"ruleId": "265", "severity": 1, "message": "314", "line": 12, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 12, "endColumn": 21}, {"ruleId": "265", "severity": 1, "message": "321", "line": 14, "column": 24, "nodeType": "267", "messageId": "268", "endLine": 14, "endColumn": 29}, {"ruleId": "265", "severity": 1, "message": "314", "line": 6, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 6, "endColumn": 21}, {"ruleId": "265", "severity": 1, "message": "322", "line": 9, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 9, "endColumn": 19}, {"ruleId": "265", "severity": 1, "message": "316", "line": 12, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 12, "endColumn": 16}, {"ruleId": "265", "severity": 1, "message": "323", "line": 13, "column": 3, "nodeType": "267", "messageId": "268", "endLine": 13, "endColumn": 18}, {"ruleId": "265", "severity": 1, "message": "324", "line": 21, "column": 41, "nodeType": "267", "messageId": "268", "endLine": 21, "endColumn": 57}, {"ruleId": "265", "severity": 1, "message": "325", "line": 59, "column": 9, "nodeType": "267", "messageId": "268", "endLine": 59, "endColumn": 26}, "no-unused-vars", "'ChartBarIcon' is defined but never used.", "Identifier", "unusedVar", "'TrendingUpIcon' is defined but never used.", "'ShoppingCartIcon' is defined but never used.", "'products' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", "'Alert' is defined but never used.", "'AnimatePresence' is defined but never used.", "'ClockIcon' is defined but never used.", "'BellIcon' is defined but never used.", "'XMarkIcon' is defined but never used.", "'CheckIcon' is defined but never used.", "'GiftIcon' is defined but never used.", "'MapPinIcon' is defined but never used.", "'handleFeedbackOpen' is assigned a value but never used.", "'packageErrors' is assigned a value but never used.", "'setPackageErrors' is assigned a value but never used.", "'getPopularityColor' is assigned a value but never used.", "'getCategoryColor' is assigned a value but never used.", "'Tooltip' is defined but never used.", "'isAfter' is defined but never used.", "'isBefore' is defined but never used.", "'isSameDay' is defined but never used.", "'parseISO' is defined but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentTime', 'currentTime', 'currentTime'.", "ArrowFunctionExpression", "unsafeRefs", "'ColorIcon' is defined but never used.", "'StarIcon' is defined but never used.", "'Chip' is defined but never used.", "'Divider' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'MoneyIcon' is defined but never used.", "'suppliers' is assigned a value but never used.", "'ReceiptIcon' is defined but never used.", "'DiscountIcon' is defined but never used.", "'processPayment' is assigned a value but never used.", "'getActiveDiscounts' is assigned a value but never used.", "'handleProcessPayment' is assigned a value but never used.", "'PeopleIcon' is defined but never used.", "'ReportIcon' is defined but never used.", "'selectedMonth' is assigned a value but never used.", "'setSelectedMonth' is assigned a value but never used.", "'selectedYear' is assigned a value but never used.", "'setSelectedYear' is assigned a value but never used.", "'CurrencyDollarIcon' is defined but never used.", "'DocumentTextIcon' is defined but never used.", "'BanknotesIcon' is defined but never used.", "'ReceiptPercentIcon' is defined but never used.", "'ExclamationTriangleIcon' is defined but never used.", "'LockClosedIcon' is defined but never used.", "'ShieldCheckIcon' is defined but never used.", "'Input' is defined but never used.", "'CalendarDaysIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'validateDiscount' is assigned a value but never used.", "'availableStylists' is assigned a value but never used."]