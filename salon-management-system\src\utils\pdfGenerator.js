import jsPDF from 'jspdf';

export const generateInvoicePDF = (invoice) => {
  const pdf = new jsPDF();
  
  // Set font
  pdf.setFont('helvetica');
  
  // Company Header
  pdf.setFontSize(20);
  pdf.setTextColor(40, 40, 40);
  pdf.text('Salon Management System', 20, 30);
  
  pdf.setFontSize(10);
  pdf.setTextColor(100, 100, 100);
  pdf.text('123 Beauty Street', 20, 40);
  pdf.text('City, State 12345', 20, 45);
  pdf.text('Phone: (*************', 20, 50);
  pdf.text('Email: <EMAIL>', 20, 55);
  
  // Invoice Title and Number
  pdf.setFontSize(24);
  pdf.setTextColor(40, 40, 40);
  pdf.text('INVOICE', 150, 30);
  
  pdf.setFontSize(14);
  pdf.setTextColor(60, 60, 60);
  pdf.text(invoice.id, 150, 40);
  
  // Invoice Details
  pdf.setFontSize(10);
  pdf.text(`Date: ${invoice.date}`, 150, 50);
  pdf.text(`Due Date: ${invoice.dueDate}`, 150, 55);
  pdf.text(`Status: ${invoice.status.toUpperCase()}`, 150, 60);
  
  // Customer Information
  pdf.setFontSize(12);
  pdf.setTextColor(40, 40, 40);
  pdf.text('Bill To:', 20, 80);
  
  pdf.setFontSize(10);
  pdf.setTextColor(60, 60, 60);
  pdf.text(invoice.customerName, 20, 90);
  pdf.text(invoice.customerEmail, 20, 95);
  pdf.text(invoice.customerPhone, 20, 100);
  
  // Services Table Header
  const tableStartY = 120;
  pdf.setFontSize(10);
  pdf.setTextColor(40, 40, 40);
  
  // Table headers
  pdf.text('Service', 20, tableStartY);
  pdf.text('Stylist', 80, tableStartY);
  pdf.text('Price', 130, tableStartY);
  pdf.text('Duration', 160, tableStartY);
  
  // Draw header line
  pdf.setDrawColor(200, 200, 200);
  pdf.line(20, tableStartY + 2, 180, tableStartY + 2);
  
  // Services Table Content
  let currentY = tableStartY + 10;
  pdf.setFontSize(9);
  pdf.setTextColor(60, 60, 60);
  
  invoice.services.forEach((service, index) => {
    pdf.text(service.name, 20, currentY);
    pdf.text(service.stylist, 80, currentY);
    pdf.text(`₹${service.price.toLocaleString('en-IN')}`, 130, currentY);
    pdf.text(`${service.duration} min`, 160, currentY);
    
    currentY += 8;
    
    // Add page break if needed
    if (currentY > 250) {
      pdf.addPage();
      currentY = 30;
    }
  });
  
  // Draw line before totals
  pdf.setDrawColor(200, 200, 200);
  pdf.line(130, currentY + 2, 180, currentY + 2);
  
  // Totals Section
  currentY += 15;
  pdf.setFontSize(10);
  pdf.setTextColor(60, 60, 60);
  
  // Subtotal
  pdf.text('Subtotal:', 130, currentY);
  pdf.text(`₹${invoice.subtotal.toLocaleString('en-IN')}`, 175, currentY);
  currentY += 8;
  
  // Discount (if applicable)
  if (invoice.discountAmount > 0) {
    pdf.setTextColor(0, 150, 0);
    pdf.text(`Discount (${invoice.discountType === 'percentage' ? `${invoice.discountValue}%` : 'Fixed'}):`, 130, currentY);
    pdf.text(`-₹${invoice.discountAmount.toLocaleString('en-IN')}`, 175, currentY);
    currentY += 8;
    pdf.setTextColor(60, 60, 60);
  }
  
  // Tax (GST)
  pdf.text(`GST (${invoice.taxRate}%):`, 130, currentY);
  pdf.text(`₹${invoice.taxAmount.toLocaleString('en-IN')}`, 175, currentY);
  currentY += 8;
  
  // Total
  pdf.setDrawColor(40, 40, 40);
  pdf.line(130, currentY, 180, currentY);
  currentY += 8;
  
  pdf.setFontSize(12);
  pdf.setTextColor(40, 40, 40);
  pdf.text('Total:', 130, currentY);
  pdf.text(`₹${invoice.total.toLocaleString('en-IN')}`, 175, currentY);
  
  // Payment Information (if paid)
  if (invoice.status === 'paid' && invoice.paymentMethod) {
    currentY += 20;
    pdf.setFontSize(10);
    pdf.setTextColor(0, 150, 0);
    pdf.text('Payment Information:', 20, currentY);
    currentY += 8;
    pdf.setTextColor(60, 60, 60);
    pdf.text(`Method: ${invoice.paymentMethod.toUpperCase()}`, 20, currentY);
    if (invoice.paymentDate) {
      currentY += 6;
      pdf.text(`Date: ${invoice.paymentDate}`, 20, currentY);
    }
    if (invoice.transactionId) {
      currentY += 6;
      pdf.text(`Transaction ID: ${invoice.transactionId}`, 20, currentY);
    }
  }
  
  // Notes (if any)
  if (invoice.notes) {
    currentY += 20;
    pdf.setFontSize(10);
    pdf.setTextColor(40, 40, 40);
    pdf.text('Notes:', 20, currentY);
    currentY += 8;
    pdf.setTextColor(60, 60, 60);
    
    // Split notes into multiple lines if too long
    const splitNotes = pdf.splitTextToSize(invoice.notes, 170);
    splitNotes.forEach((line) => {
      pdf.text(line, 20, currentY);
      currentY += 6;
    });
  }
  
  // Footer
  const pageHeight = pdf.internal.pageSize.height;
  pdf.setFontSize(8);
  pdf.setTextColor(150, 150, 150);
  pdf.text('Thank you for your business!', 20, pageHeight - 20);
  pdf.text(`Generated on ${new Date().toLocaleDateString()}`, 20, pageHeight - 15);
  
  // Save the PDF
  pdf.save(`invoice-${invoice.id}.pdf`);
};

export const generateInvoicePreview = (invoice) => {
  const pdf = new jsPDF();
  
  // Use the same generation logic as above
  // This function can be used to generate a preview without downloading
  
  // Return the PDF as a blob for preview
  return pdf.output('blob');
};

export const exportInvoicesPDF = (invoices) => {
  const pdf = new jsPDF();

  // Title page
  pdf.setFontSize(24);
  pdf.setTextColor(40, 40, 40);
  pdf.text('Invoice Export Report', 20, 30);

  pdf.setFontSize(12);
  pdf.setTextColor(60, 60, 60);
  pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 45);
  pdf.text(`Total Invoices: ${invoices.length}`, 20, 55);

  // Calculate totals
  const totalAmount = invoices.reduce((sum, inv) => sum + inv.total, 0);
  const paidAmount = invoices.filter(inv => inv.status === 'paid').reduce((sum, inv) => sum + inv.total, 0);
  const pendingAmount = invoices.filter(inv => inv.status === 'pending').reduce((sum, inv) => sum + inv.total, 0);

  pdf.text(`Total Amount: ₹${totalAmount.toLocaleString('en-IN')}`, 20, 65);
  pdf.text(`Paid Amount: ₹${paidAmount.toLocaleString('en-IN')}`, 20, 75);
  pdf.text(`Pending Amount: ₹${pendingAmount.toLocaleString('en-IN')}`, 20, 85);

  // Summary table header
  let currentY = 110;
  pdf.setFontSize(14);
  pdf.setTextColor(40, 40, 40);
  pdf.text('Invoice Summary', 20, currentY);

  currentY += 15;
  pdf.setFontSize(10);

  // Table headers
  pdf.text('Invoice ID', 20, currentY);
  pdf.text('Customer', 60, currentY);
  pdf.text('Date', 110, currentY);
  pdf.text('Status', 140, currentY);
  pdf.text('Amount', 170, currentY);

  // Draw header line
  pdf.setDrawColor(200, 200, 200);
  pdf.line(20, currentY + 2, 190, currentY + 2);

  currentY += 10;

  // Invoice rows
  pdf.setFontSize(9);
  pdf.setTextColor(60, 60, 60);

  invoices.forEach((invoice, index) => {
    if (currentY > 270) {
      pdf.addPage();
      currentY = 30;

      // Repeat headers on new page
      pdf.setFontSize(10);
      pdf.setTextColor(40, 40, 40);
      pdf.text('Invoice ID', 20, currentY);
      pdf.text('Customer', 60, currentY);
      pdf.text('Date', 110, currentY);
      pdf.text('Status', 140, currentY);
      pdf.text('Amount', 170, currentY);

      pdf.setDrawColor(200, 200, 200);
      pdf.line(20, currentY + 2, 190, currentY + 2);
      currentY += 10;
      pdf.setFontSize(9);
      pdf.setTextColor(60, 60, 60);
    }

    pdf.text(invoice.id, 20, currentY);
    pdf.text(invoice.customerName.substring(0, 20), 60, currentY);
    pdf.text(invoice.date, 110, currentY);

    // Color code status
    if (invoice.status === 'paid') {
      pdf.setTextColor(0, 150, 0);
    } else if (invoice.status === 'pending') {
      pdf.setTextColor(255, 140, 0);
    } else if (invoice.status === 'overdue') {
      pdf.setTextColor(220, 20, 60);
    }

    pdf.text(invoice.status.toUpperCase(), 140, currentY);
    pdf.setTextColor(60, 60, 60);
    pdf.text(`₹${invoice.total.toLocaleString('en-IN')}`, 170, currentY);

    currentY += 8;
  });

  // Footer
  const pageHeight = pdf.internal.pageSize.height;
  pdf.setFontSize(8);
  pdf.setTextColor(150, 150, 150);
  pdf.text('Salon Management System - Invoice Export', 20, pageHeight - 20);
  pdf.text(`Generated on ${new Date().toLocaleDateString()}`, 20, pageHeight - 15);

  // Save the PDF
  pdf.save(`invoices-export-${new Date().toISOString().split('T')[0]}.pdf`);
};

export const printInvoice = (invoice) => {
  const pdf = new jsPDF();

  // Generate PDF with same logic as generateInvoicePDF
  // But open in new window for printing instead of downloading

  const pdfUrl = pdf.output('bloburl');
  const printWindow = window.open(pdfUrl);

  if (printWindow) {
    printWindow.onload = () => {
      printWindow.print();
    };
  }
};

// Inventory PDF Export Functions
export const exportInventoryPDF = (products, options = {}) => {
  const pdf = new jsPDF();

  // Set font
  pdf.setFont('helvetica');

  // Company Header
  pdf.setFontSize(20);
  pdf.setTextColor(40, 40, 40);
  pdf.text('Salon Management System', 20, 30);

  pdf.setFontSize(10);
  pdf.setTextColor(100, 100, 100);
  pdf.text('123 Beauty Street', 20, 40);
  pdf.text('City, State 12345', 20, 45);
  pdf.text('Phone: (*************', 20, 50);
  pdf.text('Email: <EMAIL>', 20, 55);

  // Report Title
  pdf.setFontSize(24);
  pdf.setTextColor(40, 40, 40);
  pdf.text('INVENTORY REPORT', 20, 80);

  pdf.setFontSize(12);
  pdf.setTextColor(60, 60, 60);
  pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 95);
  pdf.text(`Total Products: ${products.length}`, 20, 105);

  // Calculate totals
  const totalValue = products.reduce((sum, product) => sum + (product.currentStock * product.unitPrice), 0);
  const lowStockCount = products.filter(p => p.currentStock <= p.minStockLevel).length;
  const outOfStockCount = products.filter(p => p.currentStock === 0).length;

  pdf.text(`Total Inventory Value: ₹${totalValue.toLocaleString('en-IN')}`, 20, 115);
  pdf.text(`Low Stock Items: ${lowStockCount}`, 20, 125);
  pdf.text(`Out of Stock Items: ${outOfStockCount}`, 20, 135);

  // Table headers
  let yPosition = 160;
  pdf.setFontSize(10);
  pdf.setTextColor(40, 40, 40);
  pdf.setFont('helvetica', 'bold');

  const headers = ['Product Name', 'SKU', 'Category', 'Stock', 'Min/Max', 'Unit Price', 'Total Value', 'Status'];
  const columnWidths = [35, 25, 25, 15, 20, 20, 25, 15];
  let xPosition = 20;

  headers.forEach((header, index) => {
    pdf.text(header, xPosition, yPosition);
    xPosition += columnWidths[index];
  });

  // Draw header line
  pdf.line(20, yPosition + 2, 200, yPosition + 2);

  // Table data
  pdf.setFont('helvetica', 'normal');
  pdf.setFontSize(8);
  yPosition += 10;

  products.forEach((product, index) => {
    if (yPosition > 270) {
      pdf.addPage();
      yPosition = 30;

      // Repeat headers on new page
      pdf.setFont('helvetica', 'bold');
      pdf.setFontSize(10);
      xPosition = 20;
      headers.forEach((header, index) => {
        pdf.text(header, xPosition, yPosition);
        xPosition += columnWidths[index];
      });
      pdf.line(20, yPosition + 2, 200, yPosition + 2);
      pdf.setFont('helvetica', 'normal');
      pdf.setFontSize(8);
      yPosition += 10;
    }

    xPosition = 20;
    const rowData = [
      product.name.substring(0, 20) + (product.name.length > 20 ? '...' : ''),
      product.sku,
      product.category.substring(0, 15) + (product.category.length > 15 ? '...' : ''),
      product.currentStock.toString(),
      `${product.minStockLevel}/${product.maxStockLevel}`,
      `₹${product.unitPrice.toLocaleString('en-IN')}`,
      `₹${(product.currentStock * product.unitPrice).toLocaleString('en-IN')}`,
      product.currentStock === 0 ? 'Out' : product.currentStock <= product.minStockLevel ? 'Low' : 'OK'
    ];

    // Set color based on stock status
    if (product.currentStock === 0) {
      pdf.setTextColor(220, 53, 69); // Red for out of stock
    } else if (product.currentStock <= product.minStockLevel) {
      pdf.setTextColor(255, 193, 7); // Yellow for low stock
    } else {
      pdf.setTextColor(40, 40, 40); // Normal color
    }

    rowData.forEach((data, index) => {
      pdf.text(data, xPosition, yPosition);
      xPosition += columnWidths[index];
    });

    yPosition += 8;
  });

  // Footer
  const pageHeight = pdf.internal.pageSize.height;
  pdf.setFontSize(8);
  pdf.setTextColor(150, 150, 150);
  pdf.text('Salon Management System - Inventory Report', 20, pageHeight - 20);
  pdf.text(`Generated on ${new Date().toLocaleDateString()}`, 20, pageHeight - 15);

  // Save the PDF
  pdf.save(`inventory-report-${new Date().toISOString().split('T')[0]}.pdf`);
};

export const exportLowStockPDF = (products) => {
  const lowStockProducts = products.filter(p => p.currentStock <= p.minStockLevel);

  const pdf = new jsPDF();
  pdf.setFont('helvetica');

  // Header
  pdf.setFontSize(20);
  pdf.setTextColor(40, 40, 40);
  pdf.text('Low Stock Alert Report', 20, 30);

  pdf.setFontSize(12);
  pdf.setTextColor(60, 60, 60);
  pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 45);
  pdf.text(`Low Stock Items: ${lowStockProducts.length}`, 20, 55);

  let yPosition = 80;

  lowStockProducts.forEach((product, index) => {
    if (yPosition > 250) {
      pdf.addPage();
      yPosition = 30;
    }

    pdf.setFontSize(14);
    pdf.setTextColor(220, 53, 69);
    pdf.text(`${index + 1}. ${product.name}`, 20, yPosition);

    pdf.setFontSize(10);
    pdf.setTextColor(60, 60, 60);
    pdf.text(`SKU: ${product.sku}`, 30, yPosition + 10);
    pdf.text(`Current Stock: ${product.currentStock}`, 30, yPosition + 20);
    pdf.text(`Minimum Level: ${product.minStockLevel}`, 30, yPosition + 30);
    pdf.text(`Supplier: ${product.supplier}`, 30, yPosition + 40);

    yPosition += 60;
  });

  pdf.save(`low-stock-report-${new Date().toISOString().split('T')[0]}.pdf`);
};
