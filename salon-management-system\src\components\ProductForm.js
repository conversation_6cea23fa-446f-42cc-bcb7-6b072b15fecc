import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  XMarkIcon,
  CubeIcon,
  TagIcon,
  CurrencyDollarIcon,
  BuildingStorefrontIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import { Button, Card, Input, Modal } from './ui';
import { useInventory } from '../contexts/InventoryContext';

const ProductForm = ({ open, onClose, product = null, mode = 'add' }) => {
  const { addProduct, updateProduct, getCategories, getSuppliers } = useInventory();
  
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    brand: '',
    sku: '',
    currentStock: 0,
    minStockLevel: 0,
    maxStockLevel: 0,
    unitPrice: 0,
    supplier: '',
    description: '',
    expiryDate: '',
    location: '',
    barcode: '',
    usageRate: 0,
    status: 'active'
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const categories = getCategories();
  const suppliers = getSuppliers();

  // Predefined categories for new products
  const predefinedCategories = [
    'Hair Care',
    'Hair Color',
    'Styling Products',
    'Nail Care',
    'Skincare',
    'Tools & Equipment',
    'Cleaning Supplies',
    'Other'
  ];

  const allCategories = [...new Set([...categories, ...predefinedCategories])];

  useEffect(() => {
    if (product && mode === 'edit') {
      setFormData({
        name: product.name || '',
        category: product.category || '',
        brand: product.brand || '',
        sku: product.sku || '',
        currentStock: product.currentStock || 0,
        minStockLevel: product.minStockLevel || 0,
        maxStockLevel: product.maxStockLevel || 0,
        unitPrice: product.unitPrice || 0,
        supplier: product.supplier || '',
        description: product.description || '',
        expiryDate: product.expiryDate || '',
        location: product.location || '',
        barcode: product.barcode || '',
        usageRate: product.usageRate || 0,
        status: product.status || 'active'
      });
    } else {
      // Reset form for add mode
      setFormData({
        name: '',
        category: '',
        brand: '',
        sku: '',
        currentStock: 0,
        minStockLevel: 0,
        maxStockLevel: 0,
        unitPrice: 0,
        supplier: '',
        description: '',
        expiryDate: '',
        location: '',
        barcode: '',
        usageRate: 0,
        status: 'active'
      });
    }
    setErrors({});
  }, [product, mode, open]);

  const handleChange = (field) => (event) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Product name is required';
    }

    if (!formData.category.trim()) {
      newErrors.category = 'Category is required';
    }

    if (!formData.brand.trim()) {
      newErrors.brand = 'Brand is required';
    }

    if (!formData.sku.trim()) {
      newErrors.sku = 'SKU is required';
    }

    if (formData.currentStock < 0) {
      newErrors.currentStock = 'Current stock cannot be negative';
    }

    if (formData.minStockLevel < 0) {
      newErrors.minStockLevel = 'Minimum stock level cannot be negative';
    }

    if (formData.maxStockLevel <= 0) {
      newErrors.maxStockLevel = 'Maximum stock level must be greater than 0';
    }

    if (formData.minStockLevel >= formData.maxStockLevel) {
      newErrors.minStockLevel = 'Minimum stock level must be less than maximum';
    }

    if (formData.unitPrice <= 0) {
      newErrors.unitPrice = 'Unit price must be greater than 0';
    }

    if (!formData.supplier.trim()) {
      newErrors.supplier = 'Supplier is required';
    }

    if (formData.usageRate < 0) {
      newErrors.usageRate = 'Usage rate cannot be negative';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const productData = {
        ...formData,
        currentStock: Number(formData.currentStock),
        minStockLevel: Number(formData.minStockLevel),
        maxStockLevel: Number(formData.maxStockLevel),
        unitPrice: Number(formData.unitPrice),
        usageRate: Number(formData.usageRate)
      };

      if (mode === 'edit' && product) {
        updateProduct(product.id, productData);
      } else {
        addProduct(productData);
      }

      onClose();
    } catch (error) {
      console.error('Error saving product:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  return (
    <Modal
      isOpen={open}
      onClose={handleClose}
      title={
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
            <CubeIcon className="h-6 w-6 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {mode === 'edit' ? 'Edit Product' : 'Add New Product'}
            </h2>
            <p className="text-sm text-gray-500">
              {mode === 'edit' ? 'Update product information' : 'Create a new product in your inventory'}
            </p>
          </div>
        </div>
      }
      size="xl"
    >
      <div className="space-y-8">
        {/* Basic Information Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <div className="p-6">
              <div className="flex items-center space-x-2 mb-6">
                <TagIcon className="h-5 w-5 text-blue-600" />
                <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Input
                    label="Product Name"
                    value={formData.name}
                    onChange={handleChange('name')}
                    error={errors.name}
                    required
                    placeholder="Enter product name"
                  />
                </div>

                <div>
                  <Input
                    label="Brand"
                    value={formData.brand}
                    onChange={handleChange('brand')}
                    error={errors.brand}
                    required
                    placeholder="Enter brand name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category *
                  </label>
                  <select
                    value={formData.category}
                    onChange={handleChange('category')}
                    className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 bg-white ${
                      errors.category ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-200'
                    } focus:ring-2`}
                  >
                    <option value="">Select a category</option>
                    {allCategories.map((category) => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                  {errors.category && (
                    <p className="mt-1 text-sm text-red-600">{errors.category}</p>
                  )}
                </div>

                <div>
                  <Input
                    label="SKU"
                    value={formData.sku}
                    onChange={handleChange('sku')}
                    error={errors.sku}
                    required
                    placeholder="Enter SKU code"
                  />
                </div>
              </div>

              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={handleChange('description')}
                  rows={3}
                  placeholder="Enter product description..."
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 resize-none"
                />
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Stock Information Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <div className="p-6">
              <div className="flex items-center space-x-2 mb-6">
                <CubeIcon className="h-5 w-5 text-green-600" />
                <h3 className="text-lg font-semibold text-gray-900">Stock Information</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <Input
                    label="Current Stock"
                    type="number"
                    value={formData.currentStock}
                    onChange={handleChange('currentStock')}
                    error={errors.currentStock}
                    min="0"
                    placeholder="0"
                  />
                </div>

                <div>
                  <Input
                    label="Minimum Stock Level"
                    type="number"
                    value={formData.minStockLevel}
                    onChange={handleChange('minStockLevel')}
                    error={errors.minStockLevel}
                    min="0"
                    required
                    placeholder="0"
                  />
                </div>

                <div>
                  <Input
                    label="Maximum Stock Level"
                    type="number"
                    value={formData.maxStockLevel}
                    onChange={handleChange('maxStockLevel')}
                    error={errors.maxStockLevel}
                    min="1"
                    required
                    placeholder="0"
                  />
                </div>
              </div>

              {/* Stock Status Indicator */}
              {formData.currentStock !== '' && formData.minStockLevel !== '' && (
                <div className="mt-6 p-4 rounded-lg bg-gray-50">
                  <div className="flex items-center space-x-2">
                    {parseInt(formData.currentStock) === 0 ? (
                      <>
                        <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
                        <span className="text-red-700 font-medium">Out of Stock</span>
                      </>
                    ) : parseInt(formData.currentStock) <= parseInt(formData.minStockLevel) ? (
                      <>
                        <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
                        <span className="text-yellow-700 font-medium">Low Stock</span>
                      </>
                    ) : (
                      <>
                        <CheckCircleIcon className="h-5 w-5 text-green-500" />
                        <span className="text-green-700 font-medium">In Stock</span>
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
          </Card>
        </motion.div>

        {/* Pricing and Supplier Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <div className="p-6">
              <div className="flex items-center space-x-2 mb-6">
                <CurrencyDollarIcon className="h-5 w-5 text-purple-600" />
                <h3 className="text-lg font-semibold text-gray-900">Pricing & Supplier</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Unit Price *
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 sm:text-sm">₹</span>
                    </div>
                    <input
                      type="number"
                      value={formData.unitPrice}
                      onChange={handleChange('unitPrice')}
                      min="0"
                      step="0.01"
                      placeholder="0.00"
                      className={`w-full pl-8 pr-4 py-3 rounded-lg border transition-all duration-200 ${
                        errors.unitPrice ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-200'
                      } focus:ring-2`}
                    />
                  </div>
                  {errors.unitPrice && (
                    <p className="mt-1 text-sm text-red-600">{errors.unitPrice}</p>
                  )}
                </div>

                <div>
                  <Input
                    label="Supplier"
                    value={formData.supplier}
                    onChange={handleChange('supplier')}
                    error={errors.supplier}
                    required
                    placeholder="Enter supplier name"
                    icon={<BuildingStorefrontIcon className="h-5 w-5" />}
                  />
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Additional Information Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <div className="p-6">
              <div className="flex items-center space-x-2 mb-6">
                <InformationCircleIcon className="h-5 w-5 text-indigo-600" />
                <h3 className="text-lg font-semibold text-gray-900">Additional Information</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Expiry Date
                  </label>
                  <input
                    type="date"
                    value={formData.expiryDate}
                    onChange={handleChange('expiryDate')}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                  />
                </div>

                <div>
                  <Input
                    label="Storage Location"
                    value={formData.location}
                    onChange={handleChange('location')}
                    placeholder="e.g., Storage Room A - Shelf 1"
                  />
                </div>

                <div>
                  <Input
                    label="Barcode"
                    value={formData.barcode}
                    onChange={handleChange('barcode')}
                    placeholder="Enter barcode"
                  />
                </div>

                <div>
                  <Input
                    label="Usage Rate (per week)"
                    type="number"
                    value={formData.usageRate}
                    onChange={handleChange('usageRate')}
                    error={errors.usageRate}
                    min="0"
                    step="0.1"
                    placeholder="0.0"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Status
                  </label>
                  <select
                    value={formData.status}
                    onChange={handleChange('status')}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 bg-white"
                  >
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="discontinued">Discontinued</option>
                  </select>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>

      <Modal.Footer>
        <Button variant="outline" onClick={handleClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button
          variant="primary"
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="bg-gradient-to-r from-blue-500 to-purple-600 hover:shadow-lg"
        >
          {isSubmitting ? 'Saving...' : (mode === 'edit' ? 'Update Product' : 'Add Product')}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ProductForm;
