import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  TagIcon,
  ReceiptPercentIcon,
  CurrencyDollarIcon,
  CalendarDaysIcon,
  UserGroupIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import { Button, Card, Input, Modal } from './ui';
import { useBilling } from '../contexts/BillingContext';

const DiscountForm = ({ open, onClose, discount = null, mode = 'add' }) => {
  const { createDiscount, updateDiscount } = useBilling();
  
  const [formData, setFormData] = useState({
    code: '',
    name: '',
    type: 'percentage',
    value: 0,
    maxDiscount: 0,
    validFrom: '',
    validTo: '',
    usageLimit: 100,
    description: '',
    status: 'active'
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (discount && mode === 'edit') {
      setFormData({
        code: discount.code || '',
        name: discount.name || '',
        type: discount.type || 'percentage',
        value: discount.value || 0,
        maxDiscount: discount.maxDiscount || 0,
        validFrom: discount.validFrom || '',
        validTo: discount.validTo || '',
        usageLimit: discount.usageLimit || 100,
        description: discount.description || '',
        status: discount.status || 'active'
      });
    } else {
      // Reset form for add mode
      const today = new Date().toISOString().split('T')[0];
      const nextMonth = new Date();
      nextMonth.setMonth(nextMonth.getMonth() + 1);
      
      setFormData({
        code: '',
        name: '',
        type: 'percentage',
        value: 0,
        maxDiscount: 0,
        validFrom: today,
        validTo: nextMonth.toISOString().split('T')[0],
        usageLimit: 100,
        description: '',
        status: 'active'
      });
    }
    setErrors({});
  }, [discount, mode, open]);

  const handleChange = (field) => (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.code.trim()) {
      newErrors.code = 'Discount code is required';
    } else if (formData.code.length < 3) {
      newErrors.code = 'Code must be at least 3 characters';
    }

    if (!formData.name.trim()) {
      newErrors.name = 'Discount name is required';
    }

    if (formData.value <= 0) {
      newErrors.value = 'Discount value must be greater than 0';
    }

    if (formData.type === 'percentage' && formData.value > 100) {
      newErrors.value = 'Percentage cannot exceed 100%';
    }

    if (formData.minAmount < 0) {
      newErrors.minAmount = 'Minimum amount cannot be negative';
    }

    if (formData.maxDiscount < 0) {
      newErrors.maxDiscount = 'Maximum discount cannot be negative';
    }

    if (!formData.validFrom) {
      newErrors.validFrom = 'Valid from date is required';
    }

    if (!formData.validTo) {
      newErrors.validTo = 'Valid to date is required';
    }

    if (formData.validFrom && formData.validTo && formData.validFrom >= formData.validTo) {
      newErrors.validTo = 'End date must be after start date';
    }

    if (formData.usageLimit <= 0) {
      newErrors.usageLimit = 'Usage limit must be greater than 0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const discountData = {
        ...formData,
        code: formData.code.toUpperCase(),
        value: Number(formData.value),
        minAmount: Number(formData.minAmount),
        maxDiscount: Number(formData.maxDiscount),
        usageLimit: Number(formData.usageLimit)
      };

      if (mode === 'edit' && discount) {
        updateDiscount(discount.id, discountData);
      } else {
        createDiscount(discountData);
      }

      onClose();
    } catch (error) {
      console.error('Error saving discount:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  return (
    <Modal
      isOpen={open}
      onClose={handleClose}
      title={
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg">
            <SparklesIcon className="h-6 w-6 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {mode === 'edit' ? 'Edit Discount' : 'Create New Discount'}
            </h2>
            <p className="text-sm text-gray-500">
              {mode === 'edit' ? 'Update discount information' : 'Create a new discount code for your customers'}
            </p>
          </div>
        </div>
      }
      size="xl"
    >
      <div className="space-y-8">
        {/* Basic Information Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <div className="p-6">
              <div className="flex items-center space-x-2 mb-6">
                <TagIcon className="h-5 w-5 text-purple-600" />
                <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Input
                    label="Discount Code"
                    value={formData.code}
                    onChange={handleChange('code')}
                    error={errors.code}
                    required
                    placeholder="e.g., SUMMER20, WELCOME10"
                    className="uppercase"
                    icon={<TagIcon className="h-5 w-5" />}
                  />
                  <p className="mt-1 text-xs text-gray-500">Code will be automatically converted to uppercase</p>
                </div>

                <div>
                  <Input
                    label="Discount Name"
                    value={formData.name}
                    onChange={handleChange('name')}
                    error={errors.name}
                    required
                    placeholder="Enter discount name"
                    icon={<SparklesIcon className="h-5 w-5" />}
                  />
                </div>
              </div>

              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={handleChange('description')}
                  rows={2}
                  placeholder="Brief description of the discount"
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all duration-200 resize-none"
                />
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Discount Configuration Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <div className="p-6">
              <div className="flex items-center space-x-2 mb-6">
                <ReceiptPercentIcon className="h-5 w-5 text-green-600" />
                <h3 className="text-lg font-semibold text-gray-900">Discount Configuration</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Discount Type *
                  </label>
                  <select
                    value={formData.type}
                    onChange={handleChange('type')}
                    className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 bg-white ${
                      errors.type ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : 'border-gray-300 focus:border-purple-500 focus:ring-purple-200'
                    } focus:ring-2`}
                  >
                    <option value="">Select type</option>
                    <option value="percentage">Percentage</option>
                    <option value="fixed">Fixed Amount</option>
                  </select>
                  {errors.type && (
                    <p className="mt-1 text-sm text-red-600">{errors.type}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Discount Value *
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      value={formData.value}
                      onChange={handleChange('value')}
                      min="0"
                      step={formData.type === 'percentage' ? '1' : '0.01'}
                      placeholder="0"
                      className={`w-full px-4 py-3 pr-12 rounded-lg border transition-all duration-200 ${
                        errors.value ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : 'border-gray-300 focus:border-purple-500 focus:ring-purple-200'
                      } focus:ring-2`}
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 sm:text-sm">
                        {formData.type === 'percentage' ? '%' : '$'}</span>
                    </div>
                  </div>
                  {errors.value && (
                    <p className="mt-1 text-sm text-red-600">{errors.value}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Maximum Discount
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 sm:text-sm">$</span>
                    </div>
                    <input
                      type="number"
                      value={formData.maxDiscount}
                      onChange={handleChange('maxDiscount')}
                      min="0"
                      step="0.01"
                      placeholder="0.00"
                      className={`w-full pl-8 pr-4 py-3 rounded-lg border transition-all duration-200 ${
                        errors.maxDiscount ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : 'border-gray-300 focus:border-purple-500 focus:ring-purple-200'
                      } focus:ring-2`}
                    />
                  </div>
                  {errors.maxDiscount && (
                    <p className="mt-1 text-sm text-red-600">{errors.maxDiscount}</p>
                  )}
                  <p className="mt-1 text-xs text-gray-500">Maximum discount amount (for percentage type)</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                  <Input
                    label="Usage Limit"
                    type="number"
                    value={formData.usageLimit}
                    onChange={handleChange('usageLimit')}
                    error={errors.usageLimit}
                    min="1"
                    required
                    placeholder="1"
                    icon={<UserGroupIcon className="h-5 w-5" />}
                  />
                  <p className="mt-1 text-xs text-gray-500">Maximum number of times this discount can be used</p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Validity Period Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <div className="p-6">
              <div className="flex items-center space-x-2 mb-6">
                <CalendarDaysIcon className="h-5 w-5 text-blue-600" />
                <h3 className="text-lg font-semibold text-gray-900">Validity Period</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Valid From *
                  </label>
                  <input
                    type="date"
                    value={formData.validFrom}
                    onChange={handleChange('validFrom')}
                    className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 ${
                      errors.validFrom ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-200'
                    } focus:ring-2`}
                  />
                  {errors.validFrom && (
                    <p className="mt-1 text-sm text-red-600">{errors.validFrom}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Valid To *
                  </label>
                  <input
                    type="date"
                    value={formData.validTo}
                    onChange={handleChange('validTo')}
                    className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 ${
                      errors.validTo ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : 'border-gray-300 focus:border-blue-500 focus:ring-blue-200'
                    } focus:ring-2`}
                  />
                  {errors.validTo && (
                    <p className="mt-1 text-sm text-red-600">{errors.validTo}</p>
                  )}
                </div>
              </div>

              <div className="mt-6">
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    {formData.status === 'active' ? (
                      <CheckCircleIcon className="h-5 w-5 text-green-500" />
                    ) : (
                      <ExclamationTriangleIcon className="h-5 w-5 text-gray-400" />
                    )}
                    <div>
                      <p className="font-medium text-gray-900">Discount Status</p>
                      <p className="text-sm text-gray-500">
                        {formData.status === 'active' ? 'This discount is active and can be used' : 'This discount is inactive'}
                      </p>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.status === 'active'}
                      onChange={(e) => handleChange('status')({ target: { value: e.target.checked ? 'active' : 'inactive' } })}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                  </label>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>

      <Modal.Footer>
        <Button variant="outline" onClick={handleClose} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button
          variant="primary"
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="bg-gradient-to-r from-purple-500 to-pink-600 hover:shadow-lg"
        >
          {isSubmitting ? 'Saving...' : (mode === 'edit' ? 'Update Discount' : 'Create Discount')}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default DiscountForm;
