import React from 'react';
import { motion } from 'framer-motion';
import {
  ChartBarIcon,
  UsersIcon,
  CalendarDaysIcon,
  CurrencyDollarIcon,
  ClockIcon,
  UserPlusIcon,
  EyeIcon,
  TrendingUpIcon,
  ExclamationTriangleIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  CubeIcon,
  ShoppingCartIcon
} from '@heroicons/react/24/outline';
import { Card, Button } from './ui';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { useInventory } from '../contexts/InventoryContext';
import InventoryAlerts from './InventoryAlerts';

const Dashboard = () => {
  const { user, isAdmin, isStaff } = useAuth();
  const navigate = useNavigate();
  const {
    products,
    getLowStockProducts,
    getOutOfStockProducts,
    getTotalInventoryValue
  } = useInventory();

  // Role-based stats - different data based on user role
  const getStatsForRole = () => {
    const lowStockCount = getLowStockProducts().length;
    const outOfStockCount = getOutOfStockProducts().length;
    const totalInventoryValue = getTotalInventoryValue();

    if (isAdmin()) {
      return [
        {
          title: 'Today\'s Revenue',
          value: '₹1,03,750',
          icon: CurrencyDollarIcon,
          gradient: 'from-green-500 to-emerald-600',
          change: '+12%',
          changeType: 'increase',
        },
        {
          title: 'Appointments Today',
          value: '24',
          icon: CalendarDaysIcon,
          gradient: 'from-blue-500 to-cyan-600',
          change: '+5%',
          changeType: 'increase',
        },
        {
          title: 'Total Customers',
          value: '1,847',
          icon: UsersIcon,
          gradient: 'from-purple-500 to-pink-600',
          change: '+8%',
          changeType: 'increase',
        },
        {
          title: 'Inventory Value',
          value: `₹${totalInventoryValue.toLocaleString('en-IN')}`,
          icon: CubeIcon,
          gradient: 'from-orange-500 to-red-600',
          change: lowStockCount > 0 ? `${lowStockCount} low stock` : 'All good',
          changeType: lowStockCount > 0 ? 'warning' : 'increase',
          alert: lowStockCount > 0 || outOfStockCount > 0
        },
      ];
    } else if (isStaff()) {
      return [
        {
          title: 'My Appointments Today',
          value: '8',
          icon: CalendarDaysIcon,
          gradient: 'from-blue-500 to-cyan-600',
          change: '+2',
          changeType: 'increase',
        },
        {
          title: 'Completed Today',
          value: '3',
          icon: ClockIcon,
          gradient: 'from-green-500 to-emerald-600',
          change: '+1',
          changeType: 'increase',
        },
        {
          title: 'My Customers',
          value: '156',
          icon: UsersIcon,
          gradient: 'from-purple-500 to-pink-600',
          change: '+5',
          changeType: 'increase',
        },
        {
          title: 'Today\'s Earnings',
          value: '₹26,560',
          icon: CurrencyDollarIcon,
          gradient: 'from-orange-500 to-red-600',
          change: '+15%',
          changeType: 'increase',
        },
      ];
    }
  };

  const stats = getStatsForRole();

  const todayAppointments = [
    {
      id: 1,
      customer: 'Sarah Johnson',
      service: 'Hair Cut & Style',
      time: '9:00 AM',
      status: 'completed',
      stylist: 'Emma Wilson',
    },
    {
      id: 2,
      customer: 'Mike Davis',
      service: 'Beard Trim',
      time: '10:30 AM',
      status: 'in-progress',
      stylist: 'John Smith',
    },
    {
      id: 3,
      customer: 'Lisa Brown',
      service: 'Hair Color',
      time: '11:00 AM',
      status: 'scheduled',
      stylist: 'Emma Wilson',
    },
    {
      id: 4,
      customer: 'Tom Wilson',
      service: 'Full Service',
      time: '2:00 PM',
      status: 'scheduled',
      stylist: 'Mike Johnson',
    },
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in-progress':
        return 'warning';
      case 'scheduled':
        return 'primary';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'in-progress':
        return 'In Progress';
      case 'scheduled':
        return 'Scheduled';
      default:
        return status;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard</h1>
          <p className="text-gray-600">Welcome back, {user?.name}! Here's what's happening today.</p>
        </motion.div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            const isWarning = stat.changeType === 'warning';
            const isIncrease = stat.changeType === 'increase';

            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                className={`relative overflow-hidden ${
                  stat.alert
                    ? 'ring-2 ring-orange-500 ring-opacity-50'
                    : ''
                }`}
              >
                <Card className="h-full">
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className={`p-3 rounded-xl bg-gradient-to-r ${stat.gradient}`}>
                        <Icon className="h-6 w-6 text-white" />
                      </div>
                      {stat.alert && (
                        <ExclamationTriangleIcon className="h-5 w-5 text-orange-500" />
                      )}
                    </div>

                    <div className="mb-4">
                      <h3 className="text-2xl font-bold text-gray-900 mb-1">
                        {stat.value}
                      </h3>
                      <p className="text-sm text-gray-600">{stat.title}</p>
                    </div>

                    <div className="flex items-center">
                      {isIncrease && (
                        <ArrowUpIcon className="h-4 w-4 text-green-500 mr-1" />
                      )}
                      {stat.changeType === 'decrease' && (
                        <ArrowDownIcon className="h-4 w-4 text-red-500 mr-1" />
                      )}
                      {isWarning && (
                        <ExclamationTriangleIcon className="h-4 w-4 text-orange-500 mr-1" />
                      )}
                      <span className={`text-sm font-medium ${
                        isWarning ? 'text-orange-600' :
                        isIncrease ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {stat.change}
                      </span>
                      <span className="text-sm text-gray-500 ml-1">from yesterday</span>
                    </div>
                  </div>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Inventory Alerts - Only for Admin and Staff */}
        {(isAdmin() || isStaff()) && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="mb-8"
          >
            <Card>
              <div className="p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Inventory Alerts</h2>
                <InventoryAlerts showInDashboard={true} />
              </div>
            </Card>
          </motion.div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Appointments Section */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.6 }}
            className="lg:col-span-2"
          >
            <Card>
              <div className="p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  {isStaff() ? 'My Today\'s Appointments' : 'Today\'s Appointments'}
                </h2>
                <div className="space-y-4">
                  {todayAppointments.map((appointment, index) => (
                    <motion.div
                      key={appointment.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.7 + index * 0.1 }}
                      className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex-shrink-0">
                        <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                          <ClockIcon className="h-6 w-6 text-white" />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {appointment.customer}
                          </p>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            appointment.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                            appointment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-blue-100 text-blue-800'
                          }`}>
                            {getStatusText(appointment.status)}
                          </span>
                        </div>
                        <p className="text-sm text-gray-500">
                          {appointment.service} • {appointment.time}
                        </p>
                        <p className="text-sm text-gray-500">
                          Stylist: {appointment.stylist}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </Card>
          </motion.div>

          {/* Right Sidebar - Role-based content */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.8 }}
            className="space-y-6"
          >
            {/* Progress Card */}
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {isAdmin() ? "Today's Progress" : "My Progress"}
                </h3>

                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-gray-600">Appointments Completed</span>
                      <span className="font-medium text-gray-900">
                        {isStaff() ? '3 of 8' : '6 of 24'}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${isStaff() ? 37 : 25}%` }}
                      ></div>
                    </div>
                  </div>

                  {isAdmin() && (
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span className="text-gray-600">Revenue Target</span>
                        <span className="font-medium text-gray-900">₹1,03,750 of ₹1,66,000</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-green-500 to-emerald-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: '62%' }}
                        ></div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Card>

            {/* Quick Actions Card */}
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div className="space-y-3">
                  <Button
                    variant="outline"
                    onClick={() => navigate('/appointments')}
                    className="w-full justify-start"
                    icon={<CalendarDaysIcon className="h-4 w-4" />}
                  >
                    New Appointment
                  </Button>

                  {(isAdmin() || (isStaff() && user?.permissions?.includes('customers'))) && (
                    <Button
                      variant="outline"
                      onClick={() => navigate('/customers')}
                      className="w-full justify-start"
                      icon={<UserPlusIcon className="h-4 w-4" />}
                    >
                      Add Customer
                    </Button>
                  )}

                  <Button
                    variant="outline"
                    onClick={() => navigate('/appointments')}
                    className="w-full justify-start"
                    icon={<EyeIcon className="h-4 w-4" />}
                  >
                    View Schedule
                  </Button>
                </div>
              </div>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
