import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { AuthProvider } from './contexts/AuthContext';
import { InventoryProvider } from './contexts/InventoryContext';
import { BillingProvider } from './contexts/BillingContext';
import Navbar from './components/Navbar';
import Dashboard from './components/Dashboard';
import Appointments from './components/Appointments';
import Customers from './components/Customers';
import Services from './components/Services';
import Staff from './components/Staff';
import Reports from './components/Reports';
import Inventory from './components/Inventory';
import Billing from './components/Billing';
import BillingReports from './components/BillingReports';
import Login from './components/Login';
import UserProfile from './components/UserProfile';
import {
  StaffRoute,
  AppointmentsRoute,
  CustomersRoute,
  ServicesRoute,
  ReportsRoute,
  StaffManagementRoute
} from './components/ProtectedRoute';
import './App.css';

const theme = createTheme({
  palette: {
    primary: {
      main: '#8e24aa',
    },
    secondary: {
      main: '#ff4081',
    },
    background: {
      default: '#f5f5f5',
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <InventoryProvider>
          <BillingProvider>
            <Router>
              <div className="App">
                <Navbar />
                <main style={{ paddingTop: '100px', padding: '20px' }}>
              <Routes>
                {/* Public Routes */}
                <Route path="/login" element={<Login />} />

                {/* Protected Routes */}
                <Route
                  path="/"
                  element={
                    <StaffRoute>
                      <Dashboard />
                    </StaffRoute>
                  }
                />
                <Route
                  path="/appointments"
                  element={
                    <AppointmentsRoute>
                      <Appointments />
                    </AppointmentsRoute>
                  }
                />
                <Route
                  path="/customers"
                  element={
                    <CustomersRoute>
                      <Customers />
                    </CustomersRoute>
                  }
                />
                <Route
                  path="/services"
                  element={
                    <ServicesRoute>
                      <Services />
                    </ServicesRoute>
                  }
                />
                <Route
                  path="/staff"
                  element={
                    <StaffManagementRoute>
                      <Staff />
                    </StaffManagementRoute>
                  }
                />
                <Route
                  path="/inventory"
                  element={
                    <StaffRoute>
                      <Inventory />
                    </StaffRoute>
                  }
                />
                <Route
                  path="/billing"
                  element={
                    <StaffRoute>
                      <Billing />
                    </StaffRoute>
                  }
                />
                <Route
                  path="/billing-reports"
                  element={
                    <ReportsRoute>
                      <BillingReports />
                    </ReportsRoute>
                  }
                />
                <Route
                  path="/reports"
                  element={
                    <ReportsRoute>
                      <Reports />
                    </ReportsRoute>
                  }
                />
                <Route
                  path="/profile"
                  element={
                    <StaffRoute>
                      <UserProfile />
                    </StaffRoute>
                  }
                />

                {/* Catch all route - redirect to login */}
                <Route path="*" element={<Navigate to="/login" replace />} />
              </Routes>
            </main>
          </div>
        </Router>
          </BillingProvider>
        </InventoryProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
