import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  ScissorsIcon,
  SwatchIcon,
  FaceSmileIcon,
  SparklesIcon,
  MagnifyingGlassIcon,
  ChevronDownIcon,
  UsersIcon,
  ClockIcon,
  CurrencyDollarIcon,
  CubeIcon,
  TagIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XMarkIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import { Button, Card, Input, Modal } from './ui';
import { Disclosure } from '@headlessui/react';

const Services = () => {
  // Enhanced service data with comprehensive fields
  const [services, setServices] = useState([
    {
      id: 1,
      name: 'Hair Cut & Style',
      category: 'Hair',
      price: 85,
      duration: 60,
      description: 'Professional haircut with styling',
      popularity: 'High',
      icon: 'hair',
      status: 'active',
      requirements: ['Basic hair cutting skills', 'Styling techniques'],
      staffRequired: 1,
      assignedStaff: [1, 3], // Staff IDs who can perform this service
      createdAt: new Date('2024-01-15').toISOString(),
    },
    {
      id: 2,
      name: 'Hair Color',
      category: 'Hair',
      price: 150,
      duration: 120,
      description: 'Full hair coloring service',
      popularity: 'High',
      icon: 'color',
      status: 'active',
      requirements: ['Color theory', 'Chemical processing', 'Advanced techniques'],
      staffRequired: 1,
      assignedStaff: [1],
      createdAt: new Date('2024-01-15').toISOString(),
    },
    {
      id: 3,
      name: 'Beard Trim',
      category: 'Hair',
      price: 35,
      duration: 30,
      description: 'Professional beard trimming and shaping',
      popularity: 'Medium',
      icon: 'hair',
      status: 'active',
      requirements: ['Beard trimming', 'Precision cutting'],
      staffRequired: 1,
      assignedStaff: [2],
      createdAt: new Date('2024-01-15').toISOString(),
    },
    {
      id: 4,
      name: 'Facial Treatment',
      category: 'Skincare',
      price: 120,
      duration: 75,
      description: 'Deep cleansing facial with moisturizing',
      popularity: 'Medium',
      icon: 'face',
      status: 'active',
      requirements: ['Skincare knowledge', 'Facial techniques'],
      staffRequired: 1,
      assignedStaff: [5],
      createdAt: new Date('2024-01-15').toISOString(),
    },
    {
      id: 5,
      name: 'Manicure',
      category: 'Nails',
      price: 45,
      duration: 45,
      description: 'Complete nail care and polish',
      popularity: 'High',
      icon: 'spa',
      status: 'active',
      requirements: ['Nail care techniques', 'Polish application'],
      staffRequired: 1,
      assignedStaff: [4],
      createdAt: new Date('2024-01-15').toISOString(),
    },
    {
      id: 6,
      name: 'Pedicure',
      category: 'Nails',
      price: 55,
      duration: 60,
      description: 'Foot care and nail treatment',
      popularity: 'High',
      icon: 'spa',
      status: 'active',
      requirements: ['Foot care', 'Nail techniques'],
      staffRequired: 1,
      assignedStaff: [4],
      createdAt: new Date('2024-01-15').toISOString(),
    },
  ]);

  // Staff data for assignment
  const [staff] = useState([
    { id: 1, name: 'Emma Wilson', position: 'Senior Stylist', specialties: ['Hair Cut', 'Hair Color', 'Styling'] },
    { id: 2, name: 'John Smith', position: 'Barber', specialties: ['Men\'s Cuts', 'Beard Trim', 'Shaving'] },
    { id: 3, name: 'Mike Johnson', position: 'Hair Stylist', specialties: ['Hair Cut', 'Styling', 'Wedding Hair'] },
    { id: 4, name: 'Sarah Davis', position: 'Nail Technician', specialties: ['Manicure', 'Pedicure', 'Nail Art'] },
    { id: 5, name: 'Lisa Anderson', position: 'Esthetician', specialties: ['Facial', 'Skincare', 'Massage'] },
  ]);

  // Service packages
  const [packages, setPackages] = useState([
    {
      id: 1,
      name: 'Bridal Beauty Package',
      description: 'Complete bridal makeover package',
      services: [1, 2, 4], // Service IDs
      originalPrice: 355,
      packagePrice: 300,
      discount: 55,
      duration: 255,
      status: 'active',
    },
    {
      id: 2,
      name: 'Pamper Package',
      description: 'Relaxing spa day package',
      services: [4, 5, 6], // Service IDs
      originalPrice: 220,
      packagePrice: 180,
      discount: 40,
      duration: 180,
      status: 'active',
    },
  ]);

  // State management
  const [currentTab, setCurrentTab] = useState(0);
  const [open, setOpen] = useState(false);
  const [packageDialogOpen, setPackageDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [serviceToDelete, setServiceToDelete] = useState(null);
  const [editingService, setEditingService] = useState(null);
  const [editingPackage, setEditingPackage] = useState(null);
  const [errors, setErrors] = useState({});
  const [packageErrors, setPackageErrors] = useState({});
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  const [formData, setFormData] = useState({
    name: '',
    category: '',
    price: '',
    duration: '',
    description: '',
    popularity: 'Medium',
    icon: 'hair',
    status: 'active',
    requirements: [],
    staffRequired: 1,
    assignedStaff: [],
  });

  const [packageFormData, setPackageFormData] = useState({
    name: '',
    description: '',
    services: [],
    packagePrice: '',
    status: 'active',
  });

  const categories = ['Hair', 'Skincare', 'Nails', 'Wellness', 'Package'];
  const popularityLevels = ['Low', 'Medium', 'High'];
  const statusOptions = ['active', 'inactive'];
  const iconOptions = [
    { value: 'hair', label: 'Hair', icon: <ScissorsIcon className="h-5 w-5" /> },
    { value: 'color', label: 'Color', icon: <SwatchIcon className="h-5 w-5" /> },
    { value: 'face', label: 'Face', icon: <FaceSmileIcon className="h-5 w-5" /> },
    { value: 'spa', label: 'Spa', icon: <SparklesIcon className="h-5 w-5" /> },
  ];

  // Filtering and search functionality
  const filteredServices = services.filter(service => {
    const matchesSearch = service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || service.category === selectedCategory;
    const matchesStatus = selectedStatus === 'all' || service.status === selectedStatus;

    return matchesSearch && matchesCategory && matchesStatus;
  });

  // Calculate package pricing
  const calculatePackagePrice = (selectedServices) => {
    return selectedServices.reduce((total, serviceId) => {
      const service = services.find(s => s.id === serviceId);
      return total + (service ? service.price : 0);
    }, 0);
  };

  const calculatePackageDuration = (selectedServices) => {
    return selectedServices.reduce((total, serviceId) => {
      const service = services.find(s => s.id === serviceId);
      return total + (service ? service.duration : 0);
    }, 0);
  };



  const getPopularityColor = (popularity) => {
    switch (popularity) {
      case 'High':
        return 'success';
      case 'Medium':
        return 'warning';
      case 'Low':
        return 'default';
      default:
        return 'default';
    }
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'Hair':
        return '#8e24aa';
      case 'Skincare':
        return '#43a047';
      case 'Nails':
        return '#e91e63';
      case 'Wellness':
        return '#00acc1';
      case 'Package':
        return '#ff9800';
      default:
        return '#757575';
    }
  };

  const handleOpen = (service = null) => {
    if (service) {
      setEditingService(service);
      setFormData({
        name: service.name,
        category: service.category,
        price: service.price.toString(),
        duration: service.duration.toString(),
        description: service.description,
        popularity: service.popularity,
        icon: service.icon,
        status: service.status || 'active',
        requirements: service.requirements || [],
        staffRequired: service.staffRequired || 1,
        assignedStaff: service.assignedStaff || [],
      });
    } else {
      setEditingService(null);
      setFormData({
        name: '',
        category: '',
        price: '',
        duration: '',
        description: '',
        popularity: 'Medium',
        icon: 'hair',
        status: 'active',
        requirements: [],
        staffRequired: 1,
        assignedStaff: [],
      });
    }
    setOpen(true);
  };

  const handlePackageOpen = (pkg = null) => {
    if (pkg) {
      setEditingPackage(pkg);
      setPackageFormData({
        name: pkg.name,
        description: pkg.description,
        services: pkg.services,
        packagePrice: pkg.packagePrice.toString(),
        status: pkg.status,
      });
    } else {
      setEditingPackage(null);
      setPackageFormData({
        name: '',
        description: '',
        services: [],
        packagePrice: '',
        status: 'active',
      });
    }
    setPackageDialogOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setEditingService(null);
    setErrors({});
  };

  const handlePackageClose = () => {
    setPackageDialogOpen(false);
    setEditingPackage(null);
  };

  const handleSave = () => {
    if (!validateServiceForm()) {
      return;
    }

    const serviceData = {
      ...formData,
      name: formData.name.trim(),
      description: formData.description.trim(),
      price: parseFloat(formData.price),
      duration: parseInt(formData.duration),
      staffRequired: parseInt(formData.staffRequired),
      createdAt: editingService ? editingService.createdAt : new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    if (editingService) {
      setServices(services.map(service =>
        service.id === editingService.id
          ? { ...serviceData, id: editingService.id }
          : service
      ));
    } else {
      const newService = {
        ...serviceData,
        id: Math.max(...services.map(s => s.id)) + 1,
      };
      setServices([...services, newService]);
    }
    handleClose();
  };

  const handlePackageSave = () => {
    const originalPrice = calculatePackagePrice(packageFormData.services);
    const packageData = {
      ...packageFormData,
      packagePrice: parseFloat(packageFormData.packagePrice),
      originalPrice,
      discount: originalPrice - parseFloat(packageFormData.packagePrice),
      duration: calculatePackageDuration(packageFormData.services),
      createdAt: editingPackage ? editingPackage.createdAt : new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    if (editingPackage) {
      setPackages(packages.map(pkg =>
        pkg.id === editingPackage.id
          ? { ...packageData, id: editingPackage.id }
          : pkg
      ));
    } else {
      const newPackage = {
        ...packageData,
        id: Math.max(...packages.map(p => p.id)) + 1,
      };
      setPackages([...packages, newPackage]);
    }
    handlePackageClose();
  };

  const handleDeleteClick = (service) => {
    setServiceToDelete(service);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (serviceToDelete) {
      setServices(services.filter(service => service.id !== serviceToDelete.id));
      setDeleteDialogOpen(false);
      setServiceToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setServiceToDelete(null);
  };

  const handleInputChange = (field, value) => {
    setFormData({ ...formData, [field]: value });
    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateServiceForm = () => {
    const newErrors = {};

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Service name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Service name must be at least 2 characters';
    }

    // Category validation
    if (!formData.category) {
      newErrors.category = 'Category is required';
    }

    // Price validation
    const price = parseFloat(formData.price);
    if (!formData.price || isNaN(price)) {
      newErrors.price = 'Valid price is required';
    } else if (price <= 0) {
      newErrors.price = 'Price must be greater than 0';
    } else if (price > 10000) {
      newErrors.price = 'Price seems too high (max $10,000)';
    }

    // Duration validation
    const duration = parseInt(formData.duration);
    if (!formData.duration || isNaN(duration)) {
      newErrors.duration = 'Valid duration is required';
    } else if (duration <= 0) {
      newErrors.duration = 'Duration must be greater than 0';
    } else if (duration > 480) {
      newErrors.duration = 'Duration cannot exceed 8 hours (480 minutes)';
    }

    // Description validation (optional but if provided, should have minimum length)
    if (formData.description && formData.description.trim().length < 10) {
      newErrors.description = 'Description must be at least 10 characters if provided';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handlePackageInputChange = (field, value) => {
    setPackageFormData({ ...packageFormData, [field]: value });
  };

  // Helper functions
  const getStaffName = (staffId) => {
    const staffMember = staff.find(s => s.id === staffId);
    return staffMember ? staffMember.name : 'Unknown';
  };

  const getServiceName = (serviceId) => {
    const service = services.find(s => s.id === serviceId);
    return service ? service.name : 'Unknown';
  };

  const getStatusColor = (status) => {
    return status === 'active' ? 'success' : 'default';
  };

  // Helper functions for modern UI
  const getCategoryGradient = (category) => {
    switch (category) {
      case 'Hair':
        return 'bg-gradient-to-r from-blue-500 to-purple-600';
      case 'Facial':
        return 'bg-gradient-to-r from-pink-500 to-rose-600';
      case 'Spa':
        return 'bg-gradient-to-r from-green-500 to-emerald-600';
      case 'Nails':
        return 'bg-gradient-to-r from-purple-500 to-pink-600';
      default:
        return 'bg-gradient-to-r from-gray-500 to-gray-600';
    }
  };

  const getServiceIcon = (iconType) => {
    switch (iconType) {
      case 'hair':
        return <ScissorsIcon className="h-6 w-6 text-white" />;
      case 'color':
        return <SwatchIcon className="h-6 w-6 text-white" />;
      case 'facial':
        return <FaceSmileIcon className="h-6 w-6 text-white" />;
      case 'spa':
        return <SparklesIcon className="h-6 w-6 text-white" />;
      default:
        return <TagIcon className="h-6 w-6 text-white" />;
    }
  };

  // Enhanced statistics
  const serviceStats = {
    total: services.length,
    active: services.filter(s => s.status === 'active').length,
    avgPrice: Math.round(services.reduce((sum, service) => sum + service.price, 0) / services.length),
    highPopularity: services.filter(s => s.popularity === 'High').length,
    totalPackages: packages.length,
    avgDuration: Math.round(services.reduce((sum, service) => sum + service.duration, 0) / services.length),
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 space-y-4 sm:space-y-0"
        >
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Service Management</h1>
            <p className="text-gray-600">Manage your salon services and packages</p>
          </div>
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={() => handlePackageOpen()}
              icon={<CubeIcon className="h-4 w-4" />}
            >
              Create Package
            </Button>
            <Button
              variant="primary"
              onClick={() => handleOpen()}
              icon={<PlusIcon className="h-4 w-4" />}
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:shadow-lg"
            >
              Add Service
            </Button>
          </div>
        </motion.div>

        {/* Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setCurrentTab(0)}
              className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                currentTab === 0
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Services
            </button>
            <button
              onClick={() => setCurrentTab(1)}
              className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                currentTab === 1
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Packages
            </button>
          </div>
        </motion.div>

        {/* Search and Filters */}
        {currentTab === 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="mb-8"
          >
            <Card>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <Input
                      placeholder="Search services..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      icon={<MagnifyingGlassIcon className="h-5 w-5" />}
                      className="w-full"
                    />
                  </div>

                  <div>
                    <select
                      value={selectedCategory}
                      onChange={(e) => setSelectedCategory(e.target.value)}
                      className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200 bg-white"
                    >
                      <option value="all">All Categories</option>
                      {categories.map((category) => (
                        <option key={category} value={category}>
                          {category}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <select
                      value={selectedStatus}
                      onChange={(e) => setSelectedStatus(e.target.value)}
                      className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200 bg-white"
                    >
                      <option value="all">All Status</option>
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </select>
                  </div>

                  <div className="flex items-center justify-center">
                    <p className="text-sm text-gray-600">
                      {filteredServices.length} of {services.length} services
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Enhanced Service Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          <Card className="hover:shadow-lg transition-shadow">
            <div className="p-6">
              <div className="flex items-center">
                <div className="p-3 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-xl">
                  <UsersIcon className="h-6 w-6 text-white" />
                </div>
                <div className="ml-4">
                  <p className="text-sm text-gray-600">Total Services</p>
                  <p className="text-2xl font-bold text-gray-900">{serviceStats.total}</p>
                  <p className="text-sm text-green-600">{serviceStats.active} active</p>
                </div>
              </div>
            </div>
          </Card>
          <Card className="hover:shadow-lg transition-shadow">
            <div className="p-6">
              <div className="flex items-center">
                <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl">
                  <CurrencyDollarIcon className="h-6 w-6 text-white" />
                </div>
                <div className="ml-4">
                  <p className="text-sm text-gray-600">Average Price</p>
                  <p className="text-2xl font-bold text-green-600">${serviceStats.avgPrice}</p>
                </div>
              </div>
            </div>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <div className="p-6">
              <div className="flex items-center">
                <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl">
                  <ClockIcon className="h-6 w-6 text-white" />
                </div>
                <div className="ml-4">
                  <p className="text-sm text-gray-600">Avg. Duration</p>
                  <p className="text-2xl font-bold text-purple-600">{serviceStats.avgDuration}m</p>
                </div>
              </div>
            </div>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <div className="p-6">
              <div className="flex items-center">
                <div className="p-3 bg-gradient-to-r from-orange-500 to-red-600 rounded-xl">
                  <CubeIcon className="h-6 w-6 text-white" />
                </div>
                <div className="ml-4">
                  <p className="text-sm text-gray-600">Packages</p>
                  <p className="text-2xl font-bold text-orange-600">{serviceStats.totalPackages}</p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Services Tab Content */}
        {currentTab === 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {filteredServices.map((service, index) => (
              <motion.div
                key={service.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                className="h-full"
              >
                <Card className="h-full hover:shadow-xl transition-all duration-300">
                  <div className="p-6">
                    {/* Service Header */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center ${getCategoryGradient(service.category)}`}>
                          {getServiceIcon(service.icon)}
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">{service.name}</h3>
                          <p className="text-sm text-gray-500">{service.category}</p>
                        </div>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        service.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {service.status}
                      </span>
                    </div>

                    <p className="text-sm text-gray-600 mb-4">{service.description}</p>

                    <div className="flex justify-between items-center mb-4">
                      <div className="text-2xl font-bold text-purple-600">${service.price}</div>
                      <div className="flex items-center space-x-1 text-sm text-gray-500">
                        <ClockIcon className="h-4 w-4" />
                        <span>{service.duration} min</span>
                      </div>
                    </div>

                    {/* Staff Assignment */}
                    {service.assignedStaff && service.assignedStaff.length > 0 && (
                      <div className="mb-4">
                        <p className="text-sm font-medium text-gray-700 mb-2">Assigned Staff:</p>
                        <div className="flex flex-wrap gap-2">
                          {service.assignedStaff.map((staffId) => (
                            <span
                              key={staffId}
                              className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                            >
                              {getStaffName(staffId)}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Requirements */}
                    {service.requirements && service.requirements.length > 0 && (
                      <Disclosure>
                        {({ open }) => (
                          <>
                            <Disclosure.Button className="flex w-full justify-between rounded-lg bg-gray-100 px-4 py-2 text-left text-sm font-medium text-gray-900 hover:bg-gray-200 focus:outline-none focus-visible:ring focus-visible:ring-purple-500 focus-visible:ring-opacity-75">
                              <span>Requirements</span>
                              <ChevronDownIcon
                                className={`${
                                  open ? 'rotate-180 transform' : ''
                                } h-5 w-5 text-gray-500`}
                              />
                            </Disclosure.Button>
                            <Disclosure.Panel className="px-4 pt-4 pb-2">
                              <ul className="space-y-1">
                                {service.requirements.map((req, index) => (
                                  <li key={index} className="text-sm text-gray-600">
                                    • {req}
                                  </li>
                                ))}
                              </ul>
                            </Disclosure.Panel>
                          </>
                        )}
                      </Disclosure>
                    )}

                    <div className="mt-4 flex items-center justify-between">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        service.popularity === 'High' ? 'bg-green-100 text-green-800' :
                        service.popularity === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {service.popularity} Popularity
                      </span>

                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleOpen(service)}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteClick(service)}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Packages Tab Content */}
        {currentTab === 1 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {packages.map((pkg, index) => (
              <motion.div
                key={pkg.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                className="h-full"
              >
                <Card className="h-full hover:shadow-xl transition-all duration-300">
                  <div className="p-6">
                    {/* Package Header */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 rounded-full bg-gradient-to-r from-orange-500 to-red-600 flex items-center justify-center">
                          <CubeIcon className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">{pkg.name}</h3>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            pkg.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                          }`}>
                            {pkg.status}
                          </span>
                        </div>
                      </div>
                    </div>

                    <p className="text-sm text-gray-600 mb-4">{pkg.description}</p>

                    {/* Package Services */}
                    <div className="mb-4">
                      <p className="text-sm font-medium text-gray-700 mb-2">Included Services:</p>
                      <div className="flex flex-wrap gap-2">
                        {pkg.services.map((serviceId) => (
                          <span
                            key={serviceId}
                            className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                          >
                            {getServiceName(serviceId)}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Pricing */}
                    <div className="mb-4">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-sm text-gray-500 line-through">${pkg.originalPrice}</span>
                        <span className="text-2xl font-bold text-purple-600">${pkg.packagePrice}</span>
                        <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                          Save ${pkg.discount}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">
                        Duration: {pkg.duration} minutes
                      </p>
                    </div>

                    {/* Actions */}
                    <div className="flex justify-between items-center pt-4 border-t border-gray-200">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handlePackageOpen(pkg)}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => {
                            if (window.confirm(`Are you sure you want to delete the package "${pkg.name}"?`)) {
                              setPackages(packages.filter(p => p.id !== pkg.id));
                            }
                          }}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Enhanced Add/Edit Service Modal */}
        <Modal
          isOpen={open}
          onClose={handleClose}
          title={editingService ? 'Edit Service' : 'Add New Service'}
          size="xl"
        >
          <div className="space-y-6">
            {/* Basic Information */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
              <div className="border-b border-gray-200 mb-6"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="md:col-span-3">
                <Input
                  label="Service Name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  required
                  error={errors.name}
                  placeholder="Enter service name"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200 bg-white"
                >
                  {statusOptions.map((status) => (
                    <option key={status} value={status}>
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category *</label>
                <select
                  value={formData.category}
                  onChange={(e) => handleInputChange('category', e.target.value)}
                  className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 bg-white ${
                    errors.category ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : 'border-gray-300 focus:border-primary-500 focus:ring-primary-200'
                  } focus:ring-2`}
                >
                  <option value="">Select a category</option>
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
                {errors.category && <p className="mt-1 text-sm text-red-600">{errors.category}</p>}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Icon</label>
                <select
                  value={formData.icon}
                  onChange={(e) => handleInputChange('icon', e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200 bg-white"
                >
                  <option value="">Select an icon</option>
                  {iconOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={3}
                placeholder="Describe the service..."
                className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 resize-none ${
                  errors.description ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : 'border-gray-300 focus:border-primary-500 focus:ring-primary-200'
                } focus:ring-2`}
              />
              {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
            </div>

            {/* Pricing and Duration */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 mt-6">Pricing & Duration</h3>
              <div className="border-b border-gray-200 mb-6"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <Input
                  label="Price ($)"
                  type="number"
                  value={formData.price}
                  onChange={(e) => handleInputChange('price', e.target.value)}
                  required
                  error={errors.price}
                  placeholder="0.00"
                />
              </div>

              <div>
                <Input
                  label="Duration (minutes)"
                  type="number"
                  value={formData.duration}
                  onChange={(e) => handleInputChange('duration', e.target.value)}
                  required
                  error={errors.duration}
                  placeholder="60"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Popularity</label>
                <select
                  value={formData.popularity}
                  onChange={(e) => handleInputChange('popularity', e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200 bg-white"
                >
                  <option value="">Select popularity level</option>
                  {popularityLevels.map((level) => (
                    <option key={level} value={level}>
                      {level}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Staff Requirements */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 mt-6">Staff Requirements</h3>
              <div className="border-b border-gray-200 mb-6"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Input
                  label="Staff Required"
                  type="number"
                  value={formData.staffRequired}
                  onChange={(e) => handleInputChange('staffRequired', e.target.value)}
                  min="1"
                  placeholder="1"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Assigned Staff</label>
                <select
                  multiple
                  value={formData.assignedStaff}
                  onChange={(e) => {
                    const value = Array.from(e.target.selectedOptions, option => parseInt(option.value));
                    handleInputChange('assignedStaff', value);
                  }}
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200 bg-white"
                  size="6"
                >
                  {staff.map((staffMember) => (
                    <option key={staffMember.id} value={staffMember.id}>
                      {staffMember.name} ({staffMember.position})
                    </option>
                  ))}
                </select>
                <p className="mt-1 text-xs text-gray-500">Hold Ctrl/Cmd to select multiple staff members</p>

                {/* Selected Staff Display */}
                {formData.assignedStaff.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-2">
                    {formData.assignedStaff.map((staffId) => (
                      <span
                        key={staffId}
                        className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                      >
                        {getStaffName(staffId)}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>

            <div>
              <Input
                label="Requirements (comma-separated)"
                value={formData.requirements.join(', ')}
                onChange={(e) => handleInputChange('requirements', e.target.value.split(', ').filter(req => req.trim()))}
                placeholder="e.g., Basic hair cutting skills, Color theory"
                helperText="Enter skill requirements separated by commas"
              />
            </div>
          </div>

          <Modal.Footer>
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button variant="primary" onClick={handleSave}>
              {editingService ? 'Update Service' : 'Add Service'}
          </Button>
          </Modal.Footer>
        </Modal>

        {/* Package Creation/Edit Modal */}
        <Modal
          isOpen={packageDialogOpen}
          onClose={handlePackageClose}
          title={editingPackage ? 'Edit Package' : 'Create New Package'}
          size="xl"
        >
          <div className="space-y-6">
            <div>
              <Input
                label="Package Name"
                value={packageFormData.name}
                onChange={(e) => handlePackageInputChange('name', e.target.value)}
                required
                placeholder="Enter package name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
              <textarea
                value={packageFormData.description}
                onChange={(e) => handlePackageInputChange('description', e.target.value)}
                rows={2}
                placeholder="Describe the package..."
                className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200 resize-none"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Included Services</label>
              <select
                multiple
                value={packageFormData.services}
                onChange={(e) => {
                  const value = Array.from(e.target.selectedOptions, option => parseInt(option.value));
                  handlePackageInputChange('services', value);
                }}
                className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200 bg-white"
                size="6"
              >
                {services.map((service) => (
                  <option key={service.id} value={service.id}>
                    {service.name} - ${service.price}
                  </option>
                ))}
              </select>
              <p className="mt-1 text-xs text-gray-500">Hold Ctrl/Cmd to select multiple services</p>

              {/* Selected Services Display */}
              {packageFormData.services.length > 0 && (
                <div className="mt-2 flex flex-wrap gap-2">
                  {packageFormData.services.map((serviceId) => (
                    <span
                      key={serviceId}
                      className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                    >
                      {getServiceName(serviceId)}
                    </span>
                  ))}
                </div>
              )}
            </div>

            {packageFormData.services.length > 0 && (
              <>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <InformationCircleIcon className="h-5 w-5 text-blue-600" />
                    <div className="text-blue-800">
                      <p className="text-sm">
                        <strong>Original Price:</strong> ${calculatePackagePrice(packageFormData.services)} |
                        <strong> Total Duration:</strong> {calculatePackageDuration(packageFormData.services)} minutes
                      </p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Input
                      label="Package Price ($)"
                      type="number"
                      value={packageFormData.packagePrice}
                      onChange={(e) => handlePackageInputChange('packagePrice', e.target.value)}
                      required
                      placeholder="0.00"
                      helperText={`Savings: $${Math.max(0, calculatePackagePrice(packageFormData.services) - (parseFloat(packageFormData.packagePrice) || 0))}`}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select
                      value={packageFormData.status}
                      onChange={(e) => handlePackageInputChange('status', e.target.value)}
                      className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-primary-500 focus:ring-2 focus:ring-primary-200 transition-all duration-200 bg-white"
                    >
                      {statusOptions.map((status) => (
                        <option key={status} value={status}>
                          {status.charAt(0).toUpperCase() + status.slice(1)}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </>
            )}
          </div>

          <Modal.Footer>
            <Button variant="outline" onClick={handlePackageClose}>
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handlePackageSave}
              disabled={packageFormData.services.length === 0 || !packageFormData.packagePrice}
            >
              {editingPackage ? 'Update Package' : 'Create Package'}
            </Button>
          </Modal.Footer>
        </Modal>

        {/* Delete Confirmation Modal */}
        <Modal
          isOpen={deleteDialogOpen}
          onClose={handleDeleteCancel}
          title="Confirm Delete Service"
          size="md"
        >
          <div className="space-y-4">
            <p className="text-gray-900">
              Are you sure you want to delete the service "{serviceToDelete?.name}"?
            </p>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600" />
                <p className="text-yellow-800 text-sm">
                  This action cannot be undone. All associated appointments and data will be affected.
                </p>
              </div>
            </div>

            {serviceToDelete && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Service Details:</h4>
                <div className="space-y-2 text-sm text-gray-600">
                  <p><strong>Category:</strong> {serviceToDelete.category}</p>
                  <p><strong>Price:</strong> ${serviceToDelete.price}</p>
                  <p><strong>Duration:</strong> {serviceToDelete.duration} minutes</p>
                </div>
              </div>
            )}
          </div>

          <Modal.Footer>
            <Button variant="outline" onClick={handleDeleteCancel}>
              Cancel
            </Button>
            <Button
              variant="danger"
              onClick={handleDeleteConfirm}
              icon={<TrashIcon className="h-4 w-4" />}
            >
              Delete Service
            </Button>
          </Modal.Footer>
        </Modal>
      </div>
    </div>
  );
};

export default Services;
