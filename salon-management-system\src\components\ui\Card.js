import React from 'react';
import { motion } from 'framer-motion';
import { clsx } from 'clsx';

const Card = ({
  children,
  variant = 'default',
  padding = 'md',
  hover = true,
  className,
  ...props
}) => {
  const baseClasses = 'bg-white rounded-xl border transition-all duration-300';
  
  const variants = {
    default: 'border-gray-200 shadow-soft',
    elevated: 'border-gray-200 shadow-medium',
    glass: 'bg-white/80 backdrop-blur-sm border-white/20 shadow-soft',
    gradient: 'bg-gradient-to-br from-white to-gray-50 border-gray-200 shadow-soft',
  };

  const paddings = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-10',
  };

  const hoverEffect = hover ? 'hover:shadow-medium hover:-translate-y-1' : '';

  const cardClasses = clsx(
    baseClasses,
    variants[variant],
    paddings[padding],
    hoverEffect,
    className
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cardClasses}
      {...props}
    >
      {children}
    </motion.div>
  );
};

const CardHeader = ({ children, className, ...props }) => (
  <div className={clsx('mb-4', className)} {...props}>
    {children}
  </div>
);

const CardTitle = ({ children, className, ...props }) => (
  <h3 className={clsx('text-lg font-semibold text-gray-900', className)} {...props}>
    {children}
  </h3>
);

const CardDescription = ({ children, className, ...props }) => (
  <p className={clsx('text-sm text-gray-600 mt-1', className)} {...props}>
    {children}
  </p>
);

const CardContent = ({ children, className, ...props }) => (
  <div className={clsx('', className)} {...props}>
    {children}
  </div>
);

const CardFooter = ({ children, className, ...props }) => (
  <div className={clsx('mt-6 pt-4 border-t border-gray-200', className)} {...props}>
    {children}
  </div>
);

Card.Header = CardHeader;
Card.Title = CardTitle;
Card.Description = CardDescription;
Card.Content = CardContent;
Card.Footer = CardFooter;

export default Card;
