{"ast": null, "code": "var _jsxFileName = \"D:\\\\Project\\\\salon-management-system\\\\src\\\\components\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { ChartBarIcon, UsersIcon, CalendarDaysIcon, CurrencyDollarIcon, ClockIcon, UserPlusIcon, EyeIcon, TrendingUpIcon, ExclamationTriangleIcon, ArrowUpIcon, ArrowDownIcon, CubeIcon, ShoppingCartIcon } from '@heroicons/react/24/outline';\nimport { Card, Button } from './ui';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport { useInventory } from '../contexts/InventoryContext';\nimport InventoryAlerts from './InventoryAlerts';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  var _user$permissions;\n  const {\n    user,\n    isAdmin,\n    isStaff\n  } = useAuth();\n  const navigate = useNavigate();\n  const {\n    products,\n    getLowStockProducts,\n    getOutOfStockProducts,\n    getTotalInventoryValue\n  } = useInventory();\n\n  // Role-based stats - different data based on user role\n  const getStatsForRole = () => {\n    const lowStockCount = getLowStockProducts().length;\n    const outOfStockCount = getOutOfStockProducts().length;\n    const totalInventoryValue = getTotalInventoryValue();\n    if (isAdmin()) {\n      return [{\n        title: 'Today\\'s Revenue',\n        value: '₹1,03,750',\n        icon: CurrencyDollarIcon,\n        gradient: 'from-green-500 to-emerald-600',\n        change: '+12%',\n        changeType: 'increase'\n      }, {\n        title: 'Appointments Today',\n        value: '24',\n        icon: CalendarDaysIcon,\n        gradient: 'from-blue-500 to-cyan-600',\n        change: '+5%',\n        changeType: 'increase'\n      }, {\n        title: 'Total Customers',\n        value: '1,847',\n        icon: UsersIcon,\n        gradient: 'from-purple-500 to-pink-600',\n        change: '+8%',\n        changeType: 'increase'\n      }, {\n        title: 'Inventory Value',\n        value: `₹${totalInventoryValue.toLocaleString('en-IN')}`,\n        icon: CubeIcon,\n        gradient: 'from-orange-500 to-red-600',\n        change: lowStockCount > 0 ? `${lowStockCount} low stock` : 'All good',\n        changeType: lowStockCount > 0 ? 'warning' : 'increase',\n        alert: lowStockCount > 0 || outOfStockCount > 0\n      }];\n    } else if (isStaff()) {\n      return [{\n        title: 'My Appointments Today',\n        value: '8',\n        icon: CalendarDaysIcon,\n        gradient: 'from-blue-500 to-cyan-600',\n        change: '+2',\n        changeType: 'increase'\n      }, {\n        title: 'Completed Today',\n        value: '3',\n        icon: ClockIcon,\n        gradient: 'from-green-500 to-emerald-600',\n        change: '+1',\n        changeType: 'increase'\n      }, {\n        title: 'My Customers',\n        value: '156',\n        icon: UsersIcon,\n        gradient: 'from-purple-500 to-pink-600',\n        change: '+5',\n        changeType: 'increase'\n      }, {\n        title: 'Today\\'s Earnings',\n        value: '$320',\n        icon: CurrencyDollarIcon,\n        gradient: 'from-orange-500 to-red-600',\n        change: '+15%',\n        changeType: 'increase'\n      }];\n    }\n  };\n  const stats = getStatsForRole();\n  const todayAppointments = [{\n    id: 1,\n    customer: 'Sarah Johnson',\n    service: 'Hair Cut & Style',\n    time: '9:00 AM',\n    status: 'completed',\n    stylist: 'Emma Wilson'\n  }, {\n    id: 2,\n    customer: 'Mike Davis',\n    service: 'Beard Trim',\n    time: '10:30 AM',\n    status: 'in-progress',\n    stylist: 'John Smith'\n  }, {\n    id: 3,\n    customer: 'Lisa Brown',\n    service: 'Hair Color',\n    time: '11:00 AM',\n    status: 'scheduled',\n    stylist: 'Emma Wilson'\n  }, {\n    id: 4,\n    customer: 'Tom Wilson',\n    service: 'Full Service',\n    time: '2:00 PM',\n    status: 'scheduled',\n    stylist: 'Mike Johnson'\n  }];\n  const getStatusColor = status => {\n    switch (status) {\n      case 'completed':\n        return 'success';\n      case 'in-progress':\n        return 'warning';\n      case 'scheduled':\n        return 'primary';\n      default:\n        return 'default';\n    }\n  };\n  const getStatusText = status => {\n    switch (status) {\n      case 'completed':\n        return 'Completed';\n      case 'in-progress':\n        return 'In Progress';\n      case 'scheduled':\n        return 'Scheduled';\n      default:\n        return status;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 p-6\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-2\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.name, \"! Here's what's happening today.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n        children: stats.map((stat, index) => {\n          const Icon = stat.icon;\n          const isWarning = stat.changeType === 'warning';\n          const isIncrease = stat.changeType === 'increase';\n          return /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: index * 0.1\n            },\n            whileHover: {\n              y: -5\n            },\n            className: `relative overflow-hidden ${stat.alert ? 'ring-2 ring-orange-500 ring-opacity-50' : ''}`,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"h-full\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-3 rounded-xl bg-gradient-to-r ${stat.gradient}`,\n                    children: /*#__PURE__*/_jsxDEV(Icon, {\n                      className: \"h-6 w-6 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 214,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 23\n                  }, this), stat.alert && /*#__PURE__*/_jsxDEV(ExclamationTriangleIcon, {\n                    className: \"h-5 w-5 text-orange-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-1\",\n                    children: stat.value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: stat.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [isIncrease && /*#__PURE__*/_jsxDEV(ArrowUpIcon, {\n                    className: \"h-4 w-4 text-green-500 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 25\n                  }, this), stat.changeType === 'decrease' && /*#__PURE__*/_jsxDEV(ArrowDownIcon, {\n                    className: \"h-4 w-4 text-red-500 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 25\n                  }, this), isWarning && /*#__PURE__*/_jsxDEV(ExclamationTriangleIcon, {\n                    className: \"h-4 w-4 text-orange-500 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `text-sm font-medium ${isWarning ? 'text-orange-600' : isIncrease ? 'text-green-600' : 'text-red-600'}`,\n                    children: stat.change\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-500 ml-1\",\n                    children: \"from yesterday\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this), (isAdmin() || isStaff()) && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.5\n        },\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-4\",\n              children: \"Inventory Alerts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(InventoryAlerts, {\n              showInDashboard: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            delay: 0.6\n          },\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900 mb-6\",\n                children: isStaff() ? 'My Today\\'s Appointments' : 'Today\\'s Appointments'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: todayAppointments.map((appointment, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 10\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.7 + index * 0.1\n                  },\n                  className: \"flex items-center space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n                      children: /*#__PURE__*/_jsxDEV(ClockIcon, {\n                        className: \"h-6 w-6 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 294,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 293,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2 mb-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-gray-900 truncate\",\n                        children: appointment.customer\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 299,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${appointment.status === 'confirmed' ? 'bg-green-100 text-green-800' : appointment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800'}`,\n                        children: getStatusText(appointment.status)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 302,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 298,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: [appointment.service, \" \\u2022 \", appointment.time]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 310,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: [\"Stylist: \", appointment.stylist]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 313,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 23\n                  }, this)]\n                }, appointment.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            delay: 0.8\n          },\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                children: isAdmin() ? \"Today's Progress\" : \"My Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between text-sm mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: \"Appointments Completed\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 341,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium text-gray-900\",\n                      children: isStaff() ? '3 of 8' : '6 of 24'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full bg-gray-200 rounded-full h-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300\",\n                      style: {\n                        width: `${isStaff() ? 37 : 25}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 347,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 346,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 19\n                }, this), isAdmin() && /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between text-sm mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: \"Revenue Target\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 357,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium text-gray-900\",\n                      children: \"$1,250 of $2,000\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 358,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full bg-gray-200 rounded-full h-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gradient-to-r from-green-500 to-emerald-600 h-2 rounded-full transition-all duration-300\",\n                      style: {\n                        width: '62%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 361,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                children: \"Quick Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline\",\n                  onClick: () => navigate('/appointments'),\n                  className: \"w-full justify-start\",\n                  icon: /*#__PURE__*/_jsxDEV(CalendarDaysIcon, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 27\n                  }, this),\n                  children: \"New Appointment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 19\n                }, this), (isAdmin() || isStaff() && (user === null || user === void 0 ? void 0 : (_user$permissions = user.permissions) === null || _user$permissions === void 0 ? void 0 : _user$permissions.includes('customers'))) && /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline\",\n                  onClick: () => navigate('/customers'),\n                  className: \"w-full justify-start\",\n                  icon: /*#__PURE__*/_jsxDEV(UserPlusIcon, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 29\n                  }, this),\n                  children: \"Add Customer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline\",\n                  onClick: () => navigate('/appointments'),\n                  className: \"w-full justify-start\",\n                  icon: /*#__PURE__*/_jsxDEV(EyeIcon, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 27\n                  }, this),\n                  children: \"View Schedule\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 178,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"tU31OjwCr9DAey4TjJlUjcpJuGA=\", false, function () {\n  return [useAuth, useNavigate, useInventory];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "motion", "ChartBarIcon", "UsersIcon", "CalendarDaysIcon", "CurrencyDollarIcon", "ClockIcon", "UserPlusIcon", "EyeIcon", "TrendingUpIcon", "ExclamationTriangleIcon", "ArrowUpIcon", "ArrowDownIcon", "CubeIcon", "ShoppingCartIcon", "Card", "<PERSON><PERSON>", "useAuth", "useNavigate", "useInventory", "InventoryAlerts", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "_user$permissions", "user", "isAdmin", "isStaff", "navigate", "products", "getLowStockProducts", "getOutOfStockProducts", "getTotalInventoryValue", "getStatsForRole", "lowStockCount", "length", "outOfStockCount", "totalInventoryValue", "title", "value", "icon", "gradient", "change", "changeType", "toLocaleString", "alert", "stats", "todayAppointments", "id", "customer", "service", "time", "status", "stylist", "getStatusColor", "getStatusText", "className", "children", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "map", "stat", "index", "Icon", "isWarning", "isIncrease", "transition", "delay", "whileHover", "showInDashboard", "x", "appointment", "style", "width", "variant", "onClick", "permissions", "includes", "_c", "$RefreshReg$"], "sources": ["D:/Project/salon-management-system/src/components/Dashboard.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  ChartBarIcon,\n  UsersIcon,\n  CalendarDaysIcon,\n  CurrencyDollarIcon,\n  ClockIcon,\n  UserPlusIcon,\n  EyeIcon,\n  TrendingUpIcon,\n  ExclamationTriangleIcon,\n  ArrowUpIcon,\n  ArrowDownIcon,\n  CubeIcon,\n  ShoppingCartIcon\n} from '@heroicons/react/24/outline';\nimport { Card, Button } from './ui';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport { useInventory } from '../contexts/InventoryContext';\nimport InventoryAlerts from './InventoryAlerts';\n\nconst Dashboard = () => {\n  const { user, isAdmin, isStaff } = useAuth();\n  const navigate = useNavigate();\n  const {\n    products,\n    getLowStockProducts,\n    getOutOfStockProducts,\n    getTotalInventoryValue\n  } = useInventory();\n\n  // Role-based stats - different data based on user role\n  const getStatsForRole = () => {\n    const lowStockCount = getLowStockProducts().length;\n    const outOfStockCount = getOutOfStockProducts().length;\n    const totalInventoryValue = getTotalInventoryValue();\n\n    if (isAdmin()) {\n      return [\n        {\n          title: 'Today\\'s Revenue',\n          value: '₹1,03,750',\n          icon: CurrencyDollarIcon,\n          gradient: 'from-green-500 to-emerald-600',\n          change: '+12%',\n          changeType: 'increase',\n        },\n        {\n          title: 'Appointments Today',\n          value: '24',\n          icon: CalendarDaysIcon,\n          gradient: 'from-blue-500 to-cyan-600',\n          change: '+5%',\n          changeType: 'increase',\n        },\n        {\n          title: 'Total Customers',\n          value: '1,847',\n          icon: UsersIcon,\n          gradient: 'from-purple-500 to-pink-600',\n          change: '+8%',\n          changeType: 'increase',\n        },\n        {\n          title: 'Inventory Value',\n          value: `₹${totalInventoryValue.toLocaleString('en-IN')}`,\n          icon: CubeIcon,\n          gradient: 'from-orange-500 to-red-600',\n          change: lowStockCount > 0 ? `${lowStockCount} low stock` : 'All good',\n          changeType: lowStockCount > 0 ? 'warning' : 'increase',\n          alert: lowStockCount > 0 || outOfStockCount > 0\n        },\n      ];\n    } else if (isStaff()) {\n      return [\n        {\n          title: 'My Appointments Today',\n          value: '8',\n          icon: CalendarDaysIcon,\n          gradient: 'from-blue-500 to-cyan-600',\n          change: '+2',\n          changeType: 'increase',\n        },\n        {\n          title: 'Completed Today',\n          value: '3',\n          icon: ClockIcon,\n          gradient: 'from-green-500 to-emerald-600',\n          change: '+1',\n          changeType: 'increase',\n        },\n        {\n          title: 'My Customers',\n          value: '156',\n          icon: UsersIcon,\n          gradient: 'from-purple-500 to-pink-600',\n          change: '+5',\n          changeType: 'increase',\n        },\n        {\n          title: 'Today\\'s Earnings',\n          value: '$320',\n          icon: CurrencyDollarIcon,\n          gradient: 'from-orange-500 to-red-600',\n          change: '+15%',\n          changeType: 'increase',\n        },\n      ];\n    }\n  };\n\n  const stats = getStatsForRole();\n\n  const todayAppointments = [\n    {\n      id: 1,\n      customer: 'Sarah Johnson',\n      service: 'Hair Cut & Style',\n      time: '9:00 AM',\n      status: 'completed',\n      stylist: 'Emma Wilson',\n    },\n    {\n      id: 2,\n      customer: 'Mike Davis',\n      service: 'Beard Trim',\n      time: '10:30 AM',\n      status: 'in-progress',\n      stylist: 'John Smith',\n    },\n    {\n      id: 3,\n      customer: 'Lisa Brown',\n      service: 'Hair Color',\n      time: '11:00 AM',\n      status: 'scheduled',\n      stylist: 'Emma Wilson',\n    },\n    {\n      id: 4,\n      customer: 'Tom Wilson',\n      service: 'Full Service',\n      time: '2:00 PM',\n      status: 'scheduled',\n      stylist: 'Mike Johnson',\n    },\n  ];\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'completed':\n        return 'success';\n      case 'in-progress':\n        return 'warning';\n      case 'scheduled':\n        return 'primary';\n      default:\n        return 'default';\n    }\n  };\n\n  const getStatusText = (status) => {\n    switch (status) {\n      case 'completed':\n        return 'Completed';\n      case 'in-progress':\n        return 'In Progress';\n      case 'scheduled':\n        return 'Scheduled';\n      default:\n        return status;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 p-6\">\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"mb-8\"\n        >\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Dashboard</h1>\n          <p className=\"text-gray-600\">Welcome back, {user?.name}! Here's what's happening today.</p>\n        </motion.div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          {stats.map((stat, index) => {\n            const Icon = stat.icon;\n            const isWarning = stat.changeType === 'warning';\n            const isIncrease = stat.changeType === 'increase';\n\n            return (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: index * 0.1 }}\n                whileHover={{ y: -5 }}\n                className={`relative overflow-hidden ${\n                  stat.alert\n                    ? 'ring-2 ring-orange-500 ring-opacity-50'\n                    : ''\n                }`}\n              >\n                <Card className=\"h-full\">\n                  <div className=\"p-6\">\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <div className={`p-3 rounded-xl bg-gradient-to-r ${stat.gradient}`}>\n                        <Icon className=\"h-6 w-6 text-white\" />\n                      </div>\n                      {stat.alert && (\n                        <ExclamationTriangleIcon className=\"h-5 w-5 text-orange-500\" />\n                      )}\n                    </div>\n\n                    <div className=\"mb-4\">\n                      <h3 className=\"text-2xl font-bold text-gray-900 mb-1\">\n                        {stat.value}\n                      </h3>\n                      <p className=\"text-sm text-gray-600\">{stat.title}</p>\n                    </div>\n\n                    <div className=\"flex items-center\">\n                      {isIncrease && (\n                        <ArrowUpIcon className=\"h-4 w-4 text-green-500 mr-1\" />\n                      )}\n                      {stat.changeType === 'decrease' && (\n                        <ArrowDownIcon className=\"h-4 w-4 text-red-500 mr-1\" />\n                      )}\n                      {isWarning && (\n                        <ExclamationTriangleIcon className=\"h-4 w-4 text-orange-500 mr-1\" />\n                      )}\n                      <span className={`text-sm font-medium ${\n                        isWarning ? 'text-orange-600' :\n                        isIncrease ? 'text-green-600' : 'text-red-600'\n                      }`}>\n                        {stat.change}\n                      </span>\n                      <span className=\"text-sm text-gray-500 ml-1\">from yesterday</span>\n                    </div>\n                  </div>\n                </Card>\n              </motion.div>\n            );\n          })}\n        </div>\n\n        {/* Inventory Alerts - Only for Admin and Staff */}\n        {(isAdmin() || isStaff()) && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.5 }}\n            className=\"mb-8\"\n          >\n            <Card>\n              <div className=\"p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Inventory Alerts</h2>\n                <InventoryAlerts showInDashboard={true} />\n              </div>\n            </Card>\n          </motion.div>\n        )}\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          {/* Appointments Section */}\n          <motion.div\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ delay: 0.6 }}\n            className=\"lg:col-span-2\"\n          >\n            <Card>\n              <div className=\"p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n                  {isStaff() ? 'My Today\\'s Appointments' : 'Today\\'s Appointments'}\n                </h2>\n                <div className=\"space-y-4\">\n                  {todayAppointments.map((appointment, index) => (\n                    <motion.div\n                      key={appointment.id}\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ delay: 0.7 + index * 0.1 }}\n                      className=\"flex items-center space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\"\n                    >\n                      <div className=\"flex-shrink-0\">\n                        <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\n                          <ClockIcon className=\"h-6 w-6 text-white\" />\n                        </div>\n                      </div>\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-center space-x-2 mb-1\">\n                          <p className=\"text-sm font-medium text-gray-900 truncate\">\n                            {appointment.customer}\n                          </p>\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                            appointment.status === 'confirmed' ? 'bg-green-100 text-green-800' :\n                            appointment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :\n                            'bg-blue-100 text-blue-800'\n                          }`}>\n                            {getStatusText(appointment.status)}\n                          </span>\n                        </div>\n                        <p className=\"text-sm text-gray-500\">\n                          {appointment.service} • {appointment.time}\n                        </p>\n                        <p className=\"text-sm text-gray-500\">\n                          Stylist: {appointment.stylist}\n                        </p>\n                      </div>\n                    </motion.div>\n                  ))}\n                </div>\n              </div>\n            </Card>\n          </motion.div>\n\n          {/* Right Sidebar - Role-based content */}\n          <motion.div\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ delay: 0.8 }}\n            className=\"space-y-6\"\n          >\n            {/* Progress Card */}\n            <Card>\n              <div className=\"p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                  {isAdmin() ? \"Today's Progress\" : \"My Progress\"}\n                </h3>\n\n                <div className=\"space-y-4\">\n                  <div>\n                    <div className=\"flex justify-between text-sm mb-2\">\n                      <span className=\"text-gray-600\">Appointments Completed</span>\n                      <span className=\"font-medium text-gray-900\">\n                        {isStaff() ? '3 of 8' : '6 of 24'}\n                      </span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div\n                        className=\"bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300\"\n                        style={{ width: `${isStaff() ? 37 : 25}%` }}\n                      ></div>\n                    </div>\n                  </div>\n\n                  {isAdmin() && (\n                    <div>\n                      <div className=\"flex justify-between text-sm mb-2\">\n                        <span className=\"text-gray-600\">Revenue Target</span>\n                        <span className=\"font-medium text-gray-900\">$1,250 of $2,000</span>\n                      </div>\n                      <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                        <div\n                          className=\"bg-gradient-to-r from-green-500 to-emerald-600 h-2 rounded-full transition-all duration-300\"\n                          style={{ width: '62%' }}\n                        ></div>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </Card>\n\n            {/* Quick Actions Card */}\n            <Card>\n              <div className=\"p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Quick Actions</h3>\n                <div className=\"space-y-3\">\n                  <Button\n                    variant=\"outline\"\n                    onClick={() => navigate('/appointments')}\n                    className=\"w-full justify-start\"\n                    icon={<CalendarDaysIcon className=\"h-4 w-4\" />}\n                  >\n                    New Appointment\n                  </Button>\n\n                  {(isAdmin() || (isStaff() && user?.permissions?.includes('customers'))) && (\n                    <Button\n                      variant=\"outline\"\n                      onClick={() => navigate('/customers')}\n                      className=\"w-full justify-start\"\n                      icon={<UserPlusIcon className=\"h-4 w-4\" />}\n                    >\n                      Add Customer\n                    </Button>\n                  )}\n\n                  <Button\n                    variant=\"outline\"\n                    onClick={() => navigate('/appointments')}\n                    className=\"w-full justify-start\"\n                    icon={<EyeIcon className=\"h-4 w-4\" />}\n                  >\n                    View Schedule\n                  </Button>\n                </div>\n              </div>\n            </Card>\n          </motion.div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,YAAY,EACZC,SAAS,EACTC,gBAAgB,EAChBC,kBAAkB,EAClBC,SAAS,EACTC,YAAY,EACZC,OAAO,EACPC,cAAc,EACdC,uBAAuB,EACvBC,WAAW,EACXC,aAAa,EACbC,QAAQ,EACRC,gBAAgB,QACX,6BAA6B;AACpC,SAASC,IAAI,EAAEC,MAAM,QAAQ,MAAM;AACnC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,iBAAA;EACtB,MAAM;IAAEC,IAAI;IAAEC,OAAO;IAAEC;EAAQ,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC5C,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IACJY,QAAQ;IACRC,mBAAmB;IACnBC,qBAAqB;IACrBC;EACF,CAAC,GAAGd,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMe,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,aAAa,GAAGJ,mBAAmB,CAAC,CAAC,CAACK,MAAM;IAClD,MAAMC,eAAe,GAAGL,qBAAqB,CAAC,CAAC,CAACI,MAAM;IACtD,MAAME,mBAAmB,GAAGL,sBAAsB,CAAC,CAAC;IAEpD,IAAIN,OAAO,CAAC,CAAC,EAAE;MACb,OAAO,CACL;QACEY,KAAK,EAAE,kBAAkB;QACzBC,KAAK,EAAE,WAAW;QAClBC,IAAI,EAAEpC,kBAAkB;QACxBqC,QAAQ,EAAE,+BAA+B;QACzCC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE;MACd,CAAC,EACD;QACEL,KAAK,EAAE,oBAAoB;QAC3BC,KAAK,EAAE,IAAI;QACXC,IAAI,EAAErC,gBAAgB;QACtBsC,QAAQ,EAAE,2BAA2B;QACrCC,MAAM,EAAE,KAAK;QACbC,UAAU,EAAE;MACd,CAAC,EACD;QACEL,KAAK,EAAE,iBAAiB;QACxBC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEtC,SAAS;QACfuC,QAAQ,EAAE,6BAA6B;QACvCC,MAAM,EAAE,KAAK;QACbC,UAAU,EAAE;MACd,CAAC,EACD;QACEL,KAAK,EAAE,iBAAiB;QACxBC,KAAK,EAAE,IAAIF,mBAAmB,CAACO,cAAc,CAAC,OAAO,CAAC,EAAE;QACxDJ,IAAI,EAAE5B,QAAQ;QACd6B,QAAQ,EAAE,4BAA4B;QACtCC,MAAM,EAAER,aAAa,GAAG,CAAC,GAAG,GAAGA,aAAa,YAAY,GAAG,UAAU;QACrES,UAAU,EAAET,aAAa,GAAG,CAAC,GAAG,SAAS,GAAG,UAAU;QACtDW,KAAK,EAAEX,aAAa,GAAG,CAAC,IAAIE,eAAe,GAAG;MAChD,CAAC,CACF;IACH,CAAC,MAAM,IAAIT,OAAO,CAAC,CAAC,EAAE;MACpB,OAAO,CACL;QACEW,KAAK,EAAE,uBAAuB;QAC9BC,KAAK,EAAE,GAAG;QACVC,IAAI,EAAErC,gBAAgB;QACtBsC,QAAQ,EAAE,2BAA2B;QACrCC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE;MACd,CAAC,EACD;QACEL,KAAK,EAAE,iBAAiB;QACxBC,KAAK,EAAE,GAAG;QACVC,IAAI,EAAEnC,SAAS;QACfoC,QAAQ,EAAE,+BAA+B;QACzCC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE;MACd,CAAC,EACD;QACEL,KAAK,EAAE,cAAc;QACrBC,KAAK,EAAE,KAAK;QACZC,IAAI,EAAEtC,SAAS;QACfuC,QAAQ,EAAE,6BAA6B;QACvCC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE;MACd,CAAC,EACD;QACEL,KAAK,EAAE,mBAAmB;QAC1BC,KAAK,EAAE,MAAM;QACbC,IAAI,EAAEpC,kBAAkB;QACxBqC,QAAQ,EAAE,4BAA4B;QACtCC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE;MACd,CAAC,CACF;IACH;EACF,CAAC;EAED,MAAMG,KAAK,GAAGb,eAAe,CAAC,CAAC;EAE/B,MAAMc,iBAAiB,GAAG,CACxB;IACEC,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,eAAe;IACzBC,OAAO,EAAE,kBAAkB;IAC3BC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,WAAW;IACnBC,OAAO,EAAE;EACX,CAAC,EACD;IACEL,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,YAAY;IACtBC,OAAO,EAAE,YAAY;IACrBC,IAAI,EAAE,UAAU;IAChBC,MAAM,EAAE,aAAa;IACrBC,OAAO,EAAE;EACX,CAAC,EACD;IACEL,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,YAAY;IACtBC,OAAO,EAAE,YAAY;IACrBC,IAAI,EAAE,UAAU;IAChBC,MAAM,EAAE,WAAW;IACnBC,OAAO,EAAE;EACX,CAAC,EACD;IACEL,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,YAAY;IACtBC,OAAO,EAAE,cAAc;IACvBC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,WAAW;IACnBC,OAAO,EAAE;EACX,CAAC,CACF;EAED,MAAMC,cAAc,GAAIF,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,aAAa;QAChB,OAAO,SAAS;MAClB,KAAK,WAAW;QACd,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMG,aAAa,GAAIH,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,WAAW;MACpB,KAAK,aAAa;QAChB,OAAO,aAAa;MACtB,KAAK,WAAW;QACd,OAAO,WAAW;MACpB;QACE,OAAOA,MAAM;IACjB;EACF,CAAC;EAED,oBACE/B,OAAA;IAAKmC,SAAS,EAAC,6BAA6B;IAAAC,QAAA,eAC1CpC,OAAA;MAAKmC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAEhCpC,OAAA,CAACrB,MAAM,CAAC0D,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BL,SAAS,EAAC,MAAM;QAAAC,QAAA,gBAEhBpC,OAAA;UAAImC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAS;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpE7C,OAAA;UAAGmC,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAC,gBAAc,EAAChC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,IAAI,EAAC,kCAAgC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjF,CAAC,eAGb7C,OAAA;QAAKmC,SAAS,EAAC,2DAA2D;QAAAC,QAAA,EACvEX,KAAK,CAACsB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;UAC1B,MAAMC,IAAI,GAAGF,IAAI,CAAC7B,IAAI;UACtB,MAAMgC,SAAS,GAAGH,IAAI,CAAC1B,UAAU,KAAK,SAAS;UAC/C,MAAM8B,UAAU,GAAGJ,IAAI,CAAC1B,UAAU,KAAK,UAAU;UAEjD,oBACEtB,OAAA,CAACrB,MAAM,CAAC0D,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9Ba,UAAU,EAAE;cAAEC,KAAK,EAAEL,KAAK,GAAG;YAAI,CAAE;YACnCM,UAAU,EAAE;cAAEf,CAAC,EAAE,CAAC;YAAE,CAAE;YACtBL,SAAS,EAAE,4BACTa,IAAI,CAACxB,KAAK,GACN,wCAAwC,GACxC,EAAE,EACL;YAAAY,QAAA,eAEHpC,OAAA,CAACP,IAAI;cAAC0C,SAAS,EAAC,QAAQ;cAAAC,QAAA,eACtBpC,OAAA;gBAAKmC,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBpC,OAAA;kBAAKmC,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrDpC,OAAA;oBAAKmC,SAAS,EAAE,mCAAmCa,IAAI,CAAC5B,QAAQ,EAAG;oBAAAgB,QAAA,eACjEpC,OAAA,CAACkD,IAAI;sBAACf,SAAS,EAAC;oBAAoB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC,EACLG,IAAI,CAACxB,KAAK,iBACTxB,OAAA,CAACZ,uBAAuB;oBAAC+C,SAAS,EAAC;kBAAyB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAC/D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEN7C,OAAA;kBAAKmC,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBpC,OAAA;oBAAImC,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EAClDY,IAAI,CAAC9B;kBAAK;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACL7C,OAAA;oBAAGmC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEY,IAAI,CAAC/B;kBAAK;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eAEN7C,OAAA;kBAAKmC,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,GAC/BgB,UAAU,iBACTpD,OAAA,CAACX,WAAW;oBAAC8C,SAAS,EAAC;kBAA6B;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CACvD,EACAG,IAAI,CAAC1B,UAAU,KAAK,UAAU,iBAC7BtB,OAAA,CAACV,aAAa;oBAAC6C,SAAS,EAAC;kBAA2B;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CACvD,EACAM,SAAS,iBACRnD,OAAA,CAACZ,uBAAuB;oBAAC+C,SAAS,EAAC;kBAA8B;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CACpE,eACD7C,OAAA;oBAAMmC,SAAS,EAAE,uBACfgB,SAAS,GAAG,iBAAiB,GAC7BC,UAAU,GAAG,gBAAgB,GAAG,cAAc,EAC7C;oBAAAhB,QAAA,EACAY,IAAI,CAAC3B;kBAAM;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eACP7C,OAAA;oBAAMmC,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAc;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC,GAhDFI,KAAK;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiDA,CAAC;QAEjB,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGL,CAACxC,OAAO,CAAC,CAAC,IAAIC,OAAO,CAAC,CAAC,kBACtBN,OAAA,CAACrB,MAAM,CAAC0D,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9Ba,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BnB,SAAS,EAAC,MAAM;QAAAC,QAAA,eAEhBpC,OAAA,CAACP,IAAI;UAAA2C,QAAA,eACHpC,OAAA;YAAKmC,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBpC,OAAA;cAAImC,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAgB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9E7C,OAAA,CAACF,eAAe;cAAC0D,eAAe,EAAE;YAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CACb,eAED7C,OAAA;QAAKmC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDpC,OAAA,CAACrB,MAAM,CAAC0D,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEkB,CAAC,EAAE,CAAC;UAAG,CAAE;UAChChB,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEkB,CAAC,EAAE;UAAE,CAAE;UAC9BJ,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC3BnB,SAAS,EAAC,eAAe;UAAAC,QAAA,eAEzBpC,OAAA,CAACP,IAAI;YAAA2C,QAAA,eACHpC,OAAA;cAAKmC,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBpC,OAAA;gBAAImC,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EACrD9B,OAAO,CAAC,CAAC,GAAG,0BAA0B,GAAG;cAAuB;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACL7C,OAAA;gBAAKmC,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvBV,iBAAiB,CAACqB,GAAG,CAAC,CAACW,WAAW,EAAET,KAAK,kBACxCjD,OAAA,CAACrB,MAAM,CAAC0D,GAAG;kBAETC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAG,CAAE;kBAC/BC,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAC9Ba,UAAU,EAAE;oBAAEC,KAAK,EAAE,GAAG,GAAGL,KAAK,GAAG;kBAAI,CAAE;kBACzCd,SAAS,EAAC,2FAA2F;kBAAAC,QAAA,gBAErGpC,OAAA;oBAAKmC,SAAS,EAAC,eAAe;oBAAAC,QAAA,eAC5BpC,OAAA;sBAAKmC,SAAS,EAAC,sGAAsG;sBAAAC,QAAA,eACnHpC,OAAA,CAAChB,SAAS;wBAACmD,SAAS,EAAC;sBAAoB;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN7C,OAAA;oBAAKmC,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7BpC,OAAA;sBAAKmC,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC/CpC,OAAA;wBAAGmC,SAAS,EAAC,4CAA4C;wBAAAC,QAAA,EACtDsB,WAAW,CAAC9B;sBAAQ;wBAAAc,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC,eACJ7C,OAAA;wBAAMmC,SAAS,EAAE,2EACfuB,WAAW,CAAC3B,MAAM,KAAK,WAAW,GAAG,6BAA6B,GAClE2B,WAAW,CAAC3B,MAAM,KAAK,SAAS,GAAG,+BAA+B,GAClE,2BAA2B,EAC1B;wBAAAK,QAAA,EACAF,aAAa,CAACwB,WAAW,CAAC3B,MAAM;sBAAC;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACN7C,OAAA;sBAAGmC,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GACjCsB,WAAW,CAAC7B,OAAO,EAAC,UAAG,EAAC6B,WAAW,CAAC5B,IAAI;oBAAA;sBAAAY,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC,eACJ7C,OAAA;sBAAGmC,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,WAC1B,EAACsB,WAAW,CAAC1B,OAAO;oBAAA;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA,GA9BDa,WAAW,CAAC/B,EAAE;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA+BT,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAGb7C,OAAA,CAACrB,MAAM,CAAC0D,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEkB,CAAC,EAAE;UAAG,CAAE;UAC/BhB,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEkB,CAAC,EAAE;UAAE,CAAE;UAC9BJ,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC3BnB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAGrBpC,OAAA,CAACP,IAAI;YAAA2C,QAAA,eACHpC,OAAA;cAAKmC,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBpC,OAAA;gBAAImC,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EACrD/B,OAAO,CAAC,CAAC,GAAG,kBAAkB,GAAG;cAAa;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eAEL7C,OAAA;gBAAKmC,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBpC,OAAA;kBAAAoC,QAAA,gBACEpC,OAAA;oBAAKmC,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChDpC,OAAA;sBAAMmC,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAsB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC7D7C,OAAA;sBAAMmC,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EACxC9B,OAAO,CAAC,CAAC,GAAG,QAAQ,GAAG;oBAAS;sBAAAoC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN7C,OAAA;oBAAKmC,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,eAClDpC,OAAA;sBACEmC,SAAS,EAAC,2FAA2F;sBACrGwB,KAAK,EAAE;wBAAEC,KAAK,EAAE,GAAGtD,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;sBAAI;oBAAE;sBAAAoC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAELxC,OAAO,CAAC,CAAC,iBACRL,OAAA;kBAAAoC,QAAA,gBACEpC,OAAA;oBAAKmC,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,gBAChDpC,OAAA;sBAAMmC,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAc;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACrD7C,OAAA;sBAAMmC,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC,eACN7C,OAAA;oBAAKmC,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,eAClDpC,OAAA;sBACEmC,SAAS,EAAC,6FAA6F;sBACvGwB,KAAK,EAAE;wBAAEC,KAAK,EAAE;sBAAM;oBAAE;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGP7C,OAAA,CAACP,IAAI;YAAA2C,QAAA,eACHpC,OAAA;cAAKmC,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBpC,OAAA;gBAAImC,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3E7C,OAAA;gBAAKmC,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBpC,OAAA,CAACN,MAAM;kBACLmE,OAAO,EAAC,SAAS;kBACjBC,OAAO,EAAEA,CAAA,KAAMvD,QAAQ,CAAC,eAAe,CAAE;kBACzC4B,SAAS,EAAC,sBAAsB;kBAChChB,IAAI,eAAEnB,OAAA,CAAClB,gBAAgB;oBAACqD,SAAS,EAAC;kBAAS;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAT,QAAA,EAChD;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAER,CAACxC,OAAO,CAAC,CAAC,IAAKC,OAAO,CAAC,CAAC,KAAIF,IAAI,aAAJA,IAAI,wBAAAD,iBAAA,GAAJC,IAAI,CAAE2D,WAAW,cAAA5D,iBAAA,uBAAjBA,iBAAA,CAAmB6D,QAAQ,CAAC,WAAW,CAAC,CAAC,kBACpEhE,OAAA,CAACN,MAAM;kBACLmE,OAAO,EAAC,SAAS;kBACjBC,OAAO,EAAEA,CAAA,KAAMvD,QAAQ,CAAC,YAAY,CAAE;kBACtC4B,SAAS,EAAC,sBAAsB;kBAChChB,IAAI,eAAEnB,OAAA,CAACf,YAAY;oBAACkD,SAAS,EAAC;kBAAS;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAT,QAAA,EAC5C;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT,eAED7C,OAAA,CAACN,MAAM;kBACLmE,OAAO,EAAC,SAAS;kBACjBC,OAAO,EAAEA,CAAA,KAAMvD,QAAQ,CAAC,eAAe,CAAE;kBACzC4B,SAAS,EAAC,sBAAsB;kBAChChB,IAAI,eAAEnB,OAAA,CAACd,OAAO;oBAACiD,SAAS,EAAC;kBAAS;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAT,QAAA,EACvC;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3C,EAAA,CArYID,SAAS;EAAA,QACsBN,OAAO,EACzBC,WAAW,EAMxBC,YAAY;AAAA;AAAAoE,EAAA,GARZhE,SAAS;AAuYf,eAAeA,SAAS;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}