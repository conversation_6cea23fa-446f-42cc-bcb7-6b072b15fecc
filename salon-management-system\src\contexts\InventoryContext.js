import React, { createContext, useContext, useState, useEffect } from 'react';

const InventoryContext = createContext();

export const useInventory = () => {
  const context = useContext(InventoryContext);
  if (!context) {
    throw new Error('useInventory must be used within an InventoryProvider');
  }
  return context;
};

export const InventoryProvider = ({ children }) => {
  const [products, setProducts] = useState([
    {
      id: 1,
      name: 'Professional Hair Shampoo',
      category: 'Hair Care',
      brand: 'SalonPro',
      sku: 'SP-SH-001',
      currentStock: 15,
      minStockLevel: 10,
      maxStockLevel: 50,
      unitPrice: 2074,
      supplier: 'Beauty Supply Co.',
      description: 'Premium sulfate-free shampoo for all hair types',
      expiryDate: '2025-12-31',
      lastRestocked: '2024-01-15',
      status: 'active',
      location: 'Storage Room A - Shelf 1',
      barcode: '123456789012',
      usageRate: 2.5, // units per week
      createdAt: '2024-01-01',
      updatedAt: '2024-01-15'
    },
    {
      id: 2,
      name: 'Hair Conditioner',
      category: 'Hair Care',
      brand: 'SalonPro',
      sku: 'SP-CD-001',
      currentStock: 8,
      minStockLevel: 10,
      maxStockLevel: 40,
      unitPrice: 2240,
      supplier: 'Beauty Supply Co.',
      description: 'Deep moisturizing conditioner',
      expiryDate: '2025-11-30',
      lastRestocked: '2024-01-10',
      status: 'active',
      location: 'Storage Room A - Shelf 1',
      barcode: '123456789013',
      usageRate: 2.0,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-10'
    },
    {
      id: 3,
      name: 'Hair Color - Blonde',
      category: 'Hair Color',
      brand: 'ColorMaster',
      sku: 'CM-BL-001',
      currentStock: 5,
      minStockLevel: 8,
      maxStockLevel: 25,
      unitPrice: 3817,
      supplier: 'Professional Color Inc.',
      description: 'Professional blonde hair color',
      expiryDate: '2024-08-15', // Expiring soon
      lastRestocked: '2024-01-05',
      status: 'active',
      location: 'Storage Room B - Color Cabinet',
      barcode: '123456789014',
      usageRate: 1.5,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-05'
    },
    {
      id: 4,
      name: 'Nail Polish - Red',
      category: 'Nail Care',
      brand: 'NailArt Pro',
      sku: 'NAP-RD-001',
      currentStock: 12,
      minStockLevel: 5,
      maxStockLevel: 30,
      unitPrice: 1576,
      supplier: 'Nail Supplies Ltd.',
      description: 'Long-lasting red nail polish',
      expiryDate: '2026-03-31',
      lastRestocked: '2024-01-12',
      status: 'active',
      location: 'Nail Station - Cabinet 1',
      barcode: '123456789015',
      usageRate: 0.8,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-12'
    },
    {
      id: 5,
      name: 'Styling Gel',
      category: 'Styling Products',
      brand: 'StyleMax',
      sku: 'SM-GEL-001',
      currentStock: 3,
      minStockLevel: 6,
      maxStockLevel: 20,
      unitPrice: 1659,
      supplier: 'Style Products Co.',
      description: 'Strong hold styling gel',
      expiryDate: '2024-07-25', // Expiring very soon
      lastRestocked: '2023-12-20',
      status: 'active',
      location: 'Styling Station - Shelf 2',
      barcode: '123456789016',
      usageRate: 1.2,
      createdAt: '2024-01-01',
      updatedAt: '2023-12-20'
    },
    {
      id: 6,
      name: 'Face Mask - Hydrating',
      category: 'Skincare',
      brand: 'SkinCare Pro',
      sku: 'SCP-HM-001',
      currentStock: 20,
      minStockLevel: 15,
      maxStockLevel: 50,
      unitPrice: 32.99,
      supplier: 'Skincare Solutions',
      description: 'Hydrating face mask for all skin types',
      expiryDate: '2025-08-31',
      lastRestocked: '2024-01-18',
      status: 'active',
      location: 'Facial Room - Cabinet A',
      barcode: '123456789017',
      usageRate: 3.0,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-18'
    }
  ]);

  const [stockMovements, setStockMovements] = useState([
    {
      id: 1,
      productId: 1,
      type: 'restock',
      quantity: 10,
      reason: 'Regular restock',
      date: '2024-01-15',
      performedBy: 'Admin',
      notes: 'Received from Beauty Supply Co.'
    },
    {
      id: 2,
      productId: 2,
      type: 'usage',
      quantity: -2,
      reason: 'Service usage',
      date: '2024-01-16',
      performedBy: 'Emma Wilson',
      notes: 'Used for hair treatment service'
    }
  ]);

  const [suppliers, setSuppliers] = useState([
    {
      id: 1,
      name: 'Beauty Supply Co.',
      contact: 'John Smith',
      email: '<EMAIL>',
      phone: '(*************',
      address: '123 Beauty Ave, City, State 12345'
    },
    {
      id: 2,
      name: 'Professional Color Inc.',
      contact: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '(*************',
      address: '456 Color St, City, State 12345'
    }
  ]);

  // Load data from localStorage on mount
  useEffect(() => {
    const savedProducts = localStorage.getItem('salon_inventory_products');
    const savedMovements = localStorage.getItem('salon_inventory_movements');
    const savedSuppliers = localStorage.getItem('salon_inventory_suppliers');

    if (savedProducts) {
      setProducts(JSON.parse(savedProducts));
    }
    if (savedMovements) {
      setStockMovements(JSON.parse(savedMovements));
    }
    if (savedSuppliers) {
      setSuppliers(JSON.parse(savedSuppliers));
    }
  }, []);

  // Save to localStorage whenever data changes
  useEffect(() => {
    localStorage.setItem('salon_inventory_products', JSON.stringify(products));
  }, [products]);

  useEffect(() => {
    localStorage.setItem('salon_inventory_movements', JSON.stringify(stockMovements));
  }, [stockMovements]);

  useEffect(() => {
    localStorage.setItem('salon_inventory_suppliers', JSON.stringify(suppliers));
  }, [suppliers]);

  // Helper functions
  const getProductById = (id) => {
    return products.find(product => product.id === id);
  };

  const getLowStockProducts = () => {
    return products.filter(product => 
      product.currentStock <= product.minStockLevel && product.status === 'active'
    );
  };

  const getOutOfStockProducts = () => {
    return products.filter(product => 
      product.currentStock === 0 && product.status === 'active'
    );
  };

  const getExpiringProducts = (daysAhead = 30) => {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + daysAhead);
    
    return products.filter(product => {
      if (!product.expiryDate) return false;
      const expiryDate = new Date(product.expiryDate);
      return expiryDate <= futureDate && product.status === 'active';
    });
  };

  const getTotalInventoryValue = () => {
    return products.reduce((total, product) => {
      return total + (product.currentStock * product.unitPrice);
    }, 0);
  };

  const getCategories = () => {
    return [...new Set(products.map(product => product.category))];
  };

  const getSuppliers = () => {
    return [...new Set(products.map(product => product.supplier))];
  };

  // CRUD operations for products
  const addProduct = (productData) => {
    const newProduct = {
      ...productData,
      id: Math.max(...products.map(p => p.id), 0) + 1,
      createdAt: new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString().split('T')[0]
    };
    setProducts(prev => [...prev, newProduct]);
    return newProduct;
  };

  const updateProduct = (id, updates) => {
    setProducts(prev => prev.map(product => 
      product.id === id 
        ? { ...product, ...updates, updatedAt: new Date().toISOString().split('T')[0] }
        : product
    ));
  };

  const deleteProduct = (id) => {
    setProducts(prev => prev.filter(product => product.id !== id));
    setStockMovements(prev => prev.filter(movement => movement.productId !== id));
  };

  // Stock movement operations
  const addStockMovement = (movementData) => {
    const newMovement = {
      ...movementData,
      id: Math.max(...stockMovements.map(m => m.id), 0) + 1,
      date: new Date().toISOString().split('T')[0]
    };

    setStockMovements(prev => [...prev, newMovement]);

    // Update product stock
    const currentProduct = getProductById(movementData.productId);
    if (currentProduct) {
      const newStock = Math.max(0, currentProduct.currentStock + movementData.quantity);
      const updateData = {
        currentStock: newStock
      };

      if (movementData.type === 'restock') {
        updateData.lastRestocked = newMovement.date;
      }

      updateProduct(movementData.productId, updateData);
    }

    return newMovement;
  };

  const value = {
    // State
    products,
    stockMovements,
    suppliers,
    
    // Helper functions
    getProductById,
    getLowStockProducts,
    getOutOfStockProducts,
    getExpiringProducts,
    getTotalInventoryValue,
    getCategories,
    getSuppliers,
    
    // CRUD operations
    addProduct,
    updateProduct,
    deleteProduct,
    addStockMovement,
    
    // Setters for direct manipulation if needed
    setProducts,
    setStockMovements,
    setSuppliers
  };

  return (
    <InventoryContext.Provider value={value}>
      {children}
    </InventoryContext.Provider>
  );
};
